// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    namespace Entitlements {
      /**
       * An active entitlement describes access to a feature for a customer.
       */
      interface ActiveEntitlement {
        /**
         * Unique identifier for the object.
         */
        id: string;

        /**
         * String representing the object's type. Objects of the same type share the same value.
         */
        object: 'entitlements.active_entitlement';

        /**
         * The [Feature](https://stripe.com/docs/api/entitlements/feature) that the customer is entitled to.
         */
        feature: string | Stripe.Entitlements.Feature;

        /**
         * Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
         */
        livemode: boolean;

        /**
         * A unique key you provide as your own system identifier. This may be up to 80 characters.
         */
        lookup_key: string;
      }
    }
  }
}
