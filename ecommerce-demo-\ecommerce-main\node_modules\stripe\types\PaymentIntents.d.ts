// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    /**
     * A PaymentIntent guides you through the process of collecting a payment from your customer.
     * We recommend that you create exactly one PaymentIntent for each order or
     * customer session in your system. You can reference the PaymentIntent later to
     * see the history of payment attempts for a particular session.
     *
     * A PaymentIntent transitions through
     * [multiple statuses](https://stripe.com/docs/payments/intents#intent-statuses)
     * throughout its lifetime as it interfaces with Stripe.js to perform
     * authentication flows and ultimately creates at most one successful charge.
     *
     * Related guide: [Payment Intents API](https://stripe.com/docs/payments/payment-intents)
     */
    interface PaymentIntent {
      /**
       * Unique identifier for the object.
       */
      id: string;

      /**
       * String representing the object's type. Objects of the same type share the same value.
       */
      object: 'payment_intent';

      /**
       * Amount intended to be collected by this PaymentIntent. A positive integer representing how much to charge in the [smallest currency unit](https://stripe.com/docs/currencies#zero-decimal) (e.g., 100 cents to charge $1.00 or 100 to charge ¥100, a zero-decimal currency). The minimum amount is $0.50 US or [equivalent in charge currency](https://stripe.com/docs/currencies#minimum-and-maximum-charge-amounts). The amount value supports up to eight digits (e.g., a value of 99999999 for a USD charge of $999,999.99).
       */
      amount: number;

      /**
       * Amount that can be captured from this PaymentIntent.
       */
      amount_capturable: number;

      amount_details?: PaymentIntent.AmountDetails;

      /**
       * Amount that this PaymentIntent collects.
       */
      amount_received: number;

      /**
       * ID of the Connect application that created the PaymentIntent.
       */
      application: string | Stripe.Application | null;

      /**
       * The amount of the application fee (if any) that will be requested to be applied to the payment and transferred to the application owner's Stripe account. The amount of the application fee collected will be capped at the total payment amount. For more information, see the PaymentIntents [use case for connected accounts](https://stripe.com/docs/payments/connected-accounts).
       */
      application_fee_amount: number | null;

      /**
       * Settings to configure compatible payment methods from the [Stripe Dashboard](https://dashboard.stripe.com/settings/payment_methods)
       */
      automatic_payment_methods: PaymentIntent.AutomaticPaymentMethods | null;

      /**
       * Populated when `status` is `canceled`, this is the time at which the PaymentIntent was canceled. Measured in seconds since the Unix epoch.
       */
      canceled_at: number | null;

      /**
       * Reason for cancellation of this PaymentIntent, either user-provided (`duplicate`, `fraudulent`, `requested_by_customer`, or `abandoned`) or generated by Stripe internally (`failed_invoice`, `void_invoice`, or `automatic`).
       */
      cancellation_reason: PaymentIntent.CancellationReason | null;

      /**
       * Controls when the funds will be captured from the customer's account.
       */
      capture_method: PaymentIntent.CaptureMethod;

      /**
       * The client secret of this PaymentIntent. Used for client-side retrieval using a publishable key.
       *
       * The client secret can be used to complete a payment from your frontend. It should not be stored, logged, or exposed to anyone other than the customer. Make sure that you have TLS enabled on any page that includes the client secret.
       *
       * Refer to our docs to [accept a payment](https://stripe.com/docs/payments/accept-a-payment?ui=elements) and learn about how `client_secret` should be handled.
       */
      client_secret: string | null;

      /**
       * Describes whether we can confirm this PaymentIntent automatically, or if it requires customer action to confirm the payment.
       */
      confirmation_method: PaymentIntent.ConfirmationMethod;

      /**
       * Time at which the object was created. Measured in seconds since the Unix epoch.
       */
      created: number;

      /**
       * Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
       */
      currency: string;

      /**
       * ID of the Customer this PaymentIntent belongs to, if one exists.
       *
       * Payment methods attached to other Customers cannot be used with this PaymentIntent.
       *
       * If [setup_future_usage](https://stripe.com/docs/api#payment_intent_object-setup_future_usage) is set and this PaymentIntent's payment method is not `card_present`, then the payment method attaches to the Customer after the PaymentIntent has been confirmed and any required actions from the user are complete. If the payment method is `card_present` and isn't a digital wallet, then a [generated_card](https://docs.stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card is created and attached to the Customer instead.
       */
      customer: string | Stripe.Customer | Stripe.DeletedCustomer | null;

      /**
       * An arbitrary string attached to the object. Often useful for displaying to users.
       */
      description: string | null;

      /**
       * ID of the invoice that created this PaymentIntent, if it exists.
       */
      invoice: string | Stripe.Invoice | null;

      /**
       * The payment error encountered in the previous PaymentIntent confirmation. It will be cleared if the PaymentIntent is later updated for any reason.
       */
      last_payment_error: PaymentIntent.LastPaymentError | null;

      /**
       * ID of the latest [Charge object](https://stripe.com/docs/api/charges) created by this PaymentIntent. This property is `null` until PaymentIntent confirmation is attempted.
       */
      latest_charge: string | Stripe.Charge | null;

      /**
       * Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
       */
      livemode: boolean;

      /**
       * Set of [key-value pairs](https://stripe.com/docs/api/metadata) that you can attach to an object. This can be useful for storing additional information about the object in a structured format. Learn more about [storing information in metadata](https://stripe.com/docs/payments/payment-intents/creating-payment-intents#storing-information-in-metadata).
       */
      metadata: Stripe.Metadata;

      /**
       * If present, this property tells you what actions you need to take in order for your customer to fulfill a payment using the provided source.
       */
      next_action: PaymentIntent.NextAction | null;

      /**
       * The account (if any) for which the funds of the PaymentIntent are intended. See the PaymentIntents [use case for connected accounts](https://stripe.com/docs/payments/connected-accounts) for details.
       */
      on_behalf_of: string | Stripe.Account | null;

      /**
       * ID of the payment method used in this PaymentIntent.
       */
      payment_method: string | Stripe.PaymentMethod | null;

      /**
       * Information about the [payment method configuration](https://stripe.com/docs/api/payment_method_configurations) used for this PaymentIntent.
       */
      payment_method_configuration_details: PaymentIntent.PaymentMethodConfigurationDetails | null;

      /**
       * Payment-method-specific configuration for this PaymentIntent.
       */
      payment_method_options: PaymentIntent.PaymentMethodOptions | null;

      /**
       * The list of payment method types (e.g. card) that this PaymentIntent is allowed to use.
       */
      payment_method_types: Array<string>;

      /**
       * If present, this property tells you about the processing state of the payment.
       */
      processing: PaymentIntent.Processing | null;

      /**
       * Email address that the receipt for the resulting payment will be sent to. If `receipt_email` is specified for a payment in live mode, a receipt will be sent regardless of your [email settings](https://dashboard.stripe.com/account/emails).
       */
      receipt_email: string | null;

      /**
       * ID of the review associated with this PaymentIntent, if any.
       */
      review: string | Stripe.Review | null;

      /**
       * Indicates that you intend to make future payments with this PaymentIntent's payment method.
       *
       * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
       *
       * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
       *
       * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
       */
      setup_future_usage: PaymentIntent.SetupFutureUsage | null;

      /**
       * Shipping information for this PaymentIntent.
       */
      shipping: PaymentIntent.Shipping | null;

      /**
       * This is a legacy field that will be removed in the future. It is the ID of the Source object that is associated with this PaymentIntent, if one was supplied.
       */
      source:
        | string
        | Stripe.CustomerSource
        | Stripe.DeletedCustomerSource
        | null;

      /**
       * Text that appears on the customer's statement as the statement descriptor for a non-card charge. This value overrides the account's default statement descriptor. For information about requirements, including the 22-character limit, see [the Statement Descriptor docs](https://docs.stripe.com/get-started/account/statement-descriptors).
       *
       * Setting this value for a card charge returns an error. For card charges, set the [statement_descriptor_suffix](https://docs.stripe.com/get-started/account/statement-descriptors#dynamic) instead.
       */
      statement_descriptor: string | null;

      /**
       * Provides information about a card charge. Concatenated to the account's [statement descriptor prefix](https://docs.stripe.com/get-started/account/statement-descriptors#static) to form the complete statement descriptor that appears on the customer's statement.
       */
      statement_descriptor_suffix: string | null;

      /**
       * Status of this PaymentIntent, one of `requires_payment_method`, `requires_confirmation`, `requires_action`, `processing`, `requires_capture`, `canceled`, or `succeeded`. Read more about each PaymentIntent [status](https://stripe.com/docs/payments/intents#intent-statuses).
       */
      status: PaymentIntent.Status;

      /**
       * The data that automatically creates a Transfer after the payment finalizes. Learn more about the [use case for connected accounts](https://stripe.com/docs/payments/connected-accounts).
       */
      transfer_data: PaymentIntent.TransferData | null;

      /**
       * A string that identifies the resulting payment as part of a group. Learn more about the [use case for connected accounts](https://stripe.com/docs/connect/separate-charges-and-transfers).
       */
      transfer_group: string | null;
    }

    namespace PaymentIntent {
      interface AmountDetails {
        tip?: AmountDetails.Tip;
      }

      namespace AmountDetails {
        interface Tip {
          /**
           * Portion of the amount that corresponds to a tip.
           */
          amount?: number;
        }
      }

      interface AutomaticPaymentMethods {
        /**
         * Controls whether this PaymentIntent will accept redirect-based payment methods.
         *
         * Redirect-based payment methods may require your customer to be redirected to a payment method's app or site for authentication or additional steps. To [confirm](https://stripe.com/docs/api/payment_intents/confirm) this PaymentIntent, you may be required to provide a `return_url` to redirect customers back to your site after they authenticate or complete the payment.
         */
        allow_redirects?: AutomaticPaymentMethods.AllowRedirects;

        /**
         * Automatically calculates compatible payment methods
         */
        enabled: boolean;
      }

      namespace AutomaticPaymentMethods {
        type AllowRedirects = 'always' | 'never';
      }

      type CancellationReason =
        | 'abandoned'
        | 'automatic'
        | 'duplicate'
        | 'failed_invoice'
        | 'fraudulent'
        | 'requested_by_customer'
        | 'void_invoice';

      type CaptureMethod = 'automatic' | 'automatic_async' | 'manual';

      type ConfirmationMethod = 'automatic' | 'manual';

      interface LastPaymentError {
        /**
         * For card errors resulting from a card issuer decline, a short string indicating [how to proceed with an error](https://stripe.com/docs/declines#retrying-issuer-declines) if they provide one.
         */
        advice_code?: string;

        /**
         * For card errors, the ID of the failed charge.
         */
        charge?: string;

        /**
         * For some errors that could be handled programmatically, a short string indicating the [error code](https://stripe.com/docs/error-codes) reported.
         */
        code?: LastPaymentError.Code;

        /**
         * For card errors resulting from a card issuer decline, a short string indicating the [card issuer's reason for the decline](https://stripe.com/docs/declines#issuer-declines) if they provide one.
         */
        decline_code?: string;

        /**
         * A URL to more information about the [error code](https://stripe.com/docs/error-codes) reported.
         */
        doc_url?: string;

        /**
         * A human-readable message providing more details about the error. For card errors, these messages can be shown to your users.
         */
        message?: string;

        /**
         * For card errors resulting from a card issuer decline, a 2 digit code which indicates the advice given to merchant by the card network on how to proceed with an error.
         */
        network_advice_code?: string;

        /**
         * For card errors resulting from a card issuer decline, a brand specific 2, 3, or 4 digit code which indicates the reason the authorization failed.
         */
        network_decline_code?: string;

        /**
         * If the error is parameter-specific, the parameter related to the error. For example, you can use this to display a message near the correct form field.
         */
        param?: string;

        /**
         * A PaymentIntent guides you through the process of collecting a payment from your customer.
         * We recommend that you create exactly one PaymentIntent for each order or
         * customer session in your system. You can reference the PaymentIntent later to
         * see the history of payment attempts for a particular session.
         *
         * A PaymentIntent transitions through
         * [multiple statuses](https://stripe.com/docs/payments/intents#intent-statuses)
         * throughout its lifetime as it interfaces with Stripe.js to perform
         * authentication flows and ultimately creates at most one successful charge.
         *
         * Related guide: [Payment Intents API](https://stripe.com/docs/payments/payment-intents)
         */
        payment_intent?: Stripe.PaymentIntent;

        /**
         * PaymentMethod objects represent your customer's payment instruments.
         * You can use them with [PaymentIntents](https://stripe.com/docs/payments/payment-intents) to collect payments or save them to
         * Customer objects to store instrument details for future payments.
         *
         * Related guides: [Payment Methods](https://stripe.com/docs/payments/payment-methods) and [More Payment Scenarios](https://stripe.com/docs/payments/more-payment-scenarios).
         */
        payment_method?: Stripe.PaymentMethod;

        /**
         * If the error is specific to the type of payment method, the payment method type that had a problem. This field is only populated for invoice-related errors.
         */
        payment_method_type?: string;

        /**
         * A URL to the request log entry in your dashboard.
         */
        request_log_url?: string;

        /**
         * A SetupIntent guides you through the process of setting up and saving a customer's payment credentials for future payments.
         * For example, you can use a SetupIntent to set up and save your customer's card without immediately collecting a payment.
         * Later, you can use [PaymentIntents](https://stripe.com/docs/api#payment_intents) to drive the payment flow.
         *
         * Create a SetupIntent when you're ready to collect your customer's payment credentials.
         * Don't maintain long-lived, unconfirmed SetupIntents because they might not be valid.
         * The SetupIntent transitions through multiple [statuses](https://docs.stripe.com/payments/intents#intent-statuses) as it guides
         * you through the setup process.
         *
         * Successful SetupIntents result in payment credentials that are optimized for future payments.
         * For example, cardholders in [certain regions](https://stripe.com/guides/strong-customer-authentication) might need to be run through
         * [Strong Customer Authentication](https://docs.stripe.com/strong-customer-authentication) during payment method collection
         * to streamline later [off-session payments](https://docs.stripe.com/payments/setup-intents).
         * If you use the SetupIntent with a [Customer](https://stripe.com/docs/api#setup_intent_object-customer),
         * it automatically attaches the resulting payment method to that Customer after successful setup.
         * We recommend using SetupIntents or [setup_future_usage](https://stripe.com/docs/api#payment_intent_object-setup_future_usage) on
         * PaymentIntents to save payment methods to prevent saving invalid or unoptimized payment methods.
         *
         * By using SetupIntents, you can reduce friction for your customers, even as regulations change over time.
         *
         * Related guide: [Setup Intents API](https://docs.stripe.com/payments/setup-intents)
         */
        setup_intent?: Stripe.SetupIntent;

        source?: Stripe.CustomerSource;

        /**
         * The type of error returned. One of `api_error`, `card_error`, `idempotency_error`, or `invalid_request_error`
         */
        type: LastPaymentError.Type;
      }

      namespace LastPaymentError {
        type Code =
          | 'account_closed'
          | 'account_country_invalid_address'
          | 'account_error_country_change_requires_additional_steps'
          | 'account_information_mismatch'
          | 'account_invalid'
          | 'account_number_invalid'
          | 'acss_debit_session_incomplete'
          | 'alipay_upgrade_required'
          | 'amount_too_large'
          | 'amount_too_small'
          | 'api_key_expired'
          | 'application_fees_not_allowed'
          | 'authentication_required'
          | 'balance_insufficient'
          | 'balance_invalid_parameter'
          | 'bank_account_bad_routing_numbers'
          | 'bank_account_declined'
          | 'bank_account_exists'
          | 'bank_account_restricted'
          | 'bank_account_unusable'
          | 'bank_account_unverified'
          | 'bank_account_verification_failed'
          | 'billing_invalid_mandate'
          | 'bitcoin_upgrade_required'
          | 'capture_charge_authorization_expired'
          | 'capture_unauthorized_payment'
          | 'card_decline_rate_limit_exceeded'
          | 'card_declined'
          | 'cardholder_phone_number_required'
          | 'charge_already_captured'
          | 'charge_already_refunded'
          | 'charge_disputed'
          | 'charge_exceeds_source_limit'
          | 'charge_exceeds_transaction_limit'
          | 'charge_expired_for_capture'
          | 'charge_invalid_parameter'
          | 'charge_not_refundable'
          | 'clearing_code_unsupported'
          | 'country_code_invalid'
          | 'country_unsupported'
          | 'coupon_expired'
          | 'customer_max_payment_methods'
          | 'customer_max_subscriptions'
          | 'customer_tax_location_invalid'
          | 'debit_not_authorized'
          | 'email_invalid'
          | 'expired_card'
          | 'financial_connections_account_inactive'
          | 'financial_connections_no_successful_transaction_refresh'
          | 'forwarding_api_inactive'
          | 'forwarding_api_invalid_parameter'
          | 'forwarding_api_upstream_connection_error'
          | 'forwarding_api_upstream_connection_timeout'
          | 'idempotency_key_in_use'
          | 'incorrect_address'
          | 'incorrect_cvc'
          | 'incorrect_number'
          | 'incorrect_zip'
          | 'instant_payouts_config_disabled'
          | 'instant_payouts_currency_disabled'
          | 'instant_payouts_limit_exceeded'
          | 'instant_payouts_unsupported'
          | 'insufficient_funds'
          | 'intent_invalid_state'
          | 'intent_verification_method_missing'
          | 'invalid_card_type'
          | 'invalid_characters'
          | 'invalid_charge_amount'
          | 'invalid_cvc'
          | 'invalid_expiry_month'
          | 'invalid_expiry_year'
          | 'invalid_mandate_reference_prefix_format'
          | 'invalid_number'
          | 'invalid_source_usage'
          | 'invalid_tax_location'
          | 'invoice_no_customer_line_items'
          | 'invoice_no_payment_method_types'
          | 'invoice_no_subscription_line_items'
          | 'invoice_not_editable'
          | 'invoice_on_behalf_of_not_editable'
          | 'invoice_payment_intent_requires_action'
          | 'invoice_upcoming_none'
          | 'livemode_mismatch'
          | 'lock_timeout'
          | 'missing'
          | 'no_account'
          | 'not_allowed_on_standard_account'
          | 'out_of_inventory'
          | 'ownership_declaration_not_allowed'
          | 'parameter_invalid_empty'
          | 'parameter_invalid_integer'
          | 'parameter_invalid_string_blank'
          | 'parameter_invalid_string_empty'
          | 'parameter_missing'
          | 'parameter_unknown'
          | 'parameters_exclusive'
          | 'payment_intent_action_required'
          | 'payment_intent_authentication_failure'
          | 'payment_intent_incompatible_payment_method'
          | 'payment_intent_invalid_parameter'
          | 'payment_intent_konbini_rejected_confirmation_number'
          | 'payment_intent_mandate_invalid'
          | 'payment_intent_payment_attempt_expired'
          | 'payment_intent_payment_attempt_failed'
          | 'payment_intent_unexpected_state'
          | 'payment_method_bank_account_already_verified'
          | 'payment_method_bank_account_blocked'
          | 'payment_method_billing_details_address_missing'
          | 'payment_method_configuration_failures'
          | 'payment_method_currency_mismatch'
          | 'payment_method_customer_decline'
          | 'payment_method_invalid_parameter'
          | 'payment_method_invalid_parameter_testmode'
          | 'payment_method_microdeposit_failed'
          | 'payment_method_microdeposit_verification_amounts_invalid'
          | 'payment_method_microdeposit_verification_amounts_mismatch'
          | 'payment_method_microdeposit_verification_attempts_exceeded'
          | 'payment_method_microdeposit_verification_descriptor_code_mismatch'
          | 'payment_method_microdeposit_verification_timeout'
          | 'payment_method_not_available'
          | 'payment_method_provider_decline'
          | 'payment_method_provider_timeout'
          | 'payment_method_unactivated'
          | 'payment_method_unexpected_state'
          | 'payment_method_unsupported_type'
          | 'payout_reconciliation_not_ready'
          | 'payouts_limit_exceeded'
          | 'payouts_not_allowed'
          | 'platform_account_required'
          | 'platform_api_key_expired'
          | 'postal_code_invalid'
          | 'processing_error'
          | 'product_inactive'
          | 'progressive_onboarding_limit_exceeded'
          | 'rate_limit'
          | 'refer_to_customer'
          | 'refund_disputed_payment'
          | 'resource_already_exists'
          | 'resource_missing'
          | 'return_intent_already_processed'
          | 'routing_number_invalid'
          | 'secret_key_required'
          | 'sepa_unsupported_account'
          | 'setup_attempt_failed'
          | 'setup_intent_authentication_failure'
          | 'setup_intent_invalid_parameter'
          | 'setup_intent_mandate_invalid'
          | 'setup_intent_setup_attempt_expired'
          | 'setup_intent_unexpected_state'
          | 'shipping_address_invalid'
          | 'shipping_calculation_failed'
          | 'sku_inactive'
          | 'state_unsupported'
          | 'status_transition_invalid'
          | 'stripe_tax_inactive'
          | 'tax_id_invalid'
          | 'taxes_calculation_failed'
          | 'terminal_location_country_unsupported'
          | 'terminal_reader_busy'
          | 'terminal_reader_hardware_fault'
          | 'terminal_reader_invalid_location_for_activation'
          | 'terminal_reader_invalid_location_for_payment'
          | 'terminal_reader_offline'
          | 'terminal_reader_timeout'
          | 'testmode_charges_only'
          | 'tls_version_unsupported'
          | 'token_already_used'
          | 'token_card_network_invalid'
          | 'token_in_use'
          | 'transfer_source_balance_parameters_mismatch'
          | 'transfers_not_allowed'
          | 'url_invalid';

        type Type =
          | 'api_error'
          | 'card_error'
          | 'idempotency_error'
          | 'invalid_request_error';
      }

      interface NextAction {
        alipay_handle_redirect?: NextAction.AlipayHandleRedirect;

        boleto_display_details?: NextAction.BoletoDisplayDetails;

        card_await_notification?: NextAction.CardAwaitNotification;

        cashapp_handle_redirect_or_display_qr_code?: NextAction.CashappHandleRedirectOrDisplayQrCode;

        display_bank_transfer_instructions?: NextAction.DisplayBankTransferInstructions;

        konbini_display_details?: NextAction.KonbiniDisplayDetails;

        multibanco_display_details?: NextAction.MultibancoDisplayDetails;

        oxxo_display_details?: NextAction.OxxoDisplayDetails;

        paynow_display_qr_code?: NextAction.PaynowDisplayQrCode;

        pix_display_qr_code?: NextAction.PixDisplayQrCode;

        promptpay_display_qr_code?: NextAction.PromptpayDisplayQrCode;

        redirect_to_url?: NextAction.RedirectToUrl;

        swish_handle_redirect_or_display_qr_code?: NextAction.SwishHandleRedirectOrDisplayQrCode;

        /**
         * Type of the next action to perform, one of `redirect_to_url`, `use_stripe_sdk`, `alipay_handle_redirect`, `oxxo_display_details`, or `verify_with_microdeposits`.
         */
        type: string;

        /**
         * When confirming a PaymentIntent with Stripe.js, Stripe.js depends on the contents of this dictionary to invoke authentication flows. The shape of the contents is subject to change and is only intended to be used by Stripe.js.
         */
        use_stripe_sdk?: NextAction.UseStripeSdk;

        verify_with_microdeposits?: NextAction.VerifyWithMicrodeposits;

        wechat_pay_display_qr_code?: NextAction.WechatPayDisplayQrCode;

        wechat_pay_redirect_to_android_app?: NextAction.WechatPayRedirectToAndroidApp;

        wechat_pay_redirect_to_ios_app?: NextAction.WechatPayRedirectToIosApp;
      }

      namespace NextAction {
        interface AlipayHandleRedirect {
          /**
           * The native data to be used with Alipay SDK you must redirect your customer to in order to authenticate the payment in an Android App.
           */
          native_data: string | null;

          /**
           * The native URL you must redirect your customer to in order to authenticate the payment in an iOS App.
           */
          native_url: string | null;

          /**
           * If the customer does not exit their browser while authenticating, they will be redirected to this specified URL after completion.
           */
          return_url: string | null;

          /**
           * The URL you must redirect your customer to in order to authenticate the payment.
           */
          url: string | null;
        }

        interface BoletoDisplayDetails {
          /**
           * The timestamp after which the boleto expires.
           */
          expires_at: number | null;

          /**
           * The URL to the hosted boleto voucher page, which allows customers to view the boleto voucher.
           */
          hosted_voucher_url: string | null;

          /**
           * The boleto number.
           */
          number: string | null;

          /**
           * The URL to the downloadable boleto voucher PDF.
           */
          pdf: string | null;
        }

        interface CardAwaitNotification {
          /**
           * The time that payment will be attempted. If customer approval is required, they need to provide approval before this time.
           */
          charge_attempt_at: number | null;

          /**
           * For payments greater than INR 15000, the customer must provide explicit approval of the payment with their bank. For payments of lower amount, no customer action is required.
           */
          customer_approval_required: boolean | null;
        }

        interface CashappHandleRedirectOrDisplayQrCode {
          /**
           * The URL to the hosted Cash App Pay instructions page, which allows customers to view the QR code, and supports QR code refreshing on expiration.
           */
          hosted_instructions_url: string;

          /**
           * The url for mobile redirect based auth
           */
          mobile_auth_url: string;

          qr_code: CashappHandleRedirectOrDisplayQrCode.QrCode;
        }

        namespace CashappHandleRedirectOrDisplayQrCode {
          interface QrCode {
            /**
             * The date (unix timestamp) when the QR code expires.
             */
            expires_at: number;

            /**
             * The image_url_png string used to render QR code
             */
            image_url_png: string;

            /**
             * The image_url_svg string used to render QR code
             */
            image_url_svg: string;
          }
        }

        interface DisplayBankTransferInstructions {
          /**
           * The remaining amount that needs to be transferred to complete the payment.
           */
          amount_remaining: number | null;

          /**
           * Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).
           */
          currency: string | null;

          /**
           * A list of financial addresses that can be used to fund the customer balance
           */
          financial_addresses?: Array<
            DisplayBankTransferInstructions.FinancialAddress
          >;

          /**
           * A link to a hosted page that guides your customer through completing the transfer.
           */
          hosted_instructions_url: string | null;

          /**
           * A string identifying this payment. Instruct your customer to include this code in the reference or memo field of their bank transfer.
           */
          reference: string | null;

          /**
           * Type of bank transfer
           */
          type: DisplayBankTransferInstructions.Type;
        }

        namespace DisplayBankTransferInstructions {
          interface FinancialAddress {
            /**
             * ABA Records contain U.S. bank account details per the ABA format.
             */
            aba?: FinancialAddress.Aba;

            /**
             * Iban Records contain E.U. bank account details per the SEPA format.
             */
            iban?: FinancialAddress.Iban;

            /**
             * Sort Code Records contain U.K. bank account details per the sort code format.
             */
            sort_code?: FinancialAddress.SortCode;

            /**
             * SPEI Records contain Mexico bank account details per the SPEI format.
             */
            spei?: FinancialAddress.Spei;

            /**
             * The payment networks supported by this FinancialAddress
             */
            supported_networks?: Array<FinancialAddress.SupportedNetwork>;

            /**
             * SWIFT Records contain U.S. bank account details per the SWIFT format.
             */
            swift?: FinancialAddress.Swift;

            /**
             * The type of financial address
             */
            type: FinancialAddress.Type;

            /**
             * Zengin Records contain Japan bank account details per the Zengin format.
             */
            zengin?: FinancialAddress.Zengin;
          }

          namespace FinancialAddress {
            interface Aba {
              account_holder_address: Stripe.Address;

              /**
               * The account holder name
               */
              account_holder_name: string;

              /**
               * The ABA account number
               */
              account_number: string;

              /**
               * The account type
               */
              account_type: string;

              bank_address: Stripe.Address;

              /**
               * The bank name
               */
              bank_name: string;

              /**
               * The ABA routing number
               */
              routing_number: string;
            }

            interface Iban {
              account_holder_address: Stripe.Address;

              /**
               * The name of the person or business that owns the bank account
               */
              account_holder_name: string;

              bank_address: Stripe.Address;

              /**
               * The BIC/SWIFT code of the account.
               */
              bic: string;

              /**
               * Two-letter country code ([ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)).
               */
              country: string;

              /**
               * The IBAN of the account.
               */
              iban: string;
            }

            interface SortCode {
              account_holder_address: Stripe.Address;

              /**
               * The name of the person or business that owns the bank account
               */
              account_holder_name: string;

              /**
               * The account number
               */
              account_number: string;

              bank_address: Stripe.Address;

              /**
               * The six-digit sort code
               */
              sort_code: string;
            }

            interface Spei {
              account_holder_address: Stripe.Address;

              /**
               * The account holder name
               */
              account_holder_name: string;

              bank_address: Stripe.Address;

              /**
               * The three-digit bank code
               */
              bank_code: string;

              /**
               * The short banking institution name
               */
              bank_name: string;

              /**
               * The CLABE number
               */
              clabe: string;
            }

            type SupportedNetwork =
              | 'ach'
              | 'bacs'
              | 'domestic_wire_us'
              | 'fps'
              | 'sepa'
              | 'spei'
              | 'swift'
              | 'zengin';

            interface Swift {
              account_holder_address: Stripe.Address;

              /**
               * The account holder name
               */
              account_holder_name: string;

              /**
               * The account number
               */
              account_number: string;

              /**
               * The account type
               */
              account_type: string;

              bank_address: Stripe.Address;

              /**
               * The bank name
               */
              bank_name: string;

              /**
               * The SWIFT code
               */
              swift_code: string;
            }

            type Type =
              | 'aba'
              | 'iban'
              | 'sort_code'
              | 'spei'
              | 'swift'
              | 'zengin';

            interface Zengin {
              account_holder_address: Stripe.Address;

              /**
               * The account holder name
               */
              account_holder_name: string | null;

              /**
               * The account number
               */
              account_number: string | null;

              /**
               * The bank account type. In Japan, this can only be `futsu` or `toza`.
               */
              account_type: string | null;

              bank_address: Stripe.Address;

              /**
               * The bank code of the account
               */
              bank_code: string | null;

              /**
               * The bank name of the account
               */
              bank_name: string | null;

              /**
               * The branch code of the account
               */
              branch_code: string | null;

              /**
               * The branch name of the account
               */
              branch_name: string | null;
            }
          }

          type Type =
            | 'eu_bank_transfer'
            | 'gb_bank_transfer'
            | 'jp_bank_transfer'
            | 'mx_bank_transfer'
            | 'us_bank_transfer';
        }

        interface KonbiniDisplayDetails {
          /**
           * The timestamp at which the pending Konbini payment expires.
           */
          expires_at: number;

          /**
           * The URL for the Konbini payment instructions page, which allows customers to view and print a Konbini voucher.
           */
          hosted_voucher_url: string | null;

          stores: KonbiniDisplayDetails.Stores;
        }

        namespace KonbiniDisplayDetails {
          interface Stores {
            /**
             * FamilyMart instruction details.
             */
            familymart: Stores.Familymart | null;

            /**
             * Lawson instruction details.
             */
            lawson: Stores.Lawson | null;

            /**
             * Ministop instruction details.
             */
            ministop: Stores.Ministop | null;

            /**
             * Seicomart instruction details.
             */
            seicomart: Stores.Seicomart | null;
          }

          namespace Stores {
            interface Familymart {
              /**
               * The confirmation number.
               */
              confirmation_number?: string;

              /**
               * The payment code.
               */
              payment_code: string;
            }

            interface Lawson {
              /**
               * The confirmation number.
               */
              confirmation_number?: string;

              /**
               * The payment code.
               */
              payment_code: string;
            }

            interface Ministop {
              /**
               * The confirmation number.
               */
              confirmation_number?: string;

              /**
               * The payment code.
               */
              payment_code: string;
            }

            interface Seicomart {
              /**
               * The confirmation number.
               */
              confirmation_number?: string;

              /**
               * The payment code.
               */
              payment_code: string;
            }
          }
        }

        interface MultibancoDisplayDetails {
          /**
           * Entity number associated with this Multibanco payment.
           */
          entity: string | null;

          /**
           * The timestamp at which the Multibanco voucher expires.
           */
          expires_at: number | null;

          /**
           * The URL for the hosted Multibanco voucher page, which allows customers to view a Multibanco voucher.
           */
          hosted_voucher_url: string | null;

          /**
           * Reference number associated with this Multibanco payment.
           */
          reference: string | null;
        }

        interface OxxoDisplayDetails {
          /**
           * The timestamp after which the OXXO voucher expires.
           */
          expires_after: number | null;

          /**
           * The URL for the hosted OXXO voucher page, which allows customers to view and print an OXXO voucher.
           */
          hosted_voucher_url: string | null;

          /**
           * OXXO reference number.
           */
          number: string | null;
        }

        interface PaynowDisplayQrCode {
          /**
           * The raw data string used to generate QR code, it should be used together with QR code library.
           */
          data: string;

          /**
           * The URL to the hosted PayNow instructions page, which allows customers to view the PayNow QR code.
           */
          hosted_instructions_url: string | null;

          /**
           * The image_url_png string used to render QR code
           */
          image_url_png: string;

          /**
           * The image_url_svg string used to render QR code
           */
          image_url_svg: string;
        }

        interface PixDisplayQrCode {
          /**
           * The raw data string used to generate QR code, it should be used together with QR code library.
           */
          data?: string;

          /**
           * The date (unix timestamp) when the PIX expires.
           */
          expires_at?: number;

          /**
           * The URL to the hosted pix instructions page, which allows customers to view the pix QR code.
           */
          hosted_instructions_url?: string;

          /**
           * The image_url_png string used to render png QR code
           */
          image_url_png?: string;

          /**
           * The image_url_svg string used to render svg QR code
           */
          image_url_svg?: string;
        }

        interface PromptpayDisplayQrCode {
          /**
           * The raw data string used to generate QR code, it should be used together with QR code library.
           */
          data: string;

          /**
           * The URL to the hosted PromptPay instructions page, which allows customers to view the PromptPay QR code.
           */
          hosted_instructions_url: string;

          /**
           * The PNG path used to render the QR code, can be used as the source in an HTML img tag
           */
          image_url_png: string;

          /**
           * The SVG path used to render the QR code, can be used as the source in an HTML img tag
           */
          image_url_svg: string;
        }

        interface RedirectToUrl {
          /**
           * If the customer does not exit their browser while authenticating, they will be redirected to this specified URL after completion.
           */
          return_url: string | null;

          /**
           * The URL you must redirect your customer to in order to authenticate the payment.
           */
          url: string | null;
        }

        interface SwishHandleRedirectOrDisplayQrCode {
          /**
           * The URL to the hosted Swish instructions page, which allows customers to view the QR code.
           */
          hosted_instructions_url: string;

          /**
           * The url for mobile redirect based auth (for internal use only and not typically available in standard API requests).
           */
          mobile_auth_url: string;

          qr_code: SwishHandleRedirectOrDisplayQrCode.QrCode;
        }

        namespace SwishHandleRedirectOrDisplayQrCode {
          interface QrCode {
            /**
             * The raw data string used to generate QR code, it should be used together with QR code library.
             */
            data: string;

            /**
             * The image_url_png string used to render QR code
             */
            image_url_png: string;

            /**
             * The image_url_svg string used to render QR code
             */
            image_url_svg: string;
          }
        }

        interface UseStripeSdk {}

        interface VerifyWithMicrodeposits {
          /**
           * The timestamp when the microdeposits are expected to land.
           */
          arrival_date: number;

          /**
           * The URL for the hosted verification page, which allows customers to verify their bank account.
           */
          hosted_verification_url: string;

          /**
           * The type of the microdeposit sent to the customer. Used to distinguish between different verification methods.
           */
          microdeposit_type: VerifyWithMicrodeposits.MicrodepositType | null;
        }

        namespace VerifyWithMicrodeposits {
          type MicrodepositType = 'amounts' | 'descriptor_code';
        }

        interface WechatPayDisplayQrCode {
          /**
           * The data being used to generate QR code
           */
          data: string;

          /**
           * The URL to the hosted WeChat Pay instructions page, which allows customers to view the WeChat Pay QR code.
           */
          hosted_instructions_url: string;

          /**
           * The base64 image data for a pre-generated QR code
           */
          image_data_url: string;

          /**
           * The image_url_png string used to render QR code
           */
          image_url_png: string;

          /**
           * The image_url_svg string used to render QR code
           */
          image_url_svg: string;
        }

        interface WechatPayRedirectToAndroidApp {
          /**
           * app_id is the APP ID registered on WeChat open platform
           */
          app_id: string;

          /**
           * nonce_str is a random string
           */
          nonce_str: string;

          /**
           * package is static value
           */
          package: string;

          /**
           * an unique merchant ID assigned by WeChat Pay
           */
          partner_id: string;

          /**
           * an unique trading ID assigned by WeChat Pay
           */
          prepay_id: string;

          /**
           * A signature
           */
          sign: string;

          /**
           * Specifies the current time in epoch format
           */
          timestamp: string;
        }

        interface WechatPayRedirectToIosApp {
          /**
           * An universal link that redirect to WeChat Pay app
           */
          native_url: string;
        }
      }

      interface PaymentMethodConfigurationDetails {
        /**
         * ID of the payment method configuration used.
         */
        id: string;

        /**
         * ID of the parent payment method configuration used.
         */
        parent: string | null;
      }

      interface PaymentMethodOptions {
        acss_debit?: PaymentMethodOptions.AcssDebit;

        affirm?: PaymentMethodOptions.Affirm;

        afterpay_clearpay?: PaymentMethodOptions.AfterpayClearpay;

        alipay?: PaymentMethodOptions.Alipay;

        alma?: PaymentMethodOptions.Alma;

        amazon_pay?: PaymentMethodOptions.AmazonPay;

        au_becs_debit?: PaymentMethodOptions.AuBecsDebit;

        bacs_debit?: PaymentMethodOptions.BacsDebit;

        bancontact?: PaymentMethodOptions.Bancontact;

        blik?: PaymentMethodOptions.Blik;

        boleto?: PaymentMethodOptions.Boleto;

        card?: PaymentMethodOptions.Card;

        card_present?: PaymentMethodOptions.CardPresent;

        cashapp?: PaymentMethodOptions.Cashapp;

        customer_balance?: PaymentMethodOptions.CustomerBalance;

        eps?: PaymentMethodOptions.Eps;

        fpx?: PaymentMethodOptions.Fpx;

        giropay?: PaymentMethodOptions.Giropay;

        grabpay?: PaymentMethodOptions.Grabpay;

        ideal?: PaymentMethodOptions.Ideal;

        interac_present?: PaymentMethodOptions.InteracPresent;

        kakao_pay?: PaymentMethodOptions.KakaoPay;

        klarna?: PaymentMethodOptions.Klarna;

        konbini?: PaymentMethodOptions.Konbini;

        kr_card?: PaymentMethodOptions.KrCard;

        link?: PaymentMethodOptions.Link;

        mobilepay?: PaymentMethodOptions.Mobilepay;

        multibanco?: PaymentMethodOptions.Multibanco;

        naver_pay?: PaymentMethodOptions.NaverPay;

        oxxo?: PaymentMethodOptions.Oxxo;

        p24?: PaymentMethodOptions.P24;

        pay_by_bank?: PaymentMethodOptions.PayByBank;

        payco?: PaymentMethodOptions.Payco;

        paynow?: PaymentMethodOptions.Paynow;

        paypal?: PaymentMethodOptions.Paypal;

        pix?: PaymentMethodOptions.Pix;

        promptpay?: PaymentMethodOptions.Promptpay;

        revolut_pay?: PaymentMethodOptions.RevolutPay;

        samsung_pay?: PaymentMethodOptions.SamsungPay;

        sepa_debit?: PaymentMethodOptions.SepaDebit;

        sofort?: PaymentMethodOptions.Sofort;

        swish?: PaymentMethodOptions.Swish;

        twint?: PaymentMethodOptions.Twint;

        us_bank_account?: PaymentMethodOptions.UsBankAccount;

        wechat_pay?: PaymentMethodOptions.WechatPay;

        zip?: PaymentMethodOptions.Zip;
      }

      namespace PaymentMethodOptions {
        interface AcssDebit {
          mandate_options?: AcssDebit.MandateOptions;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: AcssDebit.SetupFutureUsage;

          /**
           * Controls when Stripe will attempt to debit the funds from the customer's account. The date must be a string in YYYY-MM-DD format. The date must be in the future and between 3 and 15 calendar days from now.
           */
          target_date?: string;

          /**
           * Bank account verification method.
           */
          verification_method?: AcssDebit.VerificationMethod;
        }

        namespace AcssDebit {
          interface MandateOptions {
            /**
             * A URL for custom mandate text
             */
            custom_mandate_url?: string;

            /**
             * Description of the interval. Only required if the 'payment_schedule' parameter is 'interval' or 'combined'.
             */
            interval_description: string | null;

            /**
             * Payment schedule for the mandate.
             */
            payment_schedule: MandateOptions.PaymentSchedule | null;

            /**
             * Transaction type of the mandate.
             */
            transaction_type: MandateOptions.TransactionType | null;
          }

          namespace MandateOptions {
            type PaymentSchedule = 'combined' | 'interval' | 'sporadic';

            type TransactionType = 'business' | 'personal';
          }

          type SetupFutureUsage = 'none' | 'off_session' | 'on_session';

          type VerificationMethod = 'automatic' | 'instant' | 'microdeposits';
        }

        interface Affirm {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Preferred language of the Affirm authorization page that the customer is redirected to.
           */
          preferred_locale?: string;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface AfterpayClearpay {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * An internal identifier or reference that this payment corresponds to. You must limit the identifier to 128 characters, and it can only contain letters, numbers, underscores, backslashes, and dashes.
           * This field differs from the statement descriptor and item name.
           */
          reference: string | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Alipay {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Alipay.SetupFutureUsage;
        }

        namespace Alipay {
          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface Alma {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';
        }

        interface AmazonPay {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: AmazonPay.SetupFutureUsage;
        }

        namespace AmazonPay {
          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface AuBecsDebit {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: AuBecsDebit.SetupFutureUsage;

          /**
           * Controls when Stripe will attempt to debit the funds from the customer's account. The date must be a string in YYYY-MM-DD format. The date must be in the future and between 3 and 15 calendar days from now.
           */
          target_date?: string;
        }

        namespace AuBecsDebit {
          type SetupFutureUsage = 'none' | 'off_session' | 'on_session';
        }

        interface BacsDebit {
          mandate_options?: BacsDebit.MandateOptions;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: BacsDebit.SetupFutureUsage;

          /**
           * Controls when Stripe will attempt to debit the funds from the customer's account. The date must be a string in YYYY-MM-DD format. The date must be in the future and between 3 and 15 calendar days from now.
           */
          target_date?: string;
        }

        namespace BacsDebit {
          interface MandateOptions {
            /**
             * Prefix used to generate the Mandate reference. Must be at most 12 characters long. Must consist of only uppercase letters, numbers, spaces, or the following special characters: '/', '_', '-', '&', '.'. Cannot begin with 'DDIC' or 'STRIPE'.
             */
            reference_prefix?: string;
          }

          type SetupFutureUsage = 'none' | 'off_session' | 'on_session';
        }

        interface Bancontact {
          /**
           * Preferred language of the Bancontact authorization page that the customer is redirected to.
           */
          preferred_language: Bancontact.PreferredLanguage;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Bancontact.SetupFutureUsage;
        }

        namespace Bancontact {
          type PreferredLanguage = 'de' | 'en' | 'fr' | 'nl';

          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface Blik {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Boleto {
          /**
           * The number of calendar days before a Boleto voucher expires. For example, if you create a Boleto voucher on Monday and you set expires_after_days to 2, the Boleto voucher will expire on Wednesday at 23:59 America/Sao_Paulo time.
           */
          expires_after_days: number;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Boleto.SetupFutureUsage;
        }

        namespace Boleto {
          type SetupFutureUsage = 'none' | 'off_session' | 'on_session';
        }

        interface Card {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Installment details for this payment (Mexico only).
           *
           * For more information, see the [installments integration guide](https://stripe.com/docs/payments/installments).
           */
          installments: Card.Installments | null;

          /**
           * Configuration options for setting up an eMandate for cards issued in India.
           */
          mandate_options: Card.MandateOptions | null;

          /**
           * Selected network to process this payment intent on. Depends on the available networks of the card attached to the payment intent. Can be only set confirm-time.
           */
          network: Card.Network | null;

          /**
           * Request ability to [capture beyond the standard authorization validity window](https://stripe.com/docs/payments/extended-authorization) for this PaymentIntent.
           */
          request_extended_authorization?: Card.RequestExtendedAuthorization;

          /**
           * Request ability to [increment the authorization](https://stripe.com/docs/payments/incremental-authorization) for this PaymentIntent.
           */
          request_incremental_authorization?: Card.RequestIncrementalAuthorization;

          /**
           * Request ability to make [multiple captures](https://stripe.com/docs/payments/multicapture) for this PaymentIntent.
           */
          request_multicapture?: Card.RequestMulticapture;

          /**
           * Request ability to [overcapture](https://stripe.com/docs/payments/overcapture) for this PaymentIntent.
           */
          request_overcapture?: Card.RequestOvercapture;

          /**
           * We strongly recommend that you rely on our SCA Engine to automatically prompt your customers for authentication based on risk level and [other requirements](https://stripe.com/docs/strong-customer-authentication). However, if you wish to request 3D Secure based on logic from your own fraud engine, provide this option. If not provided, this value defaults to `automatic`. Read our guide on [manually requesting 3D Secure](https://stripe.com/docs/payments/3d-secure/authentication-flow#manual-three-ds) for more information on how this configuration interacts with Radar and our SCA Engine.
           */
          request_three_d_secure: Card.RequestThreeDSecure | null;

          /**
           * When enabled, using a card that is attached to a customer will require the CVC to be provided again (i.e. using the cvc_token parameter).
           */
          require_cvc_recollection?: boolean;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Card.SetupFutureUsage;

          /**
           * Provides information about a card payment that customers see on their statements. Concatenated with the Kana prefix (shortened Kana descriptor) or Kana statement descriptor that's set on the account to form the complete statement descriptor. Maximum 22 characters. On card statements, the *concatenation* of both prefix and suffix (including separators) will appear truncated to 22 characters.
           */
          statement_descriptor_suffix_kana?: string;

          /**
           * Provides information about a card payment that customers see on their statements. Concatenated with the Kanji prefix (shortened Kanji descriptor) or Kanji statement descriptor that's set on the account to form the complete statement descriptor. Maximum 17 characters. On card statements, the *concatenation* of both prefix and suffix (including separators) will appear truncated to 17 characters.
           */
          statement_descriptor_suffix_kanji?: string;
        }

        namespace Card {
          interface Installments {
            /**
             * Installment plans that may be selected for this PaymentIntent.
             */
            available_plans: Array<Installments.AvailablePlan> | null;

            /**
             * Whether Installments are enabled for this PaymentIntent.
             */
            enabled: boolean;

            /**
             * Installment plan selected for this PaymentIntent.
             */
            plan: Installments.Plan | null;
          }

          namespace Installments {
            interface AvailablePlan {
              /**
               * For `fixed_count` installment plans, this is the number of installment payments your customer will make to their credit card.
               */
              count: number | null;

              /**
               * For `fixed_count` installment plans, this is the interval between installment payments your customer will make to their credit card.
               * One of `month`.
               */
              interval: 'month' | null;

              /**
               * Type of installment plan, one of `fixed_count`.
               */
              type: 'fixed_count';
            }

            interface Plan {
              /**
               * For `fixed_count` installment plans, this is the number of installment payments your customer will make to their credit card.
               */
              count: number | null;

              /**
               * For `fixed_count` installment plans, this is the interval between installment payments your customer will make to their credit card.
               * One of `month`.
               */
              interval: 'month' | null;

              /**
               * Type of installment plan, one of `fixed_count`.
               */
              type: 'fixed_count';
            }
          }

          interface MandateOptions {
            /**
             * Amount to be charged for future payments.
             */
            amount: number;

            /**
             * One of `fixed` or `maximum`. If `fixed`, the `amount` param refers to the exact amount to be charged in future payments. If `maximum`, the amount charged can be up to the value passed for the `amount` param.
             */
            amount_type: MandateOptions.AmountType;

            /**
             * A description of the mandate or subscription that is meant to be displayed to the customer.
             */
            description: string | null;

            /**
             * End date of the mandate or subscription. If not provided, the mandate will be active until canceled. If provided, end date should be after start date.
             */
            end_date: number | null;

            /**
             * Specifies payment frequency. One of `day`, `week`, `month`, `year`, or `sporadic`.
             */
            interval: MandateOptions.Interval;

            /**
             * The number of intervals between payments. For example, `interval=month` and `interval_count=3` indicates one payment every three months. Maximum of one year interval allowed (1 year, 12 months, or 52 weeks). This parameter is optional when `interval=sporadic`.
             */
            interval_count: number | null;

            /**
             * Unique identifier for the mandate or subscription.
             */
            reference: string;

            /**
             * Start date of the mandate or subscription. Start date should not be lesser than yesterday.
             */
            start_date: number;

            /**
             * Specifies the type of mandates supported. Possible values are `india`.
             */
            supported_types: Array<'india'> | null;
          }

          namespace MandateOptions {
            type AmountType = 'fixed' | 'maximum';

            type Interval = 'day' | 'month' | 'sporadic' | 'week' | 'year';
          }

          type Network =
            | 'amex'
            | 'cartes_bancaires'
            | 'diners'
            | 'discover'
            | 'eftpos_au'
            | 'girocard'
            | 'interac'
            | 'jcb'
            | 'link'
            | 'mastercard'
            | 'unionpay'
            | 'unknown'
            | 'visa';

          type RequestExtendedAuthorization = 'if_available' | 'never';

          type RequestIncrementalAuthorization = 'if_available' | 'never';

          type RequestMulticapture = 'if_available' | 'never';

          type RequestOvercapture = 'if_available' | 'never';

          type RequestThreeDSecure = 'any' | 'automatic' | 'challenge';

          type SetupFutureUsage = 'none' | 'off_session' | 'on_session';
        }

        interface CardPresent {
          /**
           * Request ability to capture this payment beyond the standard [authorization validity window](https://stripe.com/docs/terminal/features/extended-authorizations#authorization-validity)
           */
          request_extended_authorization: boolean | null;

          /**
           * Request ability to [increment](https://stripe.com/docs/terminal/features/incremental-authorizations) this PaymentIntent if the combination of MCC and card brand is eligible. Check [incremental_authorization_supported](https://stripe.com/docs/api/charges/object#charge_object-payment_method_details-card_present-incremental_authorization_supported) in the [Confirm](https://stripe.com/docs/api/payment_intents/confirm) response to verify support.
           */
          request_incremental_authorization_support: boolean | null;

          routing?: CardPresent.Routing;
        }

        namespace CardPresent {
          interface Routing {
            /**
             * Requested routing priority
             */
            requested_priority: Routing.RequestedPriority | null;
          }

          namespace Routing {
            type RequestedPriority = 'domestic' | 'international';
          }
        }

        interface Cashapp {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Cashapp.SetupFutureUsage;
        }

        namespace Cashapp {
          type SetupFutureUsage = 'none' | 'off_session' | 'on_session';
        }

        interface CustomerBalance {
          bank_transfer?: CustomerBalance.BankTransfer;

          /**
           * The funding method type to be used when there are not enough funds in the customer balance. Permitted values include: `bank_transfer`.
           */
          funding_type: 'bank_transfer' | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        namespace CustomerBalance {
          interface BankTransfer {
            eu_bank_transfer?: BankTransfer.EuBankTransfer;

            /**
             * List of address types that should be returned in the financial_addresses response. If not specified, all valid types will be returned.
             *
             * Permitted values include: `sort_code`, `zengin`, `iban`, or `spei`.
             */
            requested_address_types?: Array<BankTransfer.RequestedAddressType>;

            /**
             * The bank transfer type that this PaymentIntent is allowed to use for funding Permitted values include: `eu_bank_transfer`, `gb_bank_transfer`, `jp_bank_transfer`, `mx_bank_transfer`, or `us_bank_transfer`.
             */
            type: BankTransfer.Type | null;
          }

          namespace BankTransfer {
            interface EuBankTransfer {
              /**
               * The desired country code of the bank account information. Permitted values include: `BE`, `DE`, `ES`, `FR`, `IE`, or `NL`.
               */
              country: EuBankTransfer.Country;
            }

            namespace EuBankTransfer {
              type Country = 'BE' | 'DE' | 'ES' | 'FR' | 'IE' | 'NL';
            }

            type RequestedAddressType =
              | 'aba'
              | 'iban'
              | 'sepa'
              | 'sort_code'
              | 'spei'
              | 'swift'
              | 'zengin';

            type Type =
              | 'eu_bank_transfer'
              | 'gb_bank_transfer'
              | 'jp_bank_transfer'
              | 'mx_bank_transfer'
              | 'us_bank_transfer';
          }
        }

        interface Eps {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Fpx {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Giropay {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Grabpay {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Ideal {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Ideal.SetupFutureUsage;
        }

        namespace Ideal {
          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface InteracPresent {}

        interface KakaoPay {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: KakaoPay.SetupFutureUsage;
        }

        namespace KakaoPay {
          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface Klarna {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Preferred locale of the Klarna checkout page that the customer is redirected to.
           */
          preferred_locale: string | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Konbini {
          /**
           * An optional 10 to 11 digit numeric-only string determining the confirmation code at applicable convenience stores.
           */
          confirmation_number: string | null;

          /**
           * The number of calendar days (between 1 and 60) after which Konbini payment instructions will expire. For example, if a PaymentIntent is confirmed with Konbini and `expires_after_days` set to 2 on Monday JST, the instructions will expire on Wednesday 23:59:59 JST.
           */
          expires_after_days: number | null;

          /**
           * The timestamp at which the Konbini payment instructions will expire. Only one of `expires_after_days` or `expires_at` may be set.
           */
          expires_at: number | null;

          /**
           * A product descriptor of up to 22 characters, which will appear to customers at the convenience store.
           */
          product_description: string | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface KrCard {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: KrCard.SetupFutureUsage;
        }

        namespace KrCard {
          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface Link {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * [Deprecated] This is a legacy parameter that no longer has any function.
           * @deprecated
           */
          persistent_token: string | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Link.SetupFutureUsage;
        }

        namespace Link {
          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface Mobilepay {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Multibanco {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface NaverPay {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';
        }

        interface Oxxo {
          /**
           * The number of calendar days before an OXXO invoice expires. For example, if you create an OXXO invoice on Monday and you set expires_after_days to 2, the OXXO invoice will expire on Wednesday at 23:59 America/Mexico_City time.
           */
          expires_after_days: number;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface P24 {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface PayByBank {}

        interface Payco {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';
        }

        interface Paynow {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Paypal {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Preferred locale of the PayPal checkout page that the customer is redirected to.
           */
          preferred_locale: string | null;

          /**
           * A reference of the PayPal transaction visible to customer which is mapped to PayPal's invoice ID. This must be a globally unique ID if you have configured in your PayPal settings to block multiple payments per invoice ID.
           */
          reference: string | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Paypal.SetupFutureUsage;
        }

        namespace Paypal {
          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface Pix {
          /**
           * The number of seconds (between 10 and 1209600) after which Pix payment will expire.
           */
          expires_after_seconds: number | null;

          /**
           * The timestamp at which the Pix expires.
           */
          expires_at: number | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Promptpay {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface RevolutPay {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: RevolutPay.SetupFutureUsage;
        }

        namespace RevolutPay {
          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface SamsungPay {
          /**
           * Controls when the funds will be captured from the customer's account.
           */
          capture_method?: 'manual';
        }

        interface SepaDebit {
          mandate_options?: SepaDebit.MandateOptions;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: SepaDebit.SetupFutureUsage;

          /**
           * Controls when Stripe will attempt to debit the funds from the customer's account. The date must be a string in YYYY-MM-DD format. The date must be in the future and between 3 and 15 calendar days from now.
           */
          target_date?: string;
        }

        namespace SepaDebit {
          interface MandateOptions {
            /**
             * Prefix used to generate the Mandate reference. Must be at most 12 characters long. Must consist of only uppercase letters, numbers, spaces, or the following special characters: '/', '_', '-', '&', '.'. Cannot begin with 'STRIPE'.
             */
            reference_prefix?: string;
          }

          type SetupFutureUsage = 'none' | 'off_session' | 'on_session';
        }

        interface Sofort {
          /**
           * Preferred language of the SOFORT authorization page that the customer is redirected to.
           */
          preferred_language: Sofort.PreferredLanguage | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: Sofort.SetupFutureUsage;
        }

        namespace Sofort {
          type PreferredLanguage =
            | 'de'
            | 'en'
            | 'es'
            | 'fr'
            | 'it'
            | 'nl'
            | 'pl';

          type SetupFutureUsage = 'none' | 'off_session';
        }

        interface Swish {
          /**
           * A reference for this payment to be displayed in the Swish app.
           */
          reference: string | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface Twint {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        interface UsBankAccount {
          financial_connections?: UsBankAccount.FinancialConnections;

          mandate_options?: UsBankAccount.MandateOptions;

          /**
           * Preferred transaction settlement speed
           */
          preferred_settlement_speed?: UsBankAccount.PreferredSettlementSpeed;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: UsBankAccount.SetupFutureUsage;

          /**
           * Controls when Stripe will attempt to debit the funds from the customer's account. The date must be a string in YYYY-MM-DD format. The date must be in the future and between 3 and 15 calendar days from now.
           */
          target_date?: string;

          /**
           * Bank account verification method.
           */
          verification_method?: UsBankAccount.VerificationMethod;
        }

        namespace UsBankAccount {
          interface FinancialConnections {
            filters?: FinancialConnections.Filters;

            /**
             * The list of permissions to request. The `payment_method` permission must be included.
             */
            permissions?: Array<FinancialConnections.Permission>;

            /**
             * Data features requested to be retrieved upon account creation.
             */
            prefetch: Array<FinancialConnections.Prefetch> | null;

            /**
             * For webview integrations only. Upon completing OAuth login in the native browser, the user will be redirected to this URL to return to your app.
             */
            return_url?: string;
          }

          namespace FinancialConnections {
            interface Filters {
              /**
               * The account subcategories to use to filter for possible accounts to link. Valid subcategories are `checking` and `savings`.
               */
              account_subcategories?: Array<Filters.AccountSubcategory>;
            }

            namespace Filters {
              type AccountSubcategory = 'checking' | 'savings';
            }

            type Permission =
              | 'balances'
              | 'ownership'
              | 'payment_method'
              | 'transactions';

            type Prefetch = 'balances' | 'ownership' | 'transactions';
          }

          interface MandateOptions {
            /**
             * Mandate collection method
             */
            collection_method?: 'paper';
          }

          type PreferredSettlementSpeed = 'fastest' | 'standard';

          type SetupFutureUsage = 'none' | 'off_session' | 'on_session';

          type VerificationMethod = 'automatic' | 'instant' | 'microdeposits';
        }

        interface WechatPay {
          /**
           * The app ID registered with WeChat Pay. Only required when client is ios or android.
           */
          app_id: string | null;

          /**
           * The client type that the end customer will pay from
           */
          client: WechatPay.Client | null;

          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }

        namespace WechatPay {
          type Client = 'android' | 'ios' | 'web';
        }

        interface Zip {
          /**
           * Indicates that you intend to make future payments with this PaymentIntent's payment method.
           *
           * If you provide a Customer with the PaymentIntent, you can use this parameter to [attach the payment method](https://stripe.com/payments/save-during-payment) to the Customer after the PaymentIntent is confirmed and the customer completes any required actions. If you don't provide a Customer, you can still [attach](https://stripe.com/api/payment_methods/attach) the payment method to a Customer after the transaction completes.
           *
           * If the payment method is `card_present` and isn't a digital wallet, Stripe creates and attaches a [generated_card](https://stripe.com/api/charges/object#charge_object-payment_method_details-card_present-generated_card) payment method representing the card to the Customer instead.
           *
           * When processing card payments, Stripe uses `setup_future_usage` to help you comply with regional legislation and network rules, such as [SCA](https://stripe.com/strong-customer-authentication).
           */
          setup_future_usage?: 'none';
        }
      }

      interface Processing {
        card?: Processing.Card;

        /**
         * Type of the payment method for which payment is in `processing` state, one of `card`.
         */
        type: 'card';
      }

      namespace Processing {
        interface Card {
          customer_notification?: Card.CustomerNotification;
        }

        namespace Card {
          interface CustomerNotification {
            /**
             * Whether customer approval has been requested for this payment. For payments greater than INR 15000 or mandate amount, the customer must provide explicit approval of the payment with their bank.
             */
            approval_requested: boolean | null;

            /**
             * If customer approval is required, they need to provide approval before this time.
             */
            completes_at: number | null;
          }
        }
      }

      type SetupFutureUsage = 'off_session' | 'on_session';

      interface Shipping {
        address?: Stripe.Address;

        /**
         * The delivery service that shipped a physical product, such as Fedex, UPS, USPS, etc.
         */
        carrier?: string | null;

        /**
         * Recipient name.
         */
        name?: string;

        /**
         * Recipient phone (including extension).
         */
        phone?: string | null;

        /**
         * The tracking number for a physical product, obtained from the delivery service. If multiple tracking numbers were generated for this purchase, please separate them with commas.
         */
        tracking_number?: string | null;
      }

      type Status =
        | 'canceled'
        | 'processing'
        | 'requires_action'
        | 'requires_capture'
        | 'requires_confirmation'
        | 'requires_payment_method'
        | 'succeeded';

      interface TransferData {
        /**
         * The amount transferred to the destination account. This transfer will occur automatically after the payment succeeds. If no amount is specified, by default the entire payment amount is transferred to the destination account.
         *  The amount must be less than or equal to the [amount](https://stripe.com/docs/api/payment_intents/object#payment_intent_object-amount), and must be a positive integer
         *  representing how much to transfer in the smallest currency unit (e.g., 100 cents to charge $1.00).
         */
        amount?: number;

        /**
         * The account (if any) that the payment is attributed to for tax reporting, and where funds from the payment are transferred to after payment success.
         */
        destination: string | Stripe.Account;
      }
    }
  }
}
