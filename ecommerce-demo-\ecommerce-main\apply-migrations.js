import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

// Get the directory path of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get database connection string from environment
const dbUrl = process.env.DATABASE_URL;

if (!dbUrl) {
  console.error('DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Prepare SQL client
const sql = neon(dbUrl);

const migrationsDir = path.join(__dirname, 'migrations');

async function runMigration() {
  try {
    // Read all migration files
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Ensure they're run in order

    console.log(`Found ${migrationFiles.length} migration files`);

    for (const file of migrationFiles) {
      console.log(`Running migration: ${file}`);
      const filePath = path.join(migrationsDir, file);
      const sqlContent = fs.readFileSync(filePath, 'utf8');
      
      // Split the SQL content by statement-breakpoint to execute each statement separately
      const statements = sqlContent.split('-->');
      
      for (const statement of statements) {
        const trimmedStatement = statement.trim();
        if (trimmedStatement && !trimmedStatement.startsWith('statement-breakpoint')) {
          try {
            await sql.query(trimmedStatement);
            console.log(`Successfully executed statement from ${file}`);
          } catch (error) {
            // If the table already exists or other non-critical error, continue
            console.warn(`Warning executing statement: ${error.message}`);
          }
        }
      }
      
      console.log(`Migration ${file} completed`);
    }

    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await sql.end();
  }
}

runMigration();
