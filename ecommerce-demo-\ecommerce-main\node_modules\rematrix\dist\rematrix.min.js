/*! @license Rematrix v0.3.0 (MIT) Copyright 2018 <PERSON>. */
var Rematrix=function(r){"use strict";function t(r){if(r.constructor!==Array)throw new TypeError("Expected array.");if(16===r.length)return r;if(6===r.length){var t=n();return t[0]=r[0],t[1]=r[1],t[4]=r[2],t[5]=r[3],t[12]=r[4],t[13]=r[5],t}throw new RangeError("Expected array with either 6 or 16 values.")}function n(){for(var r=[],t=0;t<16;t++)t%5==0?r.push(1):r.push(0);return r}function a(r){var n=t(r),a=n[0]*n[5]-n[4]*n[1],e=n[0]*n[6]-n[4]*n[2],u=n[0]*n[7]-n[4]*n[3],i=n[1]*n[6]-n[5]*n[2],o=n[1]*n[7]-n[5]*n[3],c=n[2]*n[7]-n[6]*n[3],f=n[10]*n[15]-n[14]*n[11],s=n[9]*n[15]-n[13]*n[11],h=n[9]*n[14]-n[13]*n[10],v=n[8]*n[15]-n[12]*n[11],M=n[8]*n[14]-n[12]*n[10],l=n[8]*n[13]-n[12]*n[9],p=1/(a*f-e*s+u*h+i*v-o*M+c*l);if(isNaN(p)||p===1/0)throw new Error("Inverse determinant attempted to divide by zero.");return[(n[5]*f-n[6]*s+n[7]*h)*p,(-n[1]*f+n[2]*s-n[3]*h)*p,(n[13]*c-n[14]*o+n[15]*i)*p,(-n[9]*c+n[10]*o-n[11]*i)*p,(-n[4]*f+n[6]*v-n[7]*M)*p,(n[0]*f-n[2]*v+n[3]*M)*p,(-n[12]*c+n[14]*u-n[15]*e)*p,(n[8]*c-n[10]*u+n[11]*e)*p,(n[4]*s-n[5]*v+n[7]*l)*p,(-n[0]*s+n[1]*v-n[3]*l)*p,(n[12]*o-n[13]*u+n[15]*a)*p,(-n[8]*o+n[9]*u-n[11]*a)*p,(-n[4]*h+n[5]*M-n[6]*l)*p,(n[0]*h-n[1]*M+n[2]*l)*p,(-n[12]*i+n[13]*e-n[14]*a)*p,(n[8]*i-n[9]*e+n[10]*a)*p]}function e(r,n){for(var a=t(r),e=t(n),u=[],i=0;i<4;i++)for(var o=[a[i],a[i+4],a[i+8],a[i+12]],c=0;c<4;c++){var f=4*c,s=[e[f],e[f+1],e[f+2],e[f+3]],h=o[0]*s[0]+o[1]*s[1]+o[2]*s[2]+o[3]*s[3];u[i+f]=h}return u}function u(r){if("string"==typeof r){var a=r.match(/matrix(3d)?\(([^)]+)\)/);if(a){return t(a[2].split(", ").map(parseFloat))}}return n()}function i(r){return f(r)}function o(r){var t=Math.PI/180*r,a=n();return a[5]=a[10]=Math.cos(t),a[6]=a[9]=Math.sin(t),a[9]*=-1,a}function c(r){var t=Math.PI/180*r,a=n();return a[0]=a[10]=Math.cos(t),a[2]=a[8]=Math.sin(t),a[2]*=-1,a}function f(r){var t=Math.PI/180*r,a=n();return a[0]=a[5]=Math.cos(t),a[1]=a[4]=Math.sin(t),a[4]*=-1,a}function s(r,t){var a=n();return a[0]=r,a[5]="number"==typeof t?t:r,a}function h(r){var t=n();return t[0]=r,t}function v(r){var t=n();return t[5]=r,t}function M(r){var t=n();return t[10]=r,t}function l(r,t){var a=Math.PI/180*r,e=n();if(e[4]=Math.tan(a),t){var u=Math.PI/180*t;e[1]=Math.tan(u)}return e}function p(r){var t=Math.PI/180*r,a=n();return a[4]=Math.tan(t),a}function m(r){var t=Math.PI/180*r,a=n();return a[1]=Math.tan(t),a}function w(r){return"matrix3d("+t(r).join(", ")+")"}function d(r,t){var a=n();return a[12]=r,t&&(a[13]=t),a}function y(r){var t=n();return t[12]=r,t}function I(r){var t=n();return t[13]=r,t}function P(r){var t=n();return t[14]=r,t}return r.format=t,r.identity=n,r.inverse=a,r.multiply=e,r.parse=u,r.rotate=i,r.rotateX=o,r.rotateY=c,r.rotateZ=f,r.scale=s,r.scaleX=h,r.scaleY=v,r.scaleZ=M,r.skew=l,r.skewX=p,r.skewY=m,r.toString=w,r.translate=d,r.translateX=y,r.translateY=I,r.translateZ=P,r}({});
