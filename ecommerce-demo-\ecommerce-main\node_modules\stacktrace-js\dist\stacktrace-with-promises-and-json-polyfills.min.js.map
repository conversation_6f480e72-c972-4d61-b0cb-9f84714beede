{"version": 3, "sources": ["node_modules/browser-pack/_prelude.js", "node_modules/error-stack-parser/error-stack-parser.js", "stacktrace-with-promises-and-json-polyfills.js", "node_modules/error-stack-parser/node_modules/stackframe/stackframe.js", "node_modules/es6-promise/dist/es6-promise.js", "node_modules/json3/lib/json3.js", "node_modules/process/browser.js", "node_modules/source-map/lib/array-set.js", "node_modules/source-map/lib/base64-vlq.js", "node_modules/source-map/lib/base64.js", "node_modules/source-map/lib/binary-search.js", "node_modules/source-map/lib/quick-sort.js", "node_modules/source-map/lib/source-map-consumer.js", "node_modules/source-map/lib/util.js", "node_modules/stack-generator/stack-generator.js", "node_modules/stacktrace-gps/stacktrace-gps.js", "polyfills.js", "stacktrace.js"], "names": ["f", "exports", "module", "define", "amd", "g", "window", "global", "self", "this", "StackTrace", "r", "e", "n", "t", "o", "i", "c", "require", "u", "a", "Error", "code", "p", "call", "length", "1", "root", "factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StackFrame", "FIREFOX_SAFARI_STACK_REGEXP", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "parse", "error", "stacktrace", "parseOpera", "stack", "match", "parseV8OrIE", "parseFFOr<PERSON><PERSON><PERSON>", "extractLocation", "urlLike", "indexOf", "regExp", "parts", "exec", "replace", "undefined", "filtered", "split", "filter", "line", "map", "sanitizedLine", "location", "tokens", "slice", "locationParts", "pop", "functionName", "join", "fileName", "lineNumber", "columnNumber", "source", "functionNameRegex", "matches", "message", "parseOpera9", "parseOpera11", "parseOpera10", "lineRE", "lines", "result", "len", "push", "argsRaw", "functionCall", "shift", "args", "stackframe", "2", "_isNumber", "isNaN", "parseFloat", "isFinite", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "substring", "_getter", "obj", "props", "booleanProps", "numericProps", "stringProps", "arrayProps", "concat", "prototype", "getArgs", "set<PERSON>rgs", "v", "Object", "toString", "TypeError", "getEval<PERSON><PERSON>in", "eval<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getFileName", "getLineNumber", "getColumnNumber", "getFunctionName", "getIsEval", "fromString", "argsStartIndex", "argsEndIndex", "lastIndexOf", "locationString", "Boolean", "j", "Number", "k", "String", "3", "process", "ES6Promise", "objectOrFunction", "x", "isFunction", "setScheduler", "scheduleFn", "customSchedulerFn", "setAsap", "asapFn", "asap", "useNextTick", "nextTick", "flush", "useVertxTimer", "vertxNext", "useMutationObserver", "iterations", "observer", "BrowserMutationObserver", "node", "document", "createTextNode", "observe", "characterData", "data", "useMessageChannel", "channel", "MessageChannel", "port1", "onmessage", "port2", "postMessage", "useSetTimeout", "globalSetTimeout", "setTimeout", "callback", "queue", "arg", "attemptVertx", "vertx", "runOnLoop", "runOnContext", "then", "onFulfillment", "onRejection", "_arguments", "arguments", "parent", "child", "constructor", "noop", "PROMISE_ID", "makePromise", "_state", "invokeCallback", "_result", "subscribe", "resolve", "object", "<PERSON><PERSON><PERSON><PERSON>", "promise", "_resolve", "selfFulfillment", "cannotReturnOwn", "getThen", "GET_THEN_ERROR", "tryThen", "value", "fulfillmentH<PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>", "handleForeignThenable", "thenable", "sealed", "fulfill", "reason", "_reject", "_label", "handleOwnThenable", "FULFILLED", "REJECTED", "handleMaybeThenable", "maybeThenable", "then$$", "publishRejection", "_onerror", "publish", "PENDING", "_subscribers", "subscribers", "settled", "detail", "ErrorObject", "tryCatch", "TRY_CATCH_ERROR", "<PERSON><PERSON><PERSON><PERSON>", "succeeded", "failed", "initializePromise", "resolver", "nextId", "id", "Enumerator", "input", "_instanceConstructor", "isArray", "_input", "_remaining", "Array", "_enumerate", "validationError", "all", "entries", "race", "reject", "_", "needsResolver", "needsNew", "Promise", "polyfill", "local", "Function", "P", "promiseToString", "cast", "_isArray", "scheduleFlush", "browserWindow", "browserGlobal", "MutationObserver", "WebKitMutationObserver", "isNode", "isWorker", "Uint8ClampedArray", "importScripts", "Math", "random", "_eachEntry", "entry", "resolve$$", "_then", "_settledAt", "_willSettleAt", "state", "enumerator", "_setScheduler", "_setAsap", "_asap", "catch", "_process", "4", "runInContext", "context", "has", "name", "undef", "isSupported", "serialized", "stringify", "stringifySupported", "isExtended", "toJSON", "getClass", "Date", "exception", "parseSupported", "SyntaxError", "nativeJSON", "isProperty", "for<PERSON>ach", "objectProto", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "functionClass", "dateClass", "numberClass", "stringClass", "arrayClass", "booleanClass", "charIndexBuggy", "floor", "Months", "getDay", "year", "month", "hasOwnProperty", "property", "members", "__proto__", "original", "Properties", "size", "valueOf", "isConstructor", "hasProperty", "objectTypes", "Escapes", "92", "34", "8", "12", "10", "13", "9", "leadingZeroes", "toPaddedString", "width", "unicodePrefix", "quote", "index", "useCharIndex", "symbols", "charCode", "charCodeAt", "serialize", "properties", "whitespace", "indentation", "className", "date", "time", "hours", "minutes", "seconds", "milliseconds", "results", "element", "prefix", "Index", "Source", "fromCharCode", "Unescapes", "47", "98", "116", "110", "102", "114", "abort", "lex", "begin", "position", "isSigned", "get", "hasM<PERSON>bers", "update", "walk", "<PERSON><PERSON><PERSON><PERSON>", "function", "freeExports", "nodeType", "freeGlobal", "JSON", "previousJSON", "isRestored", "JSON3", "noConflict", "5", "defaultSetTimout", "defaultClearTimeout", "runTimeout", "fun", "cachedSetTimeout", "runClearTimeout", "marker", "cachedClearTimeout", "clearTimeout", "cleanUpNextTick", "draining", "currentQueue", "queueIndex", "drainQueue", "timeout", "run", "<PERSON><PERSON>", "array", "apply", "title", "browser", "env", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "binding", "cwd", "chdir", "dir", "umask", "6", "ArraySet", "_array", "_set", "create", "util", "fromArray", "aArray", "aAllowDuplicates", "set", "add", "getOwnPropertyNames", "aStr", "sStr", "toSetString", "isDuplicate", "idx", "at", "aIdx", "toArray", "./util", "7", "toVLQSigned", "aValue", "fromVLQSigned", "isNegative", "shifted", "base64", "VLQ_BASE_SHIFT", "VLQ_BASE", "VLQ_BASE_MASK", "VLQ_CONTINUATION_BIT", "encode", "digit", "encoded", "vlq", "decode", "aIndex", "aOutParam", "continuation", "strLen", "rest", "./base64", "intToCharMap", "number", "bigA", "bigZ", "littleA", "littleZ", "zero", "nine", "plus", "slash", "littleOffset", "numberOffset", "recursiveSearch", "aLow", "aHigh", "aNeedle", "aHaystack", "aCompare", "aBias", "mid", "cmp", "LEAST_UPPER_BOUND", "GREATEST_LOWER_BOUND", "search", "swap", "ary", "y", "temp", "randomIntInRange", "low", "high", "round", "doQuickSort", "comparator", "pivotIndex", "pivot", "q", "quickSort", "11", "SourceMapConsumer", "aSourceMap", "sourceMap", "sections", "IndexedSourceMapConsumer", "BasicSourceMapConsumer", "getArg", "sources", "names", "sourceRoot", "sourcesContent", "mappings", "file", "_version", "normalize", "isAbsolute", "relative", "_names", "_sources", "_mappings", "Mapping", "generatedLine", "generatedColumn", "originalLine", "originalColumn", "lastOffset", "column", "_sections", "s", "url", "offset", "offsetLine", "offsetColumn", "generatedOffset", "consumer", "binarySearch", "base64VLQ", "fromSourceMap", "__generatedMappings", "defineProperty", "_parseMappings", "__originalMappings", "_charIsMappingSeparator", "aSourceRoot", "GENERATED_ORDER", "ORIGINAL_ORDER", "eachMapping", "aCallback", "aContext", "aOrder", "order", "_generatedMappings", "_originalMappings", "mapping", "allGeneratedPositionsFor", "aArgs", "needle", "_findMapping", "compareByOriginalPositions", "lastColumn", "smc", "_sourceRoot", "_generateSourcesContent", "_file", "generatedMappings", "destGeneratedMappings", "destOriginalMappings", "srcMapping", "destMapping", "segment", "end", "previousGeneratedColumn", "previousOriginalLine", "previousOriginalColumn", "previousSource", "previousName", "cachedSegments", "originalMappings", "compareByGeneratedPositionsDeflated", "aMappings", "aLineName", "aColumnName", "aComparator", "computeColumnSpans", "nextMapping", "lastGeneratedColumn", "Infinity", "originalPositionFor", "hasContentsOfAllSources", "some", "sc", "sourceContentFor", "aSource", "nullOnMissing", "urlParse", "fileUriAbsPath", "scheme", "path", "generatedPositionFor", "sectionIndex", "section", "bias", "every", "content", "generatedPosition", "ret", "sectionMappings", "adjustedMapping", "./array-set", "./base64-vlq", "./binary-search", "./quick-sort", "aName", "aDefaultValue", "aUrl", "urlRegexp", "auth", "host", "port", "urlGenerate", "aParsedUrl", "a<PERSON><PERSON>", "part", "up", "splice", "aRoot", "aPathUrl", "aRootUrl", "dataUrlRegexp", "joined", "level", "substr", "identity", "isProtoString", "fromSetString", "mappingA", "mappingB", "onlyCompareOriginal", "onlyCompareGenerated", "strcmp", "aStr1", "aStr2", "compareByGeneratedPositionsInflated", "supportsNullProto", "dup", "14", "StackGenerator", "backtrace", "opts", "maxStackSize", "curr", "callee", "test", "RegExp", "$1", "caller", "15", "16", "StackTraceGPS", "SourceMap", "_xdr", "req", "XMLHttpRequest", "open", "onerror", "onreadystatechange", "readyState", "status", "responseText", "send", "_atob", "b64str", "atob", "_parseJson", "string", "_findFunctionName", "syntaxes", "maxLines", "min", "commentPos", "m", "_ensureSupportedEnvironment", "_ensureStackFrameIsLegit", "_findSourceMappingURL", "lastSourceMappingUrl", "matchSourceMappingUrl", "sourceMappingUrlRegExp", "_extractLocationInfoFromSourceMapSource", "sourceMapConsumer", "sourceCache", "loc", "mappedSource", "sourceMapConsumerCache", "ajax", "_get", "isDataUrl", "offline", "supportedEncodingRegexp", "sourceMapStart", "encodedSource", "xhrPromise", "method", "bind", "_getSourceMapConsumer", "sourceMappingURL", "defaultSourceRoot", "sourceMapConsumerPromise", "sourceMapSource", "pinpoint", "getMappedLocation", "mappedStackFrame", "resolveMappedStackFrame", "findFunctionName", "guessedFunctionName", "source-map/lib/source-map-consumer", "17", "oThis", "fToBind", "NoOp", "fBound", "thisArg", "T", "O", "A", "kValue", "mappedValue", "res", "val", "18", "_merge", "first", "second", "target", "prop", "_isShapedLikeParsableError", "err", "_filtered", "stackframes", "_options", "_generateError", "fromError", "generateArtificially", "getSync", "gps", "sf", "resolveOriginal", "stackFrames", "instrument", "fn", "errback", "__stacktraceOriginalFn", "instrumented", "deinstrument", "report", "errorMsg", "requestOptions", "setRequestHeader", "headers", "header", "reportPayload", "error-stack-parser", "stack-generator", "stacktrace-gps"], "mappings": "CAAA,SAAAA,GAAA,GAAA,gBAAAC,UAAA,mBAAAC,QAAAA,OAAAD,QAAAD,QAAA,IAAA,kBAAAG,SAAAA,OAAAC,IAAAD,UAAAH,OAAA,CAAA,GAAAK,EAAAA,GAAA,mBAAAC,QAAAA,OAAA,mBAAAC,QAAAA,OAAA,mBAAAC,MAAAA,KAAAC,KAAAJ,EAAAK,WAAAV,MAAA,WAAA,GAAAG,EAAA,OAAA,YAAA,QAAAQ,GAAAC,EAAAC,EAAAC,GAAA,QAAAC,GAAAC,EAAAhB,GAAA,IAAAa,EAAAG,GAAA,CAAA,IAAAJ,EAAAI,GAAA,CAAA,GAAAC,GAAA,kBAAAC,UAAAA,OAAA,KAAAlB,GAAAiB,EAAA,MAAAA,GAAAD,GAAA,EAAA,IAAAG,EAAA,MAAAA,GAAAH,GAAA,EAAA,IAAAI,GAAA,GAAAC,OAAA,uBAAAL,EAAA,IAAA,MAAAI,GAAAE,KAAA,mBAAAF,EAAA,GAAAG,GAAAV,EAAAG,IAAAf,WAAAW,GAAAI,GAAA,GAAAQ,KAAAD,EAAAtB,QAAA,SAAAU,GAAA,GAAAE,GAAAD,EAAAI,GAAA,GAAAL,EAAA,OAAAI,GAAAF,GAAAF,IAAAY,EAAAA,EAAAtB,QAAAU,EAAAC,EAAAC,EAAAC,GAAA,MAAAD,GAAAG,GAAAf,QAAA,IAAA,GAAAkB,GAAA,kBAAAD,UAAAA,QAAAF,EAAA,EAAAA,EAAAF,EAAAW,OAAAT,IAAAD,EAAAD,EAAAE,GAAA,OAAAD,GAAA,MAAAJ,OAAAe,GAAA,SAAAR,EAAAhB,EAAAD,ICAA,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,sBAAA,cAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,EAAAV,EAAA,eAEAS,EAAAE,iBAAAD,EAAAD,EAAAG,aAEArB,KAAA,SAAAqB,GACA,YAEA,IAAAC,GAAA,eACAC,EAAA,iCACAC,EAAA,6BAEA,QAOAC,MAAA,SAAAC,GACA,GAAA,mBAAAA,GAAAC,YAAA,mBAAAD,GAAA,mBACA,MAAA1B,MAAA4B,WAAAF,EACA,IAAAA,EAAAG,OAAAH,EAAAG,MAAAC,MAAAP,GACA,MAAAvB,MAAA+B,YAAAL,EACA,IAAAA,EAAAG,MACA,MAAA7B,MAAAgC,gBAAAN,EAEA,MAAA,IAAAd,OAAA,oCAKAqB,gBAAA,SAAAC,GAEA,GAAAA,EAAAC,QAAA,UACA,OAAAD,EAGA,IAAAE,GAAA,+BACAC,EAAAD,EAAAE,KAAAJ,EAAAK,QAAA,QAAA,IACA,QAAAF,EAAA,GAAAA,EAAA,IAAAG,OAAAH,EAAA,IAAAG,SAGAT,YAAA,SAAAL,GACA,GAAAe,GAAAf,EAAAG,MAAAa,MAAA,MAAAC,OAAA,SAAAC,GACA,QAAAA,EAAAd,MAAAP,IACAvB,KAEA,OAAAyC,GAAAI,IAAA,SAAAD,GACAA,EAAAT,QAAA,eAEAS,EAAAA,EAAAL,QAAA,aAAA,QAAAA,QAAA,+BAAA,IAEA,IAAAO,GAAAF,EAAAL,QAAA,OAAA,IAAAA,QAAA,eAAA,KAIAQ,EAAAD,EAAAhB,MAAA,2BAGAgB,GAAAC,EAAAD,EAAAP,QAAAQ,EAAA,GAAA,IAAAD,CAEA,IAAAE,GAAAF,EAAAJ,MAAA,OAAAO,MAAA,GAEAC,EAAAlD,KAAAiC,gBAAAc,EAAAA,EAAA,GAAAC,EAAAG,OACAC,EAAAJ,EAAAK,KAAA,MAAAb,OACAc,GAAA,OAAA,eAAAnB,QAAAe,EAAA,OAAAV,OAAAU,EAAA,EAEA,OAAA,IAAA7B,IACA+B,aAAAA,EACAE,SAAAA,EACAC,WAAAL,EAAA,GACAM,aAAAN,EAAA,GACAO,OAAAb,KAEA5C,OAGAgC,gBAAA,SAAAN,GACA,GAAAe,GAAAf,EAAAG,MAAAa,MAAA,MAAAC,OAAA,SAAAC,GACA,OAAAA,EAAAd,MAAAN,IACAxB,KAEA,OAAAyC,GAAAI,IAAA,SAAAD,GAMA,GAJAA,EAAAT,QAAA,gBACAS,EAAAA,EAAAL,QAAA,mDAAA,QAGAK,EAAAT,QAAA,WAAAS,EAAAT,QAAA,UAEA,MAAA,IAAAd,IACA+B,aAAAR,GAGA,IAAAc,GAAA,6BACAC,EAAAf,EAAAd,MAAA4B,GACAN,EAAAO,GAAAA,EAAA,GAAAA,EAAA,GAAAnB,OACAU,EAAAlD,KAAAiC,gBAAAW,EAAAL,QAAAmB,EAAA,IAEA,OAAA,IAAArC,IACA+B,aAAAA,EACAE,SAAAJ,EAAA,GACAK,WAAAL,EAAA,GACAM,aAAAN,EAAA,GACAO,OAAAb,KAGA5C,OAGA4B,WAAA,SAAAzB,GACA,OAAAA,EAAAwB,YAAAxB,EAAAyD,QAAAzB,QAAA,UACAhC,EAAAyD,QAAAlB,MAAA,MAAA1B,OAAAb,EAAAwB,WAAAe,MAAA,MAAA1B,OACAhB,KAAA6D,YAAA1D,GACAA,EAAA0B,MAGA7B,KAAA8D,aAAA3D,GAFAH,KAAA+D,aAAA5D,IAMA0D,YAAA,SAAA1D,GAKA,IAAA,GAJA6D,GAAA,oCACAC,EAAA9D,EAAAyD,QAAAlB,MAAA,MACAwB,KAEA3D,EAAA,EAAA4D,EAAAF,EAAAjD,OAAAT,EAAA4D,EAAA5D,GAAA,EAAA,CACA,GAAAuB,GAAAkC,EAAA1B,KAAA2B,EAAA1D,GACAuB,IACAoC,EAAAE,KAAA,GAAA/C,IACAiC,SAAAxB,EAAA,GACAyB,WAAAzB,EAAA,GACA2B,OAAAQ,EAAA1D,MAKA,MAAA2D,IAGAH,aAAA,SAAA5D,GAKA,IAAA,GAJA6D,GAAA,6DACAC,EAAA9D,EAAAwB,WAAAe,MAAA,MACAwB,KAEA3D,EAAA,EAAA4D,EAAAF,EAAAjD,OAAAT,EAAA4D,EAAA5D,GAAA,EAAA,CACA,GAAAuB,GAAAkC,EAAA1B,KAAA2B,EAAA1D,GACAuB,IACAoC,EAAAE,KACA,GAAA/C,IACA+B,aAAAtB,EAAA,IAAAU,OACAc,SAAAxB,EAAA,GACAyB,WAAAzB,EAAA,GACA2B,OAAAQ,EAAA1D,MAMA,MAAA2D,IAIAJ,aAAA,SAAApC,GACA,GAAAe,GAAAf,EAAAG,MAAAa,MAAA,MAAAC,OAAA,SAAAC,GACA,QAAAA,EAAAd,MAAAR,KAAAsB,EAAAd,MAAA,sBACA9B,KAEA,OAAAyC,GAAAI,IAAA,SAAAD,GACA,GAMAyB,GANArB,EAAAJ,EAAAF,MAAA,KACAQ,EAAAlD,KAAAiC,gBAAAe,EAAAG,OACAmB,EAAAtB,EAAAuB,SAAA,GACAnB,EAAAkB,EACA/B,QAAA,iCAAA,MACAA,QAAA,aAAA,KAAAC,MAEA8B,GAAAxC,MAAA,iBACAuC,EAAAC,EAAA/B,QAAA,qBAAA,MAEA,IAAAiC,GAAAhC,SAAA6B,GAAA,8BAAAA,EACA7B,OAAA6B,EAAA3B,MAAA,IAEA,OAAA,IAAArB,IACA+B,aAAAA,EACAoB,KAAAA,EACAlB,SAAAJ,EAAA,GACAK,WAAAL,EAAA,GACAM,aAAAN,EAAA,GACAO,OAAAb,KAEA5C,YCMGyE,WAAa,IAAIC,GAAG,SAASjE,EAAQhB,EAAOD,IC5M/C,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,gBAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,IAEAD,EAAAG,WAAAF,KAEAnB,KAAA,WACA,YACA,SAAA2E,GAAAvE,GACA,OAAAwE,MAAAC,WAAAzE,KAAA0E,SAAA1E,GAGA,QAAA2E,GAAAC,GACA,MAAAA,GAAAC,OAAA,GAAAC,cAAAF,EAAAG,UAAA,GAGA,QAAAC,GAAAtE,GACA,MAAA,YACA,MAAAd,MAAAc,IAWA,QAAAO,GAAAgE,GACA,GAAAA,EACA,IAAA,GAAA9E,GAAA,EAAAA,EAAA+E,EAAAtE,OAAAT,IACAiC,SAAA6C,EAAAC,EAAA/E,KACAP,KAAA,MAAA+E,EAAAO,EAAA/E,KAAA8E,EAAAC,EAAA/E,KAXA,GAAAgF,IAAA,gBAAA,SAAA,WAAA,cACAC,GAAA,eAAA,cACAC,GAAA,WAAA,eAAA,UACAC,GAAA,QAEAJ,EAAAC,EAAAI,OAAAH,EAAAC,EAAAC,EAWArE,GAAAuE,WACAC,QAAA,WACA,MAAA7F,MAAAwE,MAEAsB,QAAA,SAAAC,GACA,GAAA,mBAAAC,OAAAJ,UAAAK,SAAAlF,KAAAgF,GACA,KAAA,IAAAG,WAAA,wBAEAlG,MAAAwE,KAAAuB,GAGAI,cAAA,WACA,MAAAnG,MAAAoG,YAEAC,cAAA,SAAAN,GACA,GAAAA,YAAA1E,GACArB,KAAAoG,WAAAL,MACA,CAAA,KAAAA,YAAAC,SAGA,KAAA,IAAAE,WAAA,8CAFAlG,MAAAoG,WAAA,GAAA/E,GAAA0E,KAMAE,SAAA,WACA,GAAA3C,GAAAtD,KAAAsG,eAAA,GACA/C,EAAAvD,KAAAuG,iBAAA,GACA/C,EAAAxD,KAAAwG,mBAAA,GACApD,EAAApD,KAAAyG,mBAAA,EACA,OAAAzG,MAAA0G,YACApD,EACA,WAAAA,EAAA,IAAAC,EAAA,IAAAC,EAAA,IAEA,UAAAD,EAAA,IAAAC,EAEAJ,EACAA,EAAA,KAAAE,EAAA,IAAAC,EAAA,IAAAC,EAAA,IAEAF,EAAA,IAAAC,EAAA,IAAAC,IAIAnC,EAAAsF,WAAA,SAAA3B,GACA,GAAA4B,GAAA5B,EAAA7C,QAAA,KACA0E,EAAA7B,EAAA8B,YAAA,KAEA1D,EAAA4B,EAAAG,UAAA,EAAAyB,GACApC,EAAAQ,EAAAG,UAAAyB,EAAA,EAAAC,GAAAnE,MAAA,KACAqE,EAAA/B,EAAAG,UAAA0B,EAAA,EAEA,IAAA,IAAAE,EAAA5E,QAAA,KACA,GAAAE,GAAA,gCAAAC,KAAAyE,EAAA,IACAzD,EAAAjB,EAAA,GACAkB,EAAAlB,EAAA,GACAmB,EAAAnB,EAAA,EAGA,OAAA,IAAAhB,IACA+B,aAAAA,EACAoB,KAAAA,GAAAhC,OACAc,SAAAA,EACAC,WAAAA,GAAAf,OACAgB,aAAAA,GAAAhB,SAIA,KAAA,GAAAjC,GAAA,EAAAA,EAAAgF,EAAAvE,OAAAT,IACAc,EAAAuE,UAAA,MAAAb,EAAAQ,EAAAhF,KAAA6E,EAAAG,EAAAhF,IACAc,EAAAuE,UAAA,MAAAb,EAAAQ,EAAAhF,KAAA,SAAAO,GACA,MAAA,UAAAiF,GACA/F,KAAAc,GAAAkG,QAAAjB,KAEAR,EAAAhF,GAGA,KAAA,GAAA0G,GAAA,EAAAA,EAAAzB,EAAAxE,OAAAiG,IACA5F,EAAAuE,UAAA,MAAAb,EAAAS,EAAAyB,KAAA7B,EAAAI,EAAAyB,IACA5F,EAAAuE,UAAA,MAAAb,EAAAS,EAAAyB,KAAA,SAAAnG,GACA,MAAA,UAAAiF,GACA,IAAApB,EAAAoB,GACA,KAAA,IAAAG,WAAApF,EAAA,oBAEAd,MAAAc,GAAAoG,OAAAnB,KAEAP,EAAAyB,GAGA,KAAA,GAAAE,GAAA,EAAAA,EAAA1B,EAAAzE,OAAAmG,IACA9F,EAAAuE,UAAA,MAAAb,EAAAU,EAAA0B,KAAA/B,EAAAK,EAAA0B,IACA9F,EAAAuE,UAAA,MAAAb,EAAAU,EAAA0B,KAAA,SAAArG,GACA,MAAA,UAAAiF,GACA/F,KAAAc,GAAAsG,OAAArB,KAEAN,EAAA0B,GAGA,OAAA9F,UDgNMgG,GAAG,SAAS5G,EAAQhB,EAAOD,IACjC,SAAW8H,EAAQxH,IErVnB,SAAAA,EAAAqB,GACA,gBAAA3B,IAAA,mBAAAC,GAAAA,EAAAD,QAAA2B,IACA,kBAAAzB,IAAAA,EAAAC,IAAAD,EAAAyB,GACArB,EAAAyH,WAAApG,KACAnB,KAAA,WAAA,YAEA,SAAAwH,GAAAC,GACA,MAAA,kBAAAA,IAAA,gBAAAA,IAAA,OAAAA,EAGA,QAAAC,GAAAD,GACA,MAAA,kBAAAA,GAkCA,QAAAE,GAAAC,GACAC,EAAAD,EAGA,QAAAE,GAAAC,GACAC,EAAAD,EAYA,QAAAE,KAGA,MAAA,YACA,MAAAX,GAAAY,SAAAC,IAKA,QAAAC,KACA,MAAA,YACAC,EAAAF,IAIA,QAAAG,KACA,GAAAC,GAAA,EACAC,EAAA,GAAAC,GAAAN,GACAO,EAAAC,SAAAC,eAAA,GAGA,OAFAJ,GAAAK,QAAAH,GAAAI,eAAA,IAEA,WACAJ,EAAAK,KAAAR,IAAAA,EAAA,GAKA,QAAAS,KACA,GAAAC,GAAA,GAAAC,eAEA,OADAD,GAAAE,MAAAC,UAAAjB,EACA,WACA,MAAAc,GAAAI,MAAAC,YAAA,IAIA,QAAAC,KAGA,GAAAC,GAAAC,UACA,OAAA,YACA,MAAAD,GAAArB,EAAA,IAKA,QAAAA,KACA,IAAA,GAAA5H,GAAA,EAAAA,EAAA4D,EAAA5D,GAAA,EAAA,CACA,GAAAmJ,GAAAC,GAAApJ,GACAqJ,EAAAD,GAAApJ,EAAA,EAEAmJ,GAAAE,GAEAD,GAAApJ,GAAAiC,OACAmH,GAAApJ,EAAA,GAAAiC,OAGA2B,EAAA,EAGA,QAAA0F,KACA,IACA,GAAA3J,GAAAO,EACAqJ,EAAA5J,EAAA,QAEA,OADAmI,GAAAyB,EAAAC,WAAAD,EAAAE,aACA5B,IACA,MAAAjI,GACA,MAAAoJ,MAkBA,QAAAU,GAAAC,EAAAC,GACA,GAAAC,GAAAC,UAEAC,EAAAtK,KAEAuK,EAAA,GAAAvK,MAAAwK,YAAAC,EAEAjI,UAAA+H,EAAAG,KACAC,EAAAJ,EAGA,IAAAK,GAAAN,EAAAM,MAaA,OAXAA,IACA,WACA,GAAAlB,GAAAU,EAAAQ,EAAA,EACA5C,GAAA,WACA,MAAA6C,GAAAD,EAAAL,EAAAb,EAAAY,EAAAQ,cAIAC,EAAAT,EAAAC,EAAAL,EAAAC,GAGAI,EAkCA,QAAAS,GAAAC,GAEA,GAAAC,GAAAlL,IAEA,IAAAiL,GAAA,gBAAAA,IAAAA,EAAAT,cAAAU,EACA,MAAAD,EAGA,IAAAE,GAAA,GAAAD,GAAAT,EAEA,OADAW,GAAAD,EAAAF,GACAE,EAKA,QAAAV,MAQA,QAAAY,KACA,MAAA,IAAAnF,WAAA,4CAGA,QAAAoF,KACA,MAAA,IAAApF,WAAA,wDAGA,QAAAqF,GAAAJ,GACA,IACA,MAAAA,GAAAlB,KACA,MAAAvI,GAEA,MADA8J,IAAA9J,MAAAA,EACA8J,IAIA,QAAAC,GAAAxB,EAAAyB,EAAAC,EAAAC,GACA,IACA3B,EAAAlJ,KAAA2K,EAAAC,EAAAC,GACA,MAAAzL,GACA,MAAAA,IAIA,QAAA0L,GAAAV,EAAAW,EAAA7B,GACAjC,EAAA,SAAAmD,GACA,GAAAY,IAAA,EACArK,EAAA+J,EAAAxB,EAAA6B,EAAA,SAAAJ,GACAK,IAGAA,GAAA,EACAD,IAAAJ,EACAN,EAAAD,EAAAO,GAEAM,EAAAb,EAAAO,KAEA,SAAAO,GACAF,IAGAA,GAAA,EAEAG,EAAAf,EAAAc,KACA,YAAAd,EAAAgB,QAAA,sBAEAJ,GAAArK,IACAqK,GAAA,EACAG,EAAAf,EAAAzJ,KAEAyJ,GAGA,QAAAiB,GAAAjB,EAAAW,GACAA,EAAAlB,SAAAyB,GACAL,EAAAb,EAAAW,EAAAhB,SACAgB,EAAAlB,SAAA0B,GACAJ,EAAAf,EAAAW,EAAAhB,SAEAC,EAAAe,EAAAtJ,OAAA,SAAAkJ,GACA,MAAAN,GAAAD,EAAAO,IACA,SAAAO,GACA,MAAAC,GAAAf,EAAAc,KAKA,QAAAM,GAAApB,EAAAqB,EAAAC,GACAD,EAAAhC,cAAAW,EAAAX,aAAAiC,IAAAxC,GAAAuC,EAAAhC,YAAAQ,UAAAA,EACAoB,EAAAjB,EAAAqB,GAEAC,IAAAjB,GACAU,EAAAf,EAAAK,GAAA9J,OACAc,SAAAiK,EACAT,EAAAb,EAAAqB,GACA9E,EAAA+E,GACAZ,EAAAV,EAAAqB,EAAAC,GAEAT,EAAAb,EAAAqB,GAKA,QAAApB,GAAAD,EAAAO,GACAP,IAAAO,EACAQ,EAAAf,EAAAE,KACA7D,EAAAkE,GACAa,EAAApB,EAAAO,EAAAH,EAAAG,IAEAM,EAAAb,EAAAO,GAIA,QAAAgB,GAAAvB,GACAA,EAAAwB,UACAxB,EAAAwB,SAAAxB,EAAAL,SAGA8B,EAAAzB,GAGA,QAAAa,GAAAb,EAAAO,GACAP,EAAAP,SAAAiC,KAIA1B,EAAAL,QAAAY,EACAP,EAAAP,OAAAyB,GAEA,IAAAlB,EAAA2B,aAAA9L,QACAgH,EAAA4E,EAAAzB,IAIA,QAAAe,GAAAf,EAAAc,GACAd,EAAAP,SAAAiC,KAGA1B,EAAAP,OAAA0B,GACAnB,EAAAL,QAAAmB,EAEAjE,EAAA0E,EAAAvB,IAGA,QAAAJ,GAAAT,EAAAC,EAAAL,EAAAC,GACA,GAAA2C,GAAAxC,EAAAwC,aACA9L,EAAA8L,EAAA9L,MAEAsJ,GAAAqC,SAAA,KAEAG,EAAA9L,GAAAuJ,EACAuC,EAAA9L,EAAAqL,IAAAnC,EACA4C,EAAA9L,EAAAsL,IAAAnC,EAEA,IAAAnJ,GAAAsJ,EAAAM,QACA5C,EAAA4E,EAAAtC,GAIA,QAAAsC,GAAAzB,GACA,GAAA4B,GAAA5B,EAAA2B,aACAE,EAAA7B,EAAAP,MAEA,IAAA,IAAAmC,EAAA/L,OAAA,CAQA,IAAA,GAJAuJ,GAAA/H,OACAkH,EAAAlH,OACAyK,EAAA9B,EAAAL,QAEAvK,EAAA,EAAAA,EAAAwM,EAAA/L,OAAAT,GAAA,EACAgK,EAAAwC,EAAAxM,GACAmJ,EAAAqD,EAAAxM,EAAAyM,GAEAzC,EACAM,EAAAmC,EAAAzC,EAAAb,EAAAuD,GAEAvD,EAAAuD,EAIA9B,GAAA2B,aAAA9L,OAAA,GAGA,QAAAkM,KACAlN,KAAA0B,MAAA,KAKA,QAAAyL,GAAAzD,EAAAuD,GACA,IACA,MAAAvD,GAAAuD,GACA,MAAA9M,GAEA,MADAiN,IAAA1L,MAAAvB,EACAiN,IAIA,QAAAvC,GAAAmC,EAAA7B,EAAAzB,EAAAuD,GACA,GAAAI,GAAA3F,EAAAgC,GACAgC,EAAAlJ,OACAd,EAAAc,OACA8K,EAAA9K,OACA+K,EAAA/K,MAEA,IAAA6K,GAWA,GAVA3B,EAAAyB,EAAAzD,EAAAuD,GAEAvB,IAAA0B,IACAG,GAAA,EACA7L,EAAAgK,EAAAhK,MACAgK,EAAA,MAEA4B,GAAA,EAGAnC,IAAAO,EAEA,WADAQ,GAAAf,EAAAG,SAIAI,GAAAuB,EACAK,GAAA,CAGAnC,GAAAP,SAAAiC,KAEAQ,GAAAC,EACAlC,EAAAD,EAAAO,GACA6B,EACArB,EAAAf,EAAAzJ,GACAsL,IAAAX,GACAL,EAAAb,EAAAO,GACAsB,IAAAV,IACAJ,EAAAf,EAAAO,IAIA,QAAA8B,GAAArC,EAAAsC,GACA,IACAA,EAAA,SAAA/B,GACAN,EAAAD,EAAAO,IACA,SAAAO,GACAC,EAAAf,EAAAc,KAEA,MAAA9L,GACA+L,EAAAf,EAAAhL,IAKA,QAAAuN,KACA,MAAAC,MAGA,QAAAhD,GAAAQ,GACAA,EAAAT,IAAAiD,KACAxC,EAAAP,OAAApI,OACA2I,EAAAL,QAAAtI,OACA2I,EAAA2B,gBAGA,QAAAc,GAAA1C,EAAA2C,GACA7N,KAAA8N,qBAAA5C,EACAlL,KAAAmL,QAAA,GAAAD,GAAAT,GAEAzK,KAAAmL,QAAAT,KACAC,EAAA3K,KAAAmL,SAGA4C,EAAAF,IACA7N,KAAAgO,OAAAH,EACA7N,KAAAgB,OAAA6M,EAAA7M,OACAhB,KAAAiO,WAAAJ,EAAA7M,OAEAhB,KAAA8K,QAAA,GAAAoD,OAAAlO,KAAAgB,QAEA,IAAAhB,KAAAgB,OACAgL,EAAAhM,KAAAmL,QAAAnL,KAAA8K,UAEA9K,KAAAgB,OAAAhB,KAAAgB,QAAA,EACAhB,KAAAmO,aACA,IAAAnO,KAAAiO,YACAjC,EAAAhM,KAAAmL,QAAAnL,KAAA8K,WAIAoB,EAAAlM,KAAAmL,QAAAiD,KAIA,QAAAA,KACA,MAAA,IAAAxN,OAAA,2CAiHA,QAAAyN,GAAAC,GACA,MAAA,IAAAV,GAAA5N,KAAAsO,GAAAnD,QAoEA,QAAAoD,GAAAD,GAEA,GAAApD,GAAAlL,IAEA,OAKA,IAAAkL,GALA6C,EAAAO,GAKA,SAAAtD,EAAAwD,GAEA,IAAA,GADAxN,GAAAsN,EAAAtN,OACAT,EAAA,EAAAA,EAAAS,EAAAT,IACA2K,EAAAF,QAAAsD,EAAA/N,IAAA0J,KAAAe,EAAAwD,IAPA,SAAAC,EAAAD,GACA,MAAAA,GAAA,GAAAtI,WAAA,sCA8CA,QAAAsI,GAAAvC,GAEA,GAAAf,GAAAlL,KACAmL,EAAA,GAAAD,GAAAT,EAEA,OADAyB,GAAAf,EAAAc,GACAd,EAGA,QAAAuD,KACA,KAAA,IAAAxI,WAAA,sFAGA,QAAAyI,KACA,KAAA,IAAAzI,WAAA,yHA0GA,QAAA0I,GAAAnB,GACAzN,KAAA0K,IAAAgD,IACA1N,KAAA8K,QAAA9K,KAAA4K,OAAApI,OACAxC,KAAA8M,gBAEArC,IAAAgD,IACA,kBAAAA,IAAAiB,IACA1O,eAAA4O,GAAApB,EAAAxN,KAAAyN,GAAAkB,KAkPA,QAAAE,KACA,GAAAC,GAAAtM,MAEA,IAAA,mBAAA1C,GACAgP,EAAAhP,MACA,IAAA,mBAAAC,MACA+O,EAAA/O,SAEA,KACA+O,EAAAC,SAAA,iBACA,MAAA5O,GACA,KAAA,IAAAS,OAAA,4EAIA,GAAAoO,GAAAF,EAAAF,OAEA,IAAAI,EAAA,CACA,GAAAC,GAAA,IACA,KACAA,EAAAjJ,OAAAJ,UAAAK,SAAAlF,KAAAiO,EAAAhE,WACA,MAAA7K,IAIA,GAAA,qBAAA8O,IAAAD,EAAAE,KACA,OAIAJ,EAAAF,QAAAA,EAhmCA,GAAAO,GAAA3M,MAMA2M,GALAjB,MAAAH,QAKAG,MAAAH,QAJA,SAAAtG,GACA,MAAA,mBAAAzB,OAAAJ,UAAAK,SAAAlF,KAAA0G,GAMA,IAAAsG,GAAAoB,EAEAhL,EAAA,EACAkE,EAAA7F,OACAqF,EAAArF,OAEAwF,EAAA,SAAA0B,EAAAE,GACAD,GAAAxF,GAAAuF,EACAC,GAAAxF,EAAA,GAAAyF,EACAzF,GAAA,EACA,IAAAA,IAIA0D,EACAA,EAAAM,GAEAiH,OAaAC,EAAA,mBAAAxP,QAAAA,OAAA2C,OACA8M,EAAAD,MACA5G,EAAA6G,EAAAC,kBAAAD,EAAAE,uBACAC,GAAA,mBAAA1P,OAAA,mBAAAuH,IAAA,wBAAArB,SAAAlF,KAAAuG,GAGAoI,GAAA,mBAAAC,oBAAA,mBAAAC,gBAAA,mBAAA1G,gBA+CAS,GAAA,GAAAuE,OAAA,KA0BAkB,GAAA5M,MAGA4M,IADAK,GACAxH,IACAQ,EACAH,IACAoH,GACA1G,IACAxG,SAAA6M,GAAA,kBAAA5O,GACAoJ,IAEAN,GA0EA,IAAAmB,IAAAmF,KAAAC,SAAA7J,SAAA,IAAAd,UAAA,IAIA0H,GAAA,OACAR,GAAA,EACAC,GAAA,EAEAd,GAAA,GAAA0B,GA4KAE,GAAA,GAAAF,GA+DAS,GAAA,CA0qBA,OA7nBAC,GAAAhI,UAAAuI,WAAA,WAIA,IAAA,GAHAnN,GAAAhB,KAAAgB,OACAgN,EAAAhO,KAAAgO,OAEAzN,EAAA,EAAAP,KAAA4K,SAAAiC,IAAAtM,EAAAS,EAAAT,IACAP,KAAA+P,WAAA/B,EAAAzN,GAAAA,IAIAqN,EAAAhI,UAAAmK,WAAA,SAAAC,EAAAzP,GACA,GAAAC,GAAAR,KAAA8N,qBACAmC,EAAAzP,EAAAwK,OAEA,IAAAiF,IAAAjF,EAAA,CACA,GAAAkF,GAAA3E,EAAAyE,EAEA,IAAAE,IAAAjG,GAAA+F,EAAApF,SAAAiC,GACA7M,KAAAmQ,WAAAH,EAAApF,OAAArK,EAAAyP,EAAAlF,aACA,IAAA,kBAAAoF,GACAlQ,KAAAiO,aACAjO,KAAA8K,QAAAvK,GAAAyP,MACA,IAAAxP,IAAAoO,EAAA,CACA,GAAAzD,GAAA,GAAA3K,GAAAiK,EACA8B,GAAApB,EAAA6E,EAAAE,GACAlQ,KAAAoQ,cAAAjF,EAAA5K,OAEAP,MAAAoQ,cAAA,GAAA5P,GAAA,SAAAyP,GACA,MAAAA,GAAAD,KACAzP,OAGAP,MAAAoQ,cAAAH,EAAAD,GAAAzP,IAIAqN,EAAAhI,UAAAuK,WAAA,SAAAE,EAAA9P,EAAAmL,GACA,GAAAP,GAAAnL,KAAAmL,OAEAA,GAAAP,SAAAiC,KACA7M,KAAAiO,aAEAoC,IAAA/D,GACAJ,EAAAf,EAAAO,GAEA1L,KAAA8K,QAAAvK,GAAAmL,GAIA,IAAA1L,KAAAiO,YACAjC,EAAAb,EAAAnL,KAAA8K,UAIA8C,EAAAhI,UAAAwK,cAAA,SAAAjF,EAAA5K,GACA,GAAA+P,GAAAtQ,IAEA+K,GAAAI,EAAA3I,OAAA,SAAAkJ,GACA,MAAA4E,GAAAH,WAAA9D,GAAA9L,EAAAmL,IACA,SAAAO,GACA,MAAAqE,GAAAH,WAAA7D,GAAA/L,EAAA0L,MA8SA2C,EAAAP,IAAAA,EACAO,EAAAL,KAAAA,EACAK,EAAA5D,QAAAA,EACA4D,EAAAJ,OAAAA,EACAI,EAAA2B,cAAA5I,EACAiH,EAAA4B,SAAA1I,EACA8G,EAAA6B,MAAAzI,EAEA4G,EAAAhJ,WACA4E,YAAAoE,EAmMA3E,KAAAA,EA6BAyG,QAAA,SAAAvG,GACA,MAAAnK,MAAAiK,KAAA,KAAAE,KAqCA0E,IAEAD,EAAAC,SAAAA,EACAD,EAAAA,QAAAA,EAEAA,MFkWG7N,KAAKf,KAAKS,EAAQ,YAA8B,mBAAXX,QAAyBA,OAAyB,mBAATC,MAAuBA,KAAyB,mBAAXF,QAAyBA,aAE5I8Q,SAAW,IAAIC,GAAG,SAASnQ,EAAQhB,EAAOD,IAC7C,SAAWM,IGl+CX,WA2BA,QAAA+Q,GAAAC,EAAAtR,GAuCA,QAAAuR,GAAAC,GACA,GAAAD,EAAAC,KAAAC,EAEA,MAAAF,GAAAC,EAEA,IAAAE,EACA,IAAA,yBAAAF,EAGAE,EAAA,KAAA,IAAA,OACA,IAAA,QAAAF,EAGAE,EAAAH,EAAA,mBAAAA,EAAA,kBACA,CACA,GAAArF,GAAAyF,EAAA,oDAEA,IAAA,kBAAAH,EAAA,CACA,GAAAI,GAAA5R,EAAA4R,UAAAC,EAAA,kBAAAD,IAAAE,CACA,IAAAD,EAAA,EAEA3F,EAAA,WACA,MAAA,KACA6F,OAAA7F,CACA,KACA2F,EAGA,MAAAD,EAAA,IAGA,MAAAA,EAAA,GAAAlK,KACA,MAAAkK,EAAA,GAAAhK,KAKAgK,EAAAI,KAAAP,GAGAG,EAAAH,KAAAA,GAGAG,MAAAH,GAMA,MAAAG,EAAA1F,IACA,OAAA0F,GAAA1F,KAGA,UAAA0F,GAAAH,KAEA,QAAAG,EAAA,OAKA,oBAAAA,GAAAH,EAAAO,EAAA,QAGAJ,GAAAzQ,GAAA+K,GAAA,GAAA,EAAA,KAAA,mBAAAyF,GAEA,MAAAC,EAAA,KAAA1F,IACA,iBAAA0F,GAAA,EAAA,GAAA,KAAA,IAGA,iCAAAA,EAAA,GAAAK,gBAEA,iCAAAL,EAAA,GAAAK,GAAA,UAGA,iCAAAL,EAAA,GAAAK,qBAGA,8BAAAL,EAAA,GAAAK,UACA,MAAAC,GACAL,GAAA,GAGAH,EAAAG,EAGA,GAAA,cAAAL,EAAA,CACA,GAAAvP,GAAAjC,EAAAiC,KACA,IAAA,kBAAAA,GACA,IAIA,GAAA,IAAAA,EAAA,OAAAA,GAAA,GAAA,CAEAiK,EAAAjK,EAAA0P,EACA,IAAAQ,GAAA,GAAAjG,EAAA,EAAA1K,QAAA,IAAA0K,EAAA,EAAA,EACA,IAAAiG,EAAA,CACA,IAEAA,GAAAlQ,EAAA,QACA,MAAAiQ,IACA,GAAAC,EACA,IAIAA,EAAA,IAAAlQ,EAAA,MACA,MAAAiQ,IAEA,GAAAC,EACA,IAIAA,EAAA,IAAAlQ,EAAA,MACA,MAAAiQ,OAIA,MAAAA,GACAC,GAAA,EAGAT,EAAAS,GAGA,MAAAZ,GAAAC,KAAAE,EApKAJ,IAAAA,EAAA5P,EAAA,UACA1B,IAAAA,EAAA0B,EAAA,SAGA,IAAAgG,GAAA4J,EAAA,QAAA5P,EAAA,OACAkG,EAAA0J,EAAA,QAAA5P,EAAA,OACA8E,EAAA8K,EAAA,QAAA5P,EAAA,OACAuQ,EAAAX,EAAA,MAAA5P,EAAA,KACA0Q,EAAAd,EAAA,aAAA5P,EAAA,YACAgF,EAAA4K,EAAA,WAAA5P,EAAA,UACA2O,EAAAiB,EAAA,MAAA5P,EAAA,KACA2Q,EAAAf,EAAA,MAAA5P,EAAA,IAGA,iBAAA2Q,IAAAA,IACArS,EAAA4R,UAAAS,EAAAT,UACA5R,EAAAiC,MAAAoQ,EAAApQ,MAIA,IAEAqQ,GAAAC,EAAAd,EAFAe,EAAAhM,EAAAJ,UACA4L,EAAAQ,EAAA/L,SAIAqL,EAAA,GAAAG,sBACA,KAGAH,EAAAA,EAAAW,2BAAA,IAAAX,EAAAY,eAAA,IAAAZ,EAAAa,cAIA,IAAAb,EAAAc,eAAA,IAAAd,EAAAe,iBAAA,GAAAf,EAAAgB,iBAAA,KAAAhB,EAAAiB,qBACA,MAAAb,IAqIA,IAAAX,EAAA,QAAA,CAEA,GAAAyB,GAAA,oBACAC,EAAA,gBACAC,EAAA,kBACAC,EAAA,kBACAC,EAAA,iBACAC,EAAA,mBAGAC,EAAA/B,EAAA,wBAGA,KAAAO,EACA,GAAAyB,GAAAlD,EAAAkD,MAGAC,GAAA,EAAA,GAAA,GAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KAGAC,EAAA,SAAAC,EAAAC,GACA,MAAAH,GAAAG,GAAA,KAAAD,EAAA,MAAAH,GAAAG,EAAA,MAAAC,IAAAA,EAAA,KAAA,GAAAJ,GAAAG,EAAA,KAAAC,GAAA,KAAAJ,GAAAG,EAAA,KAAAC,GAAA,KAwHA,KAlHArB,EAAAE,EAAAoB,kBACAtB,EAAA,SAAAuB,GACA,GAAA7I,GAAA8I,IA4BA,QA3BAA,EAAAC,UAAA,KAAAD,EAAAC,WAGAtN,SAAA,GACAqN,GAAArN,UAAAuL,EAGAM,EAAA,SAAAuB,GAIA,GAAAG,GAAAxT,KAAAuT,UAAArP,EAAAmP,KAAArT,KAAAuT,UAAA,KAAAvT,KAGA,OADAA,MAAAuT,UAAAC,EACAtP,IAIAsG,EAAA8I,EAAA9I,YAGAsH,EAAA,SAAAuB,GACA,GAAA/I,IAAAtK,KAAAwK,aAAAA,GAAA5E,SACA,OAAAyN,KAAArT,SAAAqT,IAAA/I,IAAAtK,KAAAqT,KAAA/I,EAAA+I,MAGAC,EAAA,KACAxB,EAAA/Q,KAAAf,KAAAqT,KAMAtB,EAAA,SAAA9G,EAAAvB,GACA,GAAA+J,GAAAH,EAAAD,EAAAK,EAAA,GAKAD,EAAA,WACAzT,KAAA2T,QAAA,IACA/N,UAAA+N,QAAA,EAGAL,EAAA,GAAAG,EACA,KAAAJ,IAAAC,GAEAxB,EAAA/Q,KAAAuS,EAAAD,IACAK,GAsDA,OAnDAD,GAAAH,EAAA,KAGAI,EAoBA3B,EAFA,GAAA2B,EAEA,SAAAzI,EAAAvB,GAEA,GAAA2J,GAAAC,KAAA5L,EAAA8J,EAAAzQ,KAAAkK,IAAAuH,CACA,KAAAa,IAAApI,GAIAvD,GAAA,aAAA2L,GAAAvB,EAAA/Q,KAAAuS,EAAAD,MAAAC,EAAAD,GAAA,KAAAvB,EAAA/Q,KAAAkK,EAAAoI,IACA3J,EAAA2J,IAMA,SAAApI,EAAAvB,GACA,GAAA2J,GAAAO,EAAAlM,EAAA8J,EAAAzQ,KAAAkK,IAAAuH,CACA,KAAAa,IAAApI,GACAvD,GAAA,aAAA2L,IAAAvB,EAAA/Q,KAAAkK,EAAAoI,KAAAO,EAAA,gBAAAP,IACA3J,EAAA2J,IAKAO,GAAA9B,EAAA/Q,KAAAkK,EAAAoI,EAAA,iBACA3J,EAAA2J,KA1CAC,GAAA,UAAA,WAAA,iBAAA,uBAAA,gBAAA,iBAAA,eAGAvB,EAAA,SAAA9G,EAAAvB,GACA,GAAA2J,GAAArS,EAAA0G,EAAA8J,EAAAzQ,KAAAkK,IAAAuH,EACAqB,GAAAnM,GAAA,kBAAAuD,GAAAT,aAAAsJ,QAAA7I,GAAAmI,iBAAAnI,EAAAmI,gBAAAtB,CACA,KAAAuB,IAAApI,GAGAvD,GAAA,aAAA2L,IAAAQ,EAAA9S,KAAAkK,EAAAoI,IACA3J,EAAA2J,EAIA,KAAArS,EAAAsS,EAAAtS,OAAAqS,EAAAC,IAAAtS,GAAA6S,EAAA9S,KAAAkK,EAAAoI,IAAA3J,EAAA2J,OAgCAtB,EAAA9G,EAAAvB,KASAqH,EAAA,kBAAA,CAEA,GAAAgD,IACAC,GAAA,OACAC,GAAA,MACAC,EAAA,MACAC,GAAA,MACAC,GAAA,MACAC,GAAA,MACAC,EAAA,OAKAC,EAAA,SACAC,EAAA,SAAAC,EAAA/I,GAGA,OAAA6I,GAAA7I,GAAA,IAAAzI,OAAAwR,IAOAC,EAAA,QACAC,EAAA,SAAAjJ,GAGA,IAFA,GAAAxH,GAAA,IAAA0Q,EAAA,EAAA5T,EAAA0K,EAAA1K,OAAA6T,GAAA/B,GAAA9R,EAAA,GACA8T,EAAAD,IAAA/B,EAAApH,EAAAhJ,MAAA,IAAAgJ,GACAkJ,EAAA5T,EAAA4T,IAAA,CACA,GAAAG,GAAArJ,EAAAsJ,WAAAJ,EAGA,QAAAG,GACA,IAAA,GAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IACA7Q,GAAA6P,EAAAgB,EACA,MACA,SACA,GAAAA,EAAA,GAAA,CACA7Q,GAAAwQ,EAAAF,EAAA,EAAAO,EAAA9O,SAAA,IACA,OAEA/B,GAAA2Q,EAAAC,EAAAF,GAAAlJ,EAAAzG,OAAA2P,IAGA,MAAA1Q,GAAA,KAKA+Q,EAAA,SAAA5B,EAAApI,EAAAvB,EAAAwL,EAAAC,EAAAC,EAAAvT,GACA,GAAA6J,GAAA2J,EAAAnC,EAAAC,EAAAmC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAjB,EAAA5T,EAAA8U,EAAA5R,CACA,KAEAwH,EAAAT,EAAAoI,GACA,MAAA3B,IACA,GAAA,gBAAAhG,IAAAA,EAEA,GADA2J,EAAA7D,EAAAzQ,KAAA2K,GACA2J,GAAA5C,GAAAX,EAAA/Q,KAAA2K,EAAA,UA4CA,kBAAAA,GAAA6F,SAAA8D,GAAA3C,GAAA2C,GAAA1C,GAAA0C,GAAAzC,GAAAd,EAAA/Q,KAAA2K,EAAA,aAKAA,EAAAA,EAAA6F,OAAA8B,QAhDA,IAAA3H,KAAA,GAAAA,EAAA,EAAA,EAAA,CAIA,GAAAuH,EAAA,CAKA,IADAqC,EAAAvC,EAAArH,EAAA,OACAwH,EAAAH,EAAAuC,EAAA,UAAA,KAAA,EAAArC,EAAAC,EAAA,EAAA,IAAAoC,EAAApC,KACA,IAAAC,EAAAJ,GAAAuC,EAAArC,EAAAC,EAAA,IAAA,OAAAD,EAAAC,EAAAC,EAAA,IAAAmC,EAAAnC,KACAmC,EAAA,EAAAA,EAAArC,EAAAC,EAAAC,GAKAoC,GAAA7J,EAAA,MAAA,OAAA,MAGA8J,EAAAzC,EAAAwC,EAAA,MAAA,GACAE,EAAA1C,EAAAwC,EAAA,KAAA,GACAG,EAAA3C,EAAAwC,EAAA,KAAA,GACAI,EAAAJ,EAAA,QAEArC,GAAAxH,EAAAuG,iBACAkB,EAAAzH,EAAAwG,cACAoD,EAAA5J,EAAAyG,aACAqD,EAAA9J,EAAA0G,cACAqD,EAAA/J,EAAA2G,gBACAqD,EAAAhK,EAAA4G,gBACAqD,EAAAjK,EAAA6G,oBAGA7G,IAAAwH,GAAA,GAAAA,GAAA,KAAAA,EAAA,EAAA,IAAA,KAAAsB,EAAA,EAAAtB,EAAA,GAAAA,EAAAA,GAAAsB,EAAA,EAAAtB,IACA,IAAAsB,EAAA,EAAArB,EAAA,GAAA,IAAAqB,EAAA,EAAAc,GAGA,IAAAd,EAAA,EAAAgB,GAAA,IAAAhB,EAAA,EAAAiB,GAAA,IAAAjB,EAAA,EAAAkB,GAEA,IAAAlB,EAAA,EAAAmB,GAAA,QAEAjK,GAAA,IAeA,IALAhC,IAGAgC,EAAAhC,EAAA3I,KAAAkK,EAAAoI,EAAA3H,IAEA,OAAAA,EACA,MAAA,MAGA,IADA2J,EAAA7D,EAAAzQ,KAAA2K,GACA2J,GAAAxC,EAEA,MAAA,GAAAnH,CACA,IAAA2J,GAAA3C,EAGA,MAAAhH,MAAA,GAAAA,EAAA,EAAA,EAAA,GAAAA,EAAA,MACA,IAAA2J,GAAA1C,EAEA,MAAAgC,GAAA,GAAAjJ,EAGA,IAAA,gBAAAA,GAAA,CAGA,IAAA1K,EAAAa,EAAAb,OAAAA,KACA,GAAAa,EAAAb,KAAA0K,EAEA,KAAAxF,IASA,IALArE,EAAAuC,KAAAsH,GACAkK,KAEAE,EAAAV,EACAA,GAAAD,EACAE,GAAAzC,EAAA,CAEA,IAAAgC,EAAA,EAAA5T,EAAA0K,EAAA1K,OAAA4T,EAAA5T,EAAA4T,IACAiB,EAAAZ,EAAAL,EAAAlJ,EAAAhC,EAAAwL,EAAAC,EAAAC,EAAAvT,GACA+T,EAAAxR,KAAAyR,IAAA5E,EAAA,OAAA4E,EAEA3R,GAAA0R,EAAA5U,OAAAmU,EAAA,MAAAC,EAAAQ,EAAAvS,KAAA,MAAA+R,GAAA,KAAAU,EAAA,IAAA,IAAAF,EAAAvS,KAAA,KAAA,IAAA,SAKA0O,GAAAmD,GAAAxJ,EAAA,SAAA2H,GACA,GAAAwC,GAAAZ,EAAA5B,EAAA3H,EAAAhC,EAAAwL,EAAAC,EAAAC,EAAAvT,EACAgU,KAAA5E,GAOA2E,EAAAxR,KAAAuQ,EAAAtB,GAAA,KAAA8B,EAAA,IAAA,IAAAU,KAGA3R,EAAA0R,EAAA5U,OAAAmU,EAAA,MAAAC,EAAAQ,EAAAvS,KAAA,MAAA+R,GAAA,KAAAU,EAAA,IAAA,IAAAF,EAAAvS,KAAA,KAAA,IAAA,IAIA,OADAxB,GAAAsB,MACAe,GAKA1E,GAAA4R,UAAA,SAAA3N,EAAAd,EAAA8R,GACA,GAAAU,GAAAzL,EAAAwL,EAAAG,CACA,IAAAvB,QAAAnR,KAAAA,EACA,IAAA0S,EAAA7D,EAAAzQ,KAAA4B,KAAA6P,EACA9I,EAAA/G,MACA,IAAA0S,GAAAzC,EAAA,CAEAsC,IACA,KAAA,GAAAxJ,GAAAkJ,EAAA,EAAA5T,EAAA2B,EAAA3B,OAAA4T,EAAA5T,EAAA0K,EAAA/I,EAAAiS,KAAAS,EAAA7D,EAAAzQ,KAAA2K,IAAA2J,GAAA1C,GAAA0C,GAAA3C,KAAAwC,EAAAxJ,GAAA,KAGA,GAAA+I,EACA,IAAAY,EAAA7D,EAAAzQ,KAAA0T,KAAA/B,GAGA,IAAA+B,GAAAA,EAAA,GAAA,EACA,IAAAU,EAAA,GAAAV,EAAA,KAAAA,EAAA,IAAAU,EAAAnU,OAAAyT,EAAAU,GAAA,UAEAE,IAAA1C,IACAwC,EAAAV,EAAAzT,QAAA,GAAAyT,EAAAA,EAAAxR,MAAA,EAAA,IAMA,OAAAgS,GAAA,IAAAvJ,KAAAA,EAAA,IAAAjI,EAAAiI,GAAAhC,EAAAwL,EAAAC,EAAA,QAKA,IAAApE,EAAA,cAAA,CACA,GAgBAgF,GAAAC,EAhBAC,EAAA7O,EAAA6O,aAIAC,GACAlC,GAAA,KACAC,GAAA,IACAkC,GAAA,IACAC,GAAA,KACAC,IAAA,KACAC,IAAA,KACAC,IAAA,KACAC,IAAA,MAOAC,EAAA,WAEA,KADAV,GAAAC,EAAA,KACApE,KAMA8E,EAAA,WAEA,IADA,GAAAhL,GAAAiL,EAAAC,EAAAC,EAAA9B,EAAAtR,EAAAuS,EAAAhV,EAAAyC,EAAAzC,OACA+U,EAAA/U,GAEA,OADA+T,EAAAtR,EAAAuR,WAAAe,IAEA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAGAA,GACA,MACA,KAAA,KAAA,IAAA,KAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAKA,MAFArK,GAAAoH,EAAArP,EAAAwB,OAAA8Q,GAAAtS,EAAAsS,GACAA,IACArK,CACA,KAAA,IAKA,IAAAA,EAAA,IAAAqK,IAAAA,EAAA/U,GAEA,GADA+T,EAAAtR,EAAAuR,WAAAe,GACAhB,EAAA,GAGA0B,QACA,IAAA,IAAA1B,EAKA,OADAA,EAAAtR,EAAAuR,aAAAe,IAEA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,KAAA,IAAA,KAAA,IAAA,KAAA,IAAA,KAEArK,GAAAwK,EAAAnB,GACAgB,GACA,MACA,KAAA,KAKA,IADAY,IAAAZ,EACAa,EAAAb,EAAA,EAAAA,EAAAa,EAAAb,IACAhB,EAAAtR,EAAAuR,WAAAe,GAGAhB,GAAA,IAAAA,GAAA,IAAAA,GAAA,IAAAA,GAAA,KAAAA,GAAA,IAAAA,GAAA,IAEA0B,GAIA/K,IAAAuK,EAAA,KAAAxS,EAAAR,MAAA0T,EAAAZ,GACA,MACA,SAEAU,QAEA,CACA,GAAA,IAAA1B,EAGA,KAKA,KAHAA,EAAAtR,EAAAuR,WAAAe,GACAY,EAAAZ,EAEAhB,GAAA,IAAA,IAAAA,GAAA,IAAAA,GACAA,EAAAtR,EAAAuR,aAAAe,EAGArK,IAAAjI,EAAAR,MAAA0T,EAAAZ,GAGA,GAAA,IAAAtS,EAAAuR,WAAAe,GAGA,MADAA,KACArK,CAGA+K,IACA,SASA,GAPAE,EAAAZ,EAEA,IAAAhB,IACA8B,GAAA,EACA9B,EAAAtR,EAAAuR,aAAAe,IAGAhB,GAAA,IAAAA,GAAA,GAAA,CAQA,IANA,IAAAA,IAAAA,EAAAtR,EAAAuR,WAAAe,EAAA,GAAAhB,GAAA,IAAAA,GAAA,KAEA0B,IAEAI,GAAA,EAEAd,EAAA/U,IAAA+T,EAAAtR,EAAAuR,WAAAe,GAAAhB,GAAA,IAAAA,GAAA,IAAAgB,KAGA,GAAA,IAAAtS,EAAAuR,WAAAe,GAAA,CAGA,IAFAa,IAAAb,EAEAa,EAAA5V,IAAA+T,EAAAtR,EAAAuR,WAAA4B,GAAA7B,GAAA,IAAAA,GAAA,IAAA6B,KACAA,GAAAb,GAEAU,IAEAV,EAAAa,EAKA,GADA7B,EAAAtR,EAAAuR,WAAAe,GACA,KAAAhB,GAAA,IAAAA,EAAA,CAQA,IAPAA,EAAAtR,EAAAuR,aAAAe,GAGA,IAAAhB,GAAA,IAAAA,GACAgB,IAGAa,EAAAb,EAAAa,EAAA5V,IAAA+T,EAAAtR,EAAAuR,WAAA4B,GAAA7B,GAAA,IAAAA,GAAA,IAAA6B,KACAA,GAAAb,GAEAU,IAEAV,EAAAa,EAGA,OAAAnT,EAAAR,MAAA0T,EAAAZ,GAOA,GAJAc,GACAJ,IAGA,QAAAhT,EAAAR,MAAA8S,EAAAA,EAAA,GAEA,MADAA,IAAA,GACA,CACA,IAAA,SAAAtS,EAAAR,MAAA8S,EAAAA,EAAA,GAEA,MADAA,IAAA,GACA,CACA,IAAA,QAAAtS,EAAAR,MAAA8S,EAAAA,EAAA,GAEA,MADAA,IAAA,EACA,IAGAU,KAKA,MAAA,KAIAK,EAAA,SAAApL,GACA,GAAAkK,GAAAmB,CAKA,IAJA,KAAArL,GAEA+K,IAEA,gBAAA/K,GAAA,CACA,GAAA,MAAAoH,EAAApH,EAAAzG,OAAA,GAAAyG,EAAA,IAEA,MAAAA,GAAAzI,MAAA,EAGA,IAAA,KAAAyI,EAAA,CAGA,IADAkK,KAEAlK,EAAAgL,IAEA,KAAAhL,EAHAqL,IAAAA,GAAA,GASAA,IACA,KAAArL,GACAA,EAAAgL,IACA,KAAAhL,GAEA+K,KAIAA,KAIA,KAAA/K,GACA+K,IAEAb,EAAAxR,KAAA0S,EAAApL,GAEA,OAAAkK,GACA,GAAA,KAAAlK,EAAA,CAGA,IADAkK,KAEAlK,EAAAgL,IAEA,KAAAhL,EAHAqL,IAAAA,GAAA,GAQAA,IACA,KAAArL,GACAA,EAAAgL,IACA,KAAAhL,GAEA+K,KAIAA,KAMA,KAAA/K,GAAA,gBAAAA,IAAA,MAAAoH,EAAApH,EAAAzG,OAAA,GAAAyG,EAAA,KAAA,KAAAgL,KACAD,IAEAb,EAAAlK,EAAAzI,MAAA,IAAA6T,EAAAJ,IAEA,OAAAd,GAGAa,IAEA,MAAA/K,IAIAsL,EAAA,SAAAvT,EAAA4P,EAAA3J,GACA,GAAAmM,GAAAoB,EAAAxT,EAAA4P,EAAA3J,EACAmM,KAAA5E,QACAxN,GAAA4P,GAEA5P,EAAA4P,GAAAwC,GAOAoB,EAAA,SAAAxT,EAAA4P,EAAA3J,GACA,GAAA1I,GAAA0K,EAAAjI,EAAA4P,EACA,IAAA,gBAAA3H,IAAAA,EAIA,GAAA8F,EAAAzQ,KAAA2K,IAAAkH,EACA,IAAA5R,EAAA0K,EAAA1K,OAAAA,KACAgW,EAAAtL,EAAA1K,EAAA0I,OAGAqI,GAAArG,EAAA,SAAA2H,GACA2D,EAAAtL,EAAA2H,EAAA3J,IAIA,OAAAA,GAAA3I,KAAA0C,EAAA4P,EAAA3H,GAIAlM,GAAAiC,MAAA,SAAAgC,EAAAiG,GACA,GAAAxF,GAAAwH,CAUA,OATAqK,GAAA,EACAC,EAAA,GAAAvS,EACAS,EAAA4S,EAAAJ,KAEA,KAAAA,KACAD,IAGAV,EAAAC,EAAA,KACAtM,GAAA8H,EAAAzQ,KAAA2I,IAAA8I,EAAAyE,GAAAvL,KAAAA,EAAA,IAAAxH,EAAAwH,GAAA,GAAAhC,GAAAxF,IAMA,MADA1E,GAAA,aAAAqR,EACArR,EA31BA,GAAA0X,GAAA,kBAAAxX,IAAAA,EAAAC,IAGAmU,GACAqD,YAAA,EACAlM,QAAA,GAIAmM,EAAAtD,QAAAtU,KAAAA,IAAAA,EAAA6X,UAAA7X,EAMA0B,EAAA4S,QAAAjU,UAAAA,QAAAG,KACAsX,EAAAF,GAAAtD,QAAArU,KAAAA,IAAAA,EAAA4X,UAAA,gBAAAvX,IAAAA,CA80BA,KA50BAwX,GAAAA,EAAA,SAAAA,GAAAA,EAAA,SAAAA,GAAAA,EAAA,OAAAA,IACApW,EAAAoW,GA20BAF,IAAAF,EAEArG,EAAA3P,EAAAkW,OACA,CAEA,GAAAvF,GAAA3Q,EAAAqW,KACAC,EAAAtW,EAAA,MACAuW,GAAA,EAEAC,EAAA7G,EAAA3P,EAAAA,EAAA,OAGAyW,WAAA,WAOA,MANAF,KACAA,GAAA,EACAvW,EAAAqW,KAAA1F,EACA3Q,EAAA,MAAAsW,EACA3F,EAAA2F,EAAA,MAEAE,IAIAxW,GAAAqW,MACA9V,MAAAiW,EAAAjW,MACA2P,UAAAsG,EAAAtG,WAKA8F,GACAxX,EAAA,WACA,MAAAgY,OAGA3W,KAAAf,QHs+CGe,KAAKf,KAAuB,mBAAXF,QAAyBA,OAAyB,mBAATC,MAAuBA,KAAyB,mBAAXF,QAAyBA,gBAErH+X,GAAG,SAASnX,EAAQhB,EAAOD,GIl2EjC,QAAAqY,KACA,KAAA,IAAAjX,OAAA,mCAEA,QAAAkX,KACA,KAAA,IAAAlX,OAAA,qCAsBA,QAAAmX,GAAAC,GACA,GAAAC,IAAAxO,WAEA,MAAAA,YAAAuO,EAAA,EAGA,KAAAC,IAAAJ,IAAAI,IAAAxO,WAEA,MADAwO,GAAAxO,WACAA,WAAAuO,EAAA,EAEA,KAEA,MAAAC,GAAAD,EAAA,GACA,MAAA7X,GACA,IAEA,MAAA8X,GAAAlX,KAAA,KAAAiX,EAAA,GACA,MAAA7X,GAEA,MAAA8X,GAAAlX,KAAAf,KAAAgY,EAAA,KAMA,QAAAE,GAAAC,GACA,GAAAC,IAAAC,aAEA,MAAAA,cAAAF,EAGA,KAAAC,IAAAN,IAAAM,IAAAC,aAEA,MADAD,GAAAC,aACAA,aAAAF,EAEA,KAEA,MAAAC,GAAAD,GACA,MAAAhY,GACA,IAEA,MAAAiY,GAAArX,KAAA,KAAAoX,GACA,MAAAhY,GAGA,MAAAiY,GAAArX,KAAAf,KAAAmY,KAYA,QAAAG,KACAC,GAAAC,IAGAD,GAAA,EACAC,EAAAxX,OACA2I,EAAA6O,EAAA7S,OAAAgE,GAEA8O,KAEA9O,EAAA3I,QACA0X,KAIA,QAAAA,KACA,IAAAH,EAAA,CAGA,GAAAI,GAAAZ,EAAAO,EACAC,IAAA,CAGA,KADA,GAAApU,GAAAwF,EAAA3I,OACAmD,GAAA,CAGA,IAFAqU,EAAA7O,EACAA,OACA8O,EAAAtU,GACAqU,GACAA,EAAAC,GAAAG,KAGAH,MACAtU,EAAAwF,EAAA3I,OAEAwX,EAAA,KACAD,GAAA,EACAL,EAAAS,IAiBA,QAAAE,GAAAb,EAAAc,GACA9Y,KAAAgY,IAAAA,EACAhY,KAAA8Y,MAAAA,EAYA,QAAArO,MAhKA,GAOAwN,GACAG,EARA9Q,EAAA7H,EAAAD,YAgBA,WACA,IAEAyY,EADA,kBAAAxO,YACAA,WAEAoO,EAEA,MAAA1X,GACA8X,EAAAJ,EAEA,IAEAO,EADA,kBAAAC,cACAA,aAEAP,EAEA,MAAA3X,GACAiY,EAAAN,KAuDA,IAEAU,GAFA7O,KACA4O,GAAA,EAEAE,IAyCAnR,GAAAY,SAAA,SAAA8P,GACA,GAAAxT,GAAA,GAAA0J,OAAA7D,UAAArJ,OAAA,EACA,IAAAqJ,UAAArJ,OAAA,EACA,IAAA,GAAAT,GAAA,EAAAA,EAAA8J,UAAArJ,OAAAT,IACAiE,EAAAjE,EAAA,GAAA8J,UAAA9J,EAGAoJ,GAAAvF,KAAA,GAAAyU,GAAAb,EAAAxT,IACA,IAAAmF,EAAA3I,QAAAuX,GACAR,EAAAW,IASAG,EAAAjT,UAAAgT,IAAA,WACA5Y,KAAAgY,IAAAe,MAAA,KAAA/Y,KAAA8Y,QAEAxR,EAAA0R,MAAA,UACA1R,EAAA2R,SAAA,EACA3R,EAAA4R,OACA5R,EAAA6R,QACA7R,EAAA8R,QAAA,GACA9R,EAAA+R,YAIA/R,EAAAgS,GAAA7O,EACAnD,EAAAiS,YAAA9O,EACAnD,EAAAkS,KAAA/O,EACAnD,EAAAmS,IAAAhP,EACAnD,EAAAoS,eAAAjP,EACAnD,EAAAqS,mBAAAlP,EACAnD,EAAAsS,KAAAnP,EACAnD,EAAAuS,gBAAApP,EACAnD,EAAAwS,oBAAArP,EAEAnD,EAAAyS,UAAA,SAAA/I,GAAA,UAEA1J,EAAA0S,QAAA,SAAAhJ,GACA,KAAA,IAAApQ,OAAA,qCAGA0G,EAAA2S,IAAA,WAAA,MAAA,KACA3S,EAAA4S,MAAA,SAAAC,GACA,KAAA,IAAAvZ,OAAA,mCAEA0G,EAAA8S,MAAA,WAAA,MAAA,SJg3EMC,GAAG,SAAS5Z,EAAQhB,EAAOD,GKvhFjC,QAAA8a,KACAta,KAAAua,UACAva,KAAAwa,KAAAxU,OAAAyU,OAAA,MAXA,GAAAC,GAAAja,EAAA,UACAsQ,EAAA/K,OAAAJ,UAAAwN,cAgBAkH,GAAAK,UAAA,SAAAC,EAAAC,GAEA,IAAA,GADAC,GAAA,GAAAR,GACA/Z,EAAA,EAAA4D,EAAAyW,EAAA5Z,OAAAT,EAAA4D,EAAA5D,IACAua,EAAAC,IAAAH,EAAAra,GAAAsa,EAEA,OAAAC,IASAR,EAAA1U,UAAA8N,KAAA,WACA,MAAA1N,QAAAgV,oBAAAhb,KAAAwa,MAAAxZ,QAQAsZ,EAAA1U,UAAAmV,IAAA,SAAAE,EAAAJ,GACA,GAAAK,GAAAR,EAAAS,YAAAF,GACAG,EAAArK,EAAAhQ,KAAAf,KAAAwa,KAAAU,GACAG,EAAArb,KAAAua,OAAAvZ,MACAoa,KAAAP,GACA7a,KAAAua,OAAAnW,KAAA6W,GAEAG,IACApb,KAAAwa,KAAAU,GAAAG,IASAf,EAAA1U,UAAAmL,IAAA,SAAAkK,GACA,GAAAC,GAAAR,EAAAS,YAAAF,EACA,OAAAlK,GAAAhQ,KAAAf,KAAAwa,KAAAU,IAQAZ,EAAA1U,UAAAzD,QAAA,SAAA8Y,GACA,GAAAC,GAAAR,EAAAS,YAAAF,EACA,IAAAlK,EAAAhQ,KAAAf,KAAAwa,KAAAU,GACA,MAAAlb,MAAAwa,KAAAU,EAEA,MAAA,IAAAta,OAAA,IAAAqa,EAAA,yBAQAX,EAAA1U,UAAA0V,GAAA,SAAAC,GACA,GAAAA,GAAA,GAAAA,EAAAvb,KAAAua,OAAAvZ,OACA,MAAAhB,MAAAua,OAAAgB,EAEA,MAAA,IAAA3a,OAAA,yBAAA2a,IAQAjB,EAAA1U,UAAA4V,QAAA,WACA,MAAAxb,MAAAua,OAAAtX,SAGAzD,EAAA8a,SAAAA,IL0iFGmB,SAAS,KAAKC,GAAG,SAASjb,EAAQhB,EAAOD,GM7kF5C,QAAAmc,GAAAC,GACA,MAAAA,GAAA,IACAA,GAAA,GAAA,GACAA,GAAA,GAAA,EASA,QAAAC,GAAAD,GACA,GAAAE,GAAA,KAAA,EAAAF,GACAG,EAAAH,GAAA,CACA,OAAAE,IACAC,EACAA,EAhDA,GAAAC,GAAAvb,EAAA,YAcAwb,EAAA,EAGAC,EAAA,GAAAD,EAGAE,EAAAD,EAAA,EAGAE,EAAAF,CA+BA1c,GAAA6c,OAAA,SAAAT,GACA,GACAU,GADAC,EAAA,GAGAC,EAAAb,EAAAC,EAEA,GACAU,GAAAE,EAAAL,EACAK,KAAAP,EACAO,EAAA,IAGAF,GAAAF,GAEAG,GAAAP,EAAAK,OAAAC,SACAE,EAAA,EAEA,OAAAD,IAOA/c,EAAAid,OAAA,SAAAxB,EAAAyB,EAAAC,GACA,GAGAC,GAAAN,EAHAO,EAAA5B,EAAAja,OACAkD,EAAA,EACAK,EAAA,CAGA,GAAA,CACA,GAAAmY,GAAAG,EACA,KAAA,IAAAjc,OAAA,6CAIA,IADA0b,EAAAN,EAAAS,OAAAxB,EAAAjG,WAAA0H,MACAJ,OACA,KAAA,IAAA1b,OAAA,yBAAAqa,EAAAhW,OAAAyX,EAAA,GAGAE,MAAAN,EAAAF,GACAE,GAAAH,EACAjY,GAAAoY,GAAA/X,EACAA,GAAA0X,QACAW,EAEAD,GAAAjR,MAAAmQ,EAAA3X,GACAyY,EAAAG,KAAAJ,KNqpFGK,WAAW,IAAI7I,GAAG,SAASzT,EAAQhB,EAAOD,GOxxF7C,GAAAwd,GAAA,mEAAAta,MAAA,GAKAlD,GAAA6c,OAAA,SAAAY,GACA,GAAA,GAAAA,GAAAA,EAAAD,EAAAhc,OACA,MAAAgc,GAAAC,EAEA,MAAA,IAAA/W,WAAA,6BAAA+W,IAOAzd,EAAAid,OAAA,SAAA1H,GACA,GAAAmI,GAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,IAEAC,EAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,EAGA,OAAAT,IAAAnI,GAAAA,GAAAoI,EACApI,EAAAmI,EAIAE,GAAArI,GAAAA,GAAAsI,EACAtI,EAAAqI,EAAAM,EAIAJ,GAAAvI,GAAAA,GAAAwI,EACAxI,EAAAuI,EAAAK,EAIA5I,GAAAyI,EACA,GAIAzI,GAAA0I,EACA,YPuyFMnJ,GAAG,SAAS7T,EAAQhB,EAAOD,GQ70FjC,QAAAoe,GAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAUA,GAAAC,GAAAtO,KAAAkD,OAAA+K,EAAAD,GAAA,GAAAA,EACAO,EAAAH,EAAAF,EAAAC,EAAAG,IAAA,EACA,OAAA,KAAAC,EAEAD,EAEAC,EAAA,EAEAN,EAAAK,EAAA,EAEAP,EAAAO,EAAAL,EAAAC,EAAAC,EAAAC,EAAAC,GAKAA,GAAA1e,EAAA6e,kBACAP,EAAAE,EAAAhd,OAAA8c,KAEAK,EAKAA,EAAAN,EAAA,EAEAD,EAAAC,EAAAM,EAAAJ,EAAAC,EAAAC,EAAAC,GAIAA,GAAA1e,EAAA6e,kBACAF,EAEAN,EAAA,KAAAA,EA1DAre,EAAA8e,qBAAA,EACA9e,EAAA6e,kBAAA,EAgFA7e,EAAA+e,OAAA,SAAAR,EAAAC,EAAAC,EAAAC,GACA,GAAA,IAAAF,EAAAhd,OACA,QAGA,IAAA4T,GAAAgJ,KAAAI,EAAAhd,OAAA+c,EAAAC,EACAC,EAAAC,GAAA1e,EAAA8e,qBACA,IAAA1J,EAAA,EACA,QAMA,MAAAA,EAAA,GAAA,GACA,IAAAqJ,EAAAD,EAAApJ,GAAAoJ,EAAApJ,EAAA,IAAA,MAGAA,CAGA,OAAAA,SRw2FMR,IAAI,SAAS3T,EAAQhB,EAAOD,GS17FlC,QAAAgf,GAAAC,EAAAhX,EAAAiX,GACA,GAAAC,GAAAF,EAAAhX,EACAgX,GAAAhX,GAAAgX,EAAAC,GACAD,EAAAC,GAAAC,EAWA,QAAAC,GAAAC,EAAAC,GACA,MAAAjP,MAAAkP,MAAAF,EAAAhP,KAAAC,UAAAgP,EAAAD,IAeA,QAAAG,GAAAP,EAAAQ,EAAAne,EAAAZ,GAKA,GAAAY,EAAAZ,EAAA,CAYA,GAAAgf,GAAAN,EAAA9d,EAAAZ,GACAK,EAAAO,EAAA,CAEA0d,GAAAC,EAAAS,EAAAhf,EASA,KAAA,GARAif,GAAAV,EAAAve,GAQA+G,EAAAnG,EAAAmG,EAAA/G,EAAA+G,IACAgY,EAAAR,EAAAxX,GAAAkY,IAAA,IACA5e,GAAA,EACAie,EAAAC,EAAAle,EAAA0G,GAIAuX,GAAAC,EAAAle,EAAA,EAAA0G,EACA,IAAAmY,GAAA7e,EAAA,CAIAye,GAAAP,EAAAQ,EAAAne,EAAAse,EAAA,GACAJ,EAAAP,EAAAQ,EAAAG,EAAA,EAAAlf,IAYAV,EAAA6f,UAAA,SAAAZ,EAAAQ,GACAD,EAAAP,EAAAQ,EAAA,EAAAR,EAAAzd,OAAA,STy9FMse,IAAI,SAAS7e,EAAQhB,EAAOD,GU5jGlC,QAAA+f,GAAAC,GACA,GAAAC,GAAAD,CAKA,OAJA,gBAAAA,KACAC,EAAAlI,KAAA9V,MAAA+d,EAAAjd,QAAA,WAAA,MAGA,MAAAkd,EAAAC,SACA,GAAAC,GAAAF,GACA,GAAAG,GAAAH,GAoQA,QAAAG,GAAAJ,GACA,GAAAC,GAAAD,CACA,iBAAAA,KACAC,EAAAlI,KAAA9V,MAAA+d,EAAAjd,QAAA,WAAA,KAGA,IAAA6W,GAAAsB,EAAAmF,OAAAJ,EAAA,WACAK,EAAApF,EAAAmF,OAAAJ,EAAA,WAGAM,EAAArF,EAAAmF,OAAAJ,EAAA,YACAO,EAAAtF,EAAAmF,OAAAJ,EAAA,aAAA,MACAQ,EAAAvF,EAAAmF,OAAAJ,EAAA,iBAAA,MACAS,EAAAxF,EAAAmF,OAAAJ,EAAA,YACAU,EAAAzF,EAAAmF,OAAAJ,EAAA,OAAA,KAIA,IAAArG,GAAApZ,KAAAogB,SACA,KAAA,IAAAxf,OAAA,wBAAAwY,EAGA0G,GAAAA,EACAjd,IAAAuE,QAIAvE,IAAA6X,EAAA2F,WAKAxd,IAAA,SAAAY,GACA,MAAAuc,IAAAtF,EAAA4F,WAAAN,IAAAtF,EAAA4F,WAAA7c,GACAiX,EAAA6F,SAAAP,EAAAvc,GACAA,IAOAzD,KAAAwgB,OAAAlG,EAAAK,UAAAoF,EAAAld,IAAAuE,SAAA,GACApH,KAAAygB,SAAAnG,EAAAK,UAAAmF,GAAA,GAEA9f,KAAAggB,WAAAA,EACAhgB,KAAAigB,eAAAA,EACAjgB,KAAA0gB,UAAAR,EACAlgB,KAAAmgB,KAAAA,EA8EA,QAAAQ,KACA3gB,KAAA4gB,cAAA,EACA5gB,KAAA6gB,gBAAA,EACA7gB,KAAAyD,OAAA,KACAzD,KAAA8gB,aAAA,KACA9gB,KAAA+gB,eAAA,KACA/gB,KAAAgR,KAAA,KAyZA,QAAA2O,GAAAH,GACA,GAAAC,GAAAD,CACA,iBAAAA,KACAC,EAAAlI,KAAA9V,MAAA+d,EAAAjd,QAAA,WAAA,KAGA,IAAA6W,GAAAsB,EAAAmF,OAAAJ,EAAA,WACAC,EAAAhF,EAAAmF,OAAAJ,EAAA,WAEA,IAAArG,GAAApZ,KAAAogB,SACA,KAAA,IAAAxf,OAAA,wBAAAwY,EAGApZ,MAAAygB,SAAA,GAAAnG,GACAta,KAAAwgB,OAAA,GAAAlG,EAEA,IAAA0G,IACApe,QACAqe,OAAA,EAEAjhB,MAAAkhB,UAAAxB,EAAA7c,IAAA,SAAAse,GACA,GAAAA,EAAAC,IAGA,KAAA,IAAAxgB,OAAA,qDAEA,IAAAygB,GAAA3G,EAAAmF,OAAAsB,EAAA,UACAG,EAAA5G,EAAAmF,OAAAwB,EAAA,QACAE,EAAA7G,EAAAmF,OAAAwB,EAAA,SAEA,IAAAC,EAAAN,EAAApe,MACA0e,IAAAN,EAAApe,MAAA2e,EAAAP,EAAAC,OACA,KAAA,IAAArgB,OAAA,uDAIA,OAFAogB,GAAAK,GAGAG,iBAGAZ,cAAAU,EAAA,EACAT,gBAAAU,EAAA,GAEAE,SAAA,GAAAlC,GAAA7E,EAAAmF,OAAAsB,EAAA,WA11BA,GAAAzG,GAAAja,EAAA,UACAihB,EAAAjhB,EAAA,mBACA6Z,EAAA7Z,EAAA,eAAA6Z,SACAqH,EAAAlhB,EAAA,gBACA4e,EAAA5e,EAAA,gBAAA4e,SAaAE,GAAAqC,cAAA,SAAApC,GACA,MAAAI,GAAAgC,cAAApC,IAMAD,EAAA3Z,UAAAwa,SAAA,EAgCAb,EAAA3Z,UAAAic,oBAAA,KACA7b,OAAA8b,eAAAvC,EAAA3Z,UAAA,sBACAkR,IAAA,WAKA,MAJA9W,MAAA6hB,qBACA7hB,KAAA+hB,eAAA/hB,KAAA0gB,UAAA1gB,KAAAggB,YAGAhgB,KAAA6hB,uBAIAtC,EAAA3Z,UAAAoc,mBAAA,KACAhc,OAAA8b,eAAAvC,EAAA3Z,UAAA,qBACAkR,IAAA,WAKA,MAJA9W,MAAAgiB,oBACAhiB,KAAA+hB,eAAA/hB,KAAA0gB,UAAA1gB,KAAAggB,YAGAhgB,KAAAgiB,sBAIAzC,EAAA3Z,UAAAqc,wBACA,SAAAhH,EAAArG,GACA,GAAApU,GAAAya,EAAAhW,OAAA2P,EACA,OAAA,MAAApU,GAAA,MAAAA,GAQA+e,EAAA3Z,UAAAmc,eACA,SAAA9G,EAAAiH,GACA,KAAA,IAAAthB,OAAA,6CAGA2e,EAAA4C,gBAAA,EACA5C,EAAA6C,eAAA,EAEA7C,EAAAjB,qBAAA,EACAiB,EAAAlB,kBAAA,EAkBAkB,EAAA3Z,UAAAyc,YACA,SAAAC,EAAAC,EAAAC,GACA,GAGAtC,GAHApP,EAAAyR,GAAA,KACAE,EAAAD,GAAAjD,EAAA4C,eAGA,QAAAM,GACA,IAAAlD,GAAA4C,gBACAjC,EAAAlgB,KAAA0iB,kBACA,MACA,KAAAnD,GAAA6C,eACAlC,EAAAlgB,KAAA2iB,iBACA,MACA,SACA,KAAA,IAAA/hB,OAAA,+BAGA,GAAAof,GAAAhgB,KAAAggB,UACAE,GAAArd,IAAA,SAAA+f,GACA,GAAAnf,GAAA,OAAAmf,EAAAnf,OAAA,KAAAzD,KAAAygB,SAAAnF,GAAAsH,EAAAnf,OAIA,OAHA,OAAAA,GAAA,MAAAuc,IACAvc,EAAAiX,EAAArX,KAAA2c,EAAAvc,KAGAA,OAAAA,EACAmd,cAAAgC,EAAAhC,cACAC,gBAAA+B,EAAA/B,gBACAC,aAAA8B,EAAA9B,aACAC,eAAA6B,EAAA7B,eACA/P,KAAA,OAAA4R,EAAA5R,KAAA,KAAAhR,KAAAwgB,OAAAlF,GAAAsH,EAAA5R,QAEAhR,MAAA+R,QAAAuQ,EAAAxR,IAsBAyO,EAAA3Z,UAAAid,yBACA,SAAAC,GACA,GAAAlgB,GAAA8X,EAAAmF,OAAAiD,EAAA,QAMAC,GACAtf,OAAAiX,EAAAmF,OAAAiD,EAAA,UACAhC,aAAAle,EACAme,eAAArG,EAAAmF,OAAAiD,EAAA,SAAA,GAMA,IAHA,MAAA9iB,KAAAggB,aACA+C,EAAAtf,OAAAiX,EAAA6F,SAAAvgB,KAAAggB,WAAA+C,EAAAtf,UAEAzD,KAAAygB,SAAA1P,IAAAgS,EAAAtf,QACA,QAEAsf,GAAAtf,OAAAzD,KAAAygB,SAAAte,QAAA4gB,EAAAtf,OAEA,IAAAyc,MAEAtL,EAAA5U,KAAAgjB,aAAAD,EACA/iB,KAAA2iB,kBACA,eACA,iBACAjI,EAAAuI,2BACAvB,EAAArD,kBACA,IAAAzJ,GAAA,EAAA,CACA,GAAAgO,GAAA5iB,KAAA2iB,kBAAA/N,EAEA,IAAApS,SAAAsgB,EAAA7B,OAOA,IANA,GAAAH,GAAA8B,EAAA9B,aAMA8B,GAAAA,EAAA9B,eAAAA,GACAZ,EAAA9b,MACAxB,KAAA8X,EAAAmF,OAAA+C,EAAA,gBAAA,MACA3B,OAAAvG,EAAAmF,OAAA+C,EAAA,kBAAA,MACAM,WAAAxI,EAAAmF,OAAA+C,EAAA,sBAAA,QAGAA,EAAA5iB,KAAA2iB,oBAAA/N,OASA,KANA,GAAAmM,GAAA6B,EAAA7B,eAMA6B,GACAA,EAAA9B,eAAAle,GACAggB,EAAA7B,gBAAAA,GACAb,EAAA9b,MACAxB,KAAA8X,EAAAmF,OAAA+C,EAAA,gBAAA,MACA3B,OAAAvG,EAAAmF,OAAA+C,EAAA,kBAAA,MACAM,WAAAxI,EAAAmF,OAAA+C,EAAA,sBAAA,QAGAA,EAAA5iB,KAAA2iB,oBAAA/N,GAKA,MAAAsL,IAGA1gB,EAAA+f,kBAAAA,EAmFAK,EAAAha,UAAAI,OAAAyU,OAAA8E,EAAA3Z,WACAga,EAAAha,UAAA6b,SAAAlC,EASAK,EAAAgC,cACA,SAAApC,GACA,GAAA2D,GAAAnd,OAAAyU,OAAAmF,EAAAha,WAEAma,EAAAoD,EAAA3C,OAAAlG,EAAAK,UAAA6E,EAAAgB,OAAAhF,WAAA,GACAsE,EAAAqD,EAAA1C,SAAAnG,EAAAK,UAAA6E,EAAAiB,SAAAjF,WAAA,EACA2H,GAAAnD,WAAAR,EAAA4D,YACAD,EAAAlD,eAAAT,EAAA6D,wBAAAF,EAAA1C,SAAAjF,UACA2H,EAAAnD,YACAmD,EAAAhD,KAAAX,EAAA8D,KAWA,KAAA,GAJAC,GAAA/D,EAAAkB,UAAAlF,UAAAvY,QACAugB,EAAAL,EAAAtB,uBACA4B,EAAAN,EAAAnB,sBAEAzhB,EAAA,EAAAS,EAAAuiB,EAAAviB,OAAAT,EAAAS,EAAAT,IAAA,CACA,GAAAmjB,GAAAH,EAAAhjB,GACAojB,EAAA,GAAAhD,EACAgD,GAAA/C,cAAA8C,EAAA9C,cACA+C,EAAA9C,gBAAA6C,EAAA7C,gBAEA6C,EAAAjgB,SACAkgB,EAAAlgB,OAAAqc,EAAA3d,QAAAuhB,EAAAjgB,QACAkgB,EAAA7C,aAAA4C,EAAA5C,aACA6C,EAAA5C,eAAA2C,EAAA3C,eAEA2C,EAAA1S,OACA2S,EAAA3S,KAAA+O,EAAA5d,QAAAuhB,EAAA1S,OAGAyS,EAAArf,KAAAuf,IAGAH,EAAApf,KAAAuf,GAKA,MAFAtE,GAAA8D,EAAAnB,mBAAAtH,EAAAuI,4BAEAE,GAMAvD,EAAAha,UAAAwa,SAAA,EAKApa,OAAA8b,eAAAlC,EAAAha,UAAA,WACAkR,IAAA,WACA,MAAA9W,MAAAygB,SAAAjF,UAAA3Y,IAAA,SAAAse,GACA,MAAA,OAAAnhB,KAAAggB,WAAAtF,EAAArX,KAAArD,KAAAggB,WAAAmB,GAAAA,GACAnhB,SAqBA4f,EAAAha,UAAAmc,eACA,SAAA9G,EAAAiH,GAeA,IAdA,GAYAU,GAAA5d,EAAA4e,EAAAC,EAAAnY,EAZAkV,EAAA,EACAkD,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAljB,EAAAia,EAAAja,OACA4T,EAAA,EACAuP,KACAxF,KACAyF,KACAb,KAGA3O,EAAA5T,GACA,GAAA,MAAAia,EAAAhW,OAAA2P,GACAgM,IACAhM,IACAkP,EAAA,MAEA,IAAA,MAAA7I,EAAAhW,OAAA2P,GACAA,QAEA,CASA,IARAgO,EAAA,GAAAjC,GACAiC,EAAAhC,cAAAA,EAOAiD,EAAAjP,EAAAiP,EAAA7iB,IACAhB,KAAAiiB,wBAAAhH,EAAA4I,GADAA,KAQA,GAHA7e,EAAAiW,EAAAhY,MAAA2R,EAAAiP,GAEAD,EAAAO,EAAAnf,GAEA4P,GAAA5P,EAAAhE,WACA,CAEA,IADA4iB,KACAhP,EAAAiP,GACAlC,EAAAlF,OAAAxB,EAAArG,EAAA+J,GACAjT,EAAAiT,EAAAjT,MACAkJ,EAAA+J,EAAA7B,KACA8G,EAAAxf,KAAAsH,EAGA,IAAA,IAAAkY,EAAA5iB,OACA,KAAA,IAAAJ,OAAA,yCAGA,IAAA,IAAAgjB,EAAA5iB,OACA,KAAA,IAAAJ,OAAA,yCAGAujB,GAAAnf,GAAA4e,EAIAhB,EAAA/B,gBAAAiD,EAAAF,EAAA,GACAE,EAAAlB,EAAA/B,gBAEA+C,EAAA5iB,OAAA,IAEA4hB,EAAAnf,OAAAwgB,EAAAL,EAAA,GACAK,GAAAL,EAAA,GAGAhB,EAAA9B,aAAAiD,EAAAH,EAAA,GACAG,EAAAnB,EAAA9B,aAEA8B,EAAA9B,cAAA,EAGA8B,EAAA7B,eAAAiD,EAAAJ,EAAA,GACAI,EAAApB,EAAA7B,eAEA6C,EAAA5iB,OAAA,IAEA4hB,EAAA5R,KAAAkT,EAAAN,EAAA,GACAM,GAAAN,EAAA,KAIAL,EAAAnf,KAAAwe,GACA,gBAAAA,GAAA9B,cACAsD,EAAAhgB,KAAAwe,GAKAvD,EAAAkE,EAAA7I,EAAA2J,qCACArkB,KAAA6hB,oBAAA0B,EAEAlE,EAAA+E,EAAA1J,EAAAuI,4BACAjjB,KAAAgiB,mBAAAoC,GAOAxE,EAAAha,UAAAod,aACA,SAAAjF,EAAAuG,EAAAC,EACAC,EAAAC,EAAAvG,GAMA,GAAAH,EAAAwG,IAAA,EACA,KAAA,IAAAre,WAAA,gDACA6X,EAAAwG,GAEA,IAAAxG,EAAAyG,GAAA,EACA,KAAA,IAAAte,WAAA,kDACA6X,EAAAyG,GAGA,OAAA9C,GAAAnD,OAAAR,EAAAuG,EAAAG,EAAAvG,IAOA0B,EAAAha,UAAA8e,mBACA,WACA,IAAA,GAAA9P,GAAA,EAAAA,EAAA5U,KAAA0iB,mBAAA1hB,SAAA4T,EAAA,CACA,GAAAgO,GAAA5iB,KAAA0iB,mBAAA9N,EAMA,IAAAA,EAAA,EAAA5U,KAAA0iB,mBAAA1hB,OAAA,CACA,GAAA2jB,GAAA3kB,KAAA0iB,mBAAA9N,EAAA,EAEA,IAAAgO,EAAAhC,gBAAA+D,EAAA/D,cAAA,CACAgC,EAAAgC,oBAAAD,EAAA9D,gBAAA,CACA,WAKA+B,EAAAgC,oBAAAC,EAAAA,IAwBAjF,EAAAha,UAAAkf,oBACA,SAAAhC,GACA,GAAAC,IACAnC,cAAAlG,EAAAmF,OAAAiD,EAAA,QACAjC,gBAAAnG,EAAAmF,OAAAiD,EAAA,WAGAlO,EAAA5U,KAAAgjB,aACAD,EACA/iB,KAAA0iB,mBACA,gBACA,kBACAhI,EAAA2J,oCACA3J,EAAAmF,OAAAiD,EAAA,OAAAvD,EAAAjB,sBAGA,IAAA1J,GAAA,EAAA,CACA,GAAAgO,GAAA5iB,KAAA0iB,mBAAA9N,EAEA,IAAAgO,EAAAhC,gBAAAmC,EAAAnC,cAAA,CACA,GAAAnd,GAAAiX,EAAAmF,OAAA+C,EAAA,SAAA,KACA,QAAAnf,IACAA,EAAAzD,KAAAygB,SAAAnF,GAAA7X,GACA,MAAAzD,KAAAggB,aACAvc,EAAAiX,EAAArX,KAAArD,KAAAggB,WAAAvc,IAGA,IAAAuN,GAAA0J,EAAAmF,OAAA+C,EAAA,OAAA,KAIA,OAHA,QAAA5R,IACAA,EAAAhR,KAAAwgB,OAAAlF,GAAAtK,KAGAvN,OAAAA,EACAb,KAAA8X,EAAAmF,OAAA+C,EAAA,eAAA,MACA3B,OAAAvG,EAAAmF,OAAA+C,EAAA,iBAAA,MACA5R,KAAAA,IAKA,OACAvN,OAAA,KACAb,KAAA,KACAqe,OAAA,KACAjQ,KAAA,OAQA4O,EAAAha,UAAAmf,wBACA,WACA,QAAA/kB,KAAAigB,iBAGAjgB,KAAAigB,eAAAjf,QAAAhB,KAAAygB,SAAA/M,SACA1T,KAAAigB,eAAA+E,KAAA,SAAAC,GAAA,MAAA,OAAAA,MAQArF,EAAAha,UAAAsf,iBACA,SAAAC,EAAAC,GACA,IAAAplB,KAAAigB,eACA,MAAA;AAOA,GAJA,MAAAjgB,KAAAggB,aACAmF,EAAAzK,EAAA6F,SAAAvgB,KAAAggB,WAAAmF,IAGAnlB,KAAAygB,SAAA1P,IAAAoU,GACA,MAAAnlB,MAAAigB,eAAAjgB,KAAAygB,SAAAte,QAAAgjB,GAGA,IAAA/D,EACA,IAAA,MAAAphB,KAAAggB,aACAoB,EAAA1G,EAAA2K,SAAArlB,KAAAggB,aAAA,CAKA,GAAAsF,GAAAH,EAAA5iB,QAAA,aAAA,GACA,IAAA,QAAA6e,EAAAmE,QACAvlB,KAAAygB,SAAA1P,IAAAuU,GACA,MAAAtlB,MAAAigB,eAAAjgB,KAAAygB,SAAAte,QAAAmjB,GAGA,MAAAlE,EAAAoE,MAAA,KAAApE,EAAAoE,OACAxlB,KAAAygB,SAAA1P,IAAA,IAAAoU,GACA,MAAAnlB,MAAAigB,eAAAjgB,KAAAygB,SAAAte,QAAA,IAAAgjB,IAQA,GAAAC,EACA,MAAA,KAGA,MAAA,IAAAxkB,OAAA,IAAAukB,EAAA,+BAuBAvF,EAAAha,UAAA6f,qBACA,SAAA3C,GACA,GAAArf,GAAAiX,EAAAmF,OAAAiD,EAAA,SAIA,IAHA,MAAA9iB,KAAAggB,aACAvc,EAAAiX,EAAA6F,SAAAvgB,KAAAggB,WAAAvc,KAEAzD,KAAAygB,SAAA1P,IAAAtN,GACA,OACAb,KAAA,KACAqe,OAAA,KACAiC,WAAA,KAGAzf,GAAAzD,KAAAygB,SAAAte,QAAAsB,EAEA,IAAAsf,IACAtf,OAAAA,EACAqd,aAAApG,EAAAmF,OAAAiD,EAAA,QACA/B,eAAArG,EAAAmF,OAAAiD,EAAA,WAGAlO,EAAA5U,KAAAgjB,aACAD,EACA/iB,KAAA2iB,kBACA,eACA,iBACAjI,EAAAuI,2BACAvI,EAAAmF,OAAAiD,EAAA,OAAAvD,EAAAjB,sBAGA,IAAA1J,GAAA,EAAA,CACA,GAAAgO,GAAA5iB,KAAA2iB,kBAAA/N,EAEA,IAAAgO,EAAAnf,SAAAsf,EAAAtf,OACA,OACAb,KAAA8X,EAAAmF,OAAA+C,EAAA,gBAAA,MACA3B,OAAAvG,EAAAmF,OAAA+C,EAAA,kBAAA,MACAM,WAAAxI,EAAAmF,OAAA+C,EAAA,sBAAA,OAKA,OACAhgB,KAAA,KACAqe,OAAA,KACAiC,WAAA,OAIA1jB,EAAAogB,uBAAAA,EA+FAD,EAAA/Z,UAAAI,OAAAyU,OAAA8E,EAAA3Z,WACA+Z,EAAA/Z,UAAA4E,YAAA+U,EAKAI,EAAA/Z,UAAAwa,SAAA,EAKApa,OAAA8b,eAAAnC,EAAA/Z,UAAA,WACAkR,IAAA,WAEA,IAAA,GADAgJ,MACAvf,EAAA,EAAAA,EAAAP,KAAAkhB,UAAAlgB,OAAAT,IACA,IAAA,GAAA0G,GAAA,EAAAA,EAAAjH,KAAAkhB,UAAA3gB,GAAAkhB,SAAA3B,QAAA9e,OAAAiG,IACA6Y,EAAA1b,KAAApE,KAAAkhB,UAAA3gB,GAAAkhB,SAAA3B,QAAA7Y,GAGA,OAAA6Y,MAmBAH,EAAA/Z,UAAAkf,oBACA,SAAAhC,GACA,GAAAC,IACAnC,cAAAlG,EAAAmF,OAAAiD,EAAA,QACAjC,gBAAAnG,EAAAmF,OAAAiD,EAAA,WAKA4C,EAAAhE,EAAAnD,OAAAwE,EAAA/iB,KAAAkhB,UACA,SAAA6B,EAAA4C,GACA,GAAAvH,GAAA2E,EAAAnC,cAAA+E,EAAAnE,gBAAAZ,aACA,OAAAxC,GACAA,EAGA2E,EAAAlC,gBACA8E,EAAAnE,gBAAAX,kBAEA8E,EAAA3lB,KAAAkhB,UAAAwE,EAEA,OAAAC,GASAA,EAAAlE,SAAAqD,qBACAliB,KAAAmgB,EAAAnC,eACA+E,EAAAnE,gBAAAZ,cAAA,GACAK,OAAA8B,EAAAlC,iBACA8E,EAAAnE,gBAAAZ,gBAAAmC,EAAAnC,cACA+E,EAAAnE,gBAAAX,gBAAA,EACA,GACA+E,KAAA9C,EAAA8C,QAdAniB,OAAA,KACAb,KAAA,KACAqe,OAAA,KACAjQ,KAAA,OAmBA2O,EAAA/Z,UAAAmf,wBACA,WACA,MAAA/kB,MAAAkhB,UAAA2E,MAAA,SAAA1E,GACA,MAAAA,GAAAM,SAAAsD,6BASApF,EAAA/Z,UAAAsf,iBACA,SAAAC,EAAAC,GACA,IAAA,GAAA7kB,GAAA,EAAAA,EAAAP,KAAAkhB,UAAAlgB,OAAAT,IAAA,CACA,GAAAolB,GAAA3lB,KAAAkhB,UAAA3gB,GAEAulB,EAAAH,EAAAlE,SAAAyD,iBAAAC,GAAA,EACA,IAAAW,EACA,MAAAA,GAGA,GAAAV,EACA,MAAA,KAGA,MAAA,IAAAxkB,OAAA,IAAAukB,EAAA,+BAkBAxF,EAAA/Z,UAAA6f,qBACA,SAAA3C,GACA,IAAA,GAAAviB,GAAA,EAAAA,EAAAP,KAAAkhB,UAAAlgB,OAAAT,IAAA,CACA,GAAAolB,GAAA3lB,KAAAkhB,UAAA3gB,EAIA,IAAAolB,EAAAlE,SAAA3B,QAAA3d,QAAAuY,EAAAmF,OAAAiD,EAAA,gBAAA,CAGA,GAAAiD,GAAAJ,EAAAlE,SAAAgE,qBAAA3C,EACA,IAAAiD,EAAA,CACA,GAAAC,IACApjB,KAAAmjB,EAAAnjB,MACA+iB,EAAAnE,gBAAAZ,cAAA,GACAK,OAAA8E,EAAA9E,QACA0E,EAAAnE,gBAAAZ,gBAAAmF,EAAAnjB,KACA+iB,EAAAnE,gBAAAX,gBAAA,EACA,GAEA,OAAAmF,KAIA,OACApjB,KAAA,KACAqe,OAAA,OASAtB,EAAA/Z,UAAAmc,eACA,SAAA9G,EAAAiH,GACAliB,KAAA6hB,uBACA7hB,KAAAgiB,qBACA,KAAA,GAAAzhB,GAAA,EAAAA,EAAAP,KAAAkhB,UAAAlgB,OAAAT,IAGA,IAAA,GAFAolB,GAAA3lB,KAAAkhB,UAAA3gB,GACA0lB,EAAAN,EAAAlE,SAAAiB,mBACAzb,EAAA,EAAAA,EAAAgf,EAAAjlB,OAAAiG,IAAA,CACA,GAAA2b,GAAAqD,EAAAhf,GAEAxD,EAAAkiB,EAAAlE,SAAAhB,SAAAnF,GAAAsH,EAAAnf,OACA,QAAAkiB,EAAAlE,SAAAzB,aACAvc,EAAAiX,EAAArX,KAAAsiB,EAAAlE,SAAAzB,WAAAvc,IAEAzD,KAAAygB,SAAA1F,IAAAtX,GACAA,EAAAzD,KAAAygB,SAAAte,QAAAsB,EAEA,IAAAuN,GAAA2U,EAAAlE,SAAAjB,OAAAlF,GAAAsH,EAAA5R,KACAhR,MAAAwgB,OAAAzF,IAAA/J,GACAA,EAAAhR,KAAAwgB,OAAAre,QAAA6O,EAMA,IAAAkV,IACAziB,OAAAA,EACAmd,cAAAgC,EAAAhC,eACA+E,EAAAnE,gBAAAZ,cAAA,GACAC,gBAAA+B,EAAA/B,iBACA8E,EAAAnE,gBAAAZ,gBAAAgC,EAAAhC,cACA+E,EAAAnE,gBAAAX,gBAAA,EACA,GACAC,aAAA8B,EAAA9B,aACAC,eAAA6B,EAAA7B,eACA/P,KAAAA,EAGAhR,MAAA6hB,oBAAAzd,KAAA8hB,GACA,gBAAAA,GAAApF,cACA9gB,KAAAgiB,mBAAA5d,KAAA8hB,GAKA7G,EAAArf,KAAA6hB,oBAAAnH,EAAA2J,qCACAhF,EAAArf,KAAAgiB,mBAAAtH,EAAAuI,6BAGAzjB,EAAAmgB,yBAAAA,IV4kGGwG,cAAc,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,eAAe,GAAG7K,SAAS,KAAKtH,IAAI,SAAS1T,EAAQhB,EAAOD,GWpnIpH,QAAAqgB,GAAAiD,EAAAyD,EAAAC,GACA,GAAAD,IAAAzD,GACA,MAAAA,GAAAyD,EACA,IAAA,IAAAlc,UAAArJ,OACA,MAAAwlB,EAEA,MAAA,IAAA5lB,OAAA,IAAA2lB,EAAA,6BAQA,QAAAlB,GAAAoB,GACA,GAAA3kB,GAAA2kB,EAAA3kB,MAAA4kB,EACA,OAAA5kB,IAIAyjB,OAAAzjB,EAAA,GACA6kB,KAAA7kB,EAAA,GACA8kB,KAAA9kB,EAAA,GACA+kB,KAAA/kB,EAAA,GACA0jB,KAAA1jB,EAAA,IAPA,KAYA,QAAAglB,GAAAC,GACA,GAAA3F,GAAA,EAiBA,OAhBA2F,GAAAxB,SACAnE,GAAA2F,EAAAxB,OAAA,KAEAnE,GAAA,KACA2F,EAAAJ,OACAvF,GAAA2F,EAAAJ,KAAA,KAEAI,EAAAH,OACAxF,GAAA2F,EAAAH,MAEAG,EAAAF,OACAzF,GAAA,IAAA2F,EAAAF,MAEAE,EAAAvB,OACApE,GAAA2F,EAAAvB,MAEApE,EAeA,QAAAf,GAAA2G,GACA,GAAAxB,GAAAwB,EACA5F,EAAAiE,EAAA2B,EACA,IAAA5F,EAAA,CACA,IAAAA,EAAAoE,KACA,MAAAwB,EAEAxB,GAAApE,EAAAoE,KAKA,IAAA,GAAAyB,GAHA3G,EAAA9gB,EAAA8gB,WAAAkF,GAEAnjB,EAAAmjB,EAAA9iB,MAAA,OACAwkB,EAAA,EAAA3mB,EAAA8B,EAAArB,OAAA,EAAAT,GAAA,EAAAA,IACA0mB,EAAA5kB,EAAA9B,GACA,MAAA0mB,EACA5kB,EAAA8kB,OAAA5mB,EAAA,GACA,OAAA0mB,EACAC,IACAA,EAAA,IACA,KAAAD,GAIA5kB,EAAA8kB,OAAA5mB,EAAA,EAAA2mB,GACAA,EAAA,IAEA7kB,EAAA8kB,OAAA5mB,EAAA,GACA2mB,KAUA,OANA1B,GAAAnjB,EAAAgB,KAAA,KAEA,KAAAmiB,IACAA,EAAAlF,EAAA,IAAA,KAGAc,GACAA,EAAAoE,KAAAA,EACAsB,EAAA1F,IAEAoE,EAoBA,QAAAniB,GAAA+jB,EAAAJ,GACA,KAAAI,IACAA,EAAA,KAEA,KAAAJ,IACAA,EAAA,IAEA,IAAAK,GAAAhC,EAAA2B,GACAM,EAAAjC,EAAA+B,EAMA,IALAE,IACAF,EAAAE,EAAA9B,MAAA,KAIA6B,IAAAA,EAAA9B,OAIA,MAHA+B,KACAD,EAAA9B,OAAA+B,EAAA/B,QAEAuB,EAAAO,EAGA,IAAAA,GAAAL,EAAAllB,MAAAylB,GACA,MAAAP,EAIA,IAAAM,IAAAA,EAAAV,OAAAU,EAAA9B,KAEA,MADA8B,GAAAV,KAAAI,EACAF,EAAAQ,EAGA,IAAAE,GAAA,MAAAR,EAAA/hB,OAAA,GACA+hB,EACA3G,EAAA+G,EAAA7kB,QAAA,OAAA,IAAA,IAAAykB,EAEA,OAAAM,IACAA,EAAA9B,KAAAgC,EACAV,EAAAQ,IAEAE,EAcA,QAAAjH,GAAA6G,EAAAJ,GACA,KAAAI,IACAA,EAAA,KAGAA,EAAAA,EAAA7kB,QAAA,MAAA,GAOA,KADA,GAAAklB,GAAA,EACA,IAAAT,EAAA7kB,QAAAilB,EAAA,MAAA,CACA,GAAAxS,GAAAwS,EAAAtgB,YAAA,IACA,IAAA8N,EAAA,EACA,MAAAoS,EAOA,IADAI,EAAAA,EAAAnkB,MAAA,EAAA2R,GACAwS,EAAAtlB,MAAA,qBACA,MAAAklB,KAGAS,EAIA,MAAAvZ,OAAAuZ,EAAA,GAAApkB,KAAA,OAAA2jB,EAAAU,OAAAN,EAAApmB,OAAA,GASA,QAAA2mB,GAAAxG,GACA,MAAAA,GAYA,QAAAhG,GAAAF,GACA,MAAA2M,GAAA3M,GACA,IAAAA,EAGAA,EAIA,QAAA4M,GAAA5M,GACA,MAAA2M,GAAA3M,GACAA,EAAAhY,MAAA,GAGAgY,EAIA,QAAA2M,GAAAzG,GACA,IAAAA,EACA,OAAA,CAGA,IAAAngB,GAAAmgB,EAAAngB,MAEA,IAAAA,EAAA,EACA,OAAA,CAGA,IAAA,KAAAmgB,EAAAnM,WAAAhU,EAAA,IACA,KAAAmgB,EAAAnM,WAAAhU,EAAA,IACA,MAAAmgB,EAAAnM,WAAAhU,EAAA,IACA,MAAAmgB,EAAAnM,WAAAhU,EAAA,IACA,MAAAmgB,EAAAnM,WAAAhU,EAAA,IACA,MAAAmgB,EAAAnM,WAAAhU,EAAA,IACA,MAAAmgB,EAAAnM,WAAAhU,EAAA,IACA,KAAAmgB,EAAAnM,WAAAhU,EAAA,IACA,KAAAmgB,EAAAnM,WAAAhU,EAAA,GACA,OAAA,CAGA,KAAA,GAAAT,GAAAS,EAAA,GAAAT,GAAA,EAAAA,IACA,GAAA,KAAA4gB,EAAAnM,WAAAzU,GACA,OAAA,CAIA,QAAA,EAWA,QAAA0iB,GAAA6E,EAAAC,EAAAC,GACA,GAAA5J,GAAA0J,EAAArkB,OAAAskB,EAAAtkB,MACA,OAAA,KAAA2a,EACAA,GAGAA,EAAA0J,EAAAhH,aAAAiH,EAAAjH,aACA,IAAA1C,EACAA,GAGAA,EAAA0J,EAAA/G,eAAAgH,EAAAhH,eACA,IAAA3C,GAAA4J,EACA5J,GAGAA,EAAA0J,EAAAjH,gBAAAkH,EAAAlH,gBACA,IAAAzC,EACAA,GAGAA,EAAA0J,EAAAlH,cAAAmH,EAAAnH,cACA,IAAAxC,EACAA,EAGA0J,EAAA9W,KAAA+W,EAAA/W,SAaA,QAAAqT,GAAAyD,EAAAC,EAAAE,GACA,GAAA7J,GAAA0J,EAAAlH,cAAAmH,EAAAnH,aACA,OAAA,KAAAxC,EACAA,GAGAA,EAAA0J,EAAAjH,gBAAAkH,EAAAlH,gBACA,IAAAzC,GAAA6J,EACA7J,GAGAA,EAAA0J,EAAArkB,OAAAskB,EAAAtkB,OACA,IAAA2a,EACAA,GAGAA,EAAA0J,EAAAhH,aAAAiH,EAAAjH,aACA,IAAA1C,EACAA,GAGAA,EAAA0J,EAAA/G,eAAAgH,EAAAhH,eACA,IAAA3C,EACAA,EAGA0J,EAAA9W,KAAA+W,EAAA/W,SAIA,QAAAkX,GAAAC,EAAAC,GACA,MAAAD,KAAAC,EACA,EAGAD,EAAAC,EACA,KAUA,QAAAC,GAAAP,EAAAC,GACA,GAAA3J,GAAA0J,EAAAlH,cAAAmH,EAAAnH,aACA,OAAA,KAAAxC,EACAA,GAGAA,EAAA0J,EAAAjH,gBAAAkH,EAAAlH,gBACA,IAAAzC,EACAA,GAGAA,EAAA8J,EAAAJ,EAAArkB,OAAAskB,EAAAtkB,QACA,IAAA2a,EACAA,GAGAA,EAAA0J,EAAAhH,aAAAiH,EAAAjH,aACA,IAAA1C,EACAA,GAGAA,EAAA0J,EAAA/G,eAAAgH,EAAAhH,eACA,IAAA3C,EACAA,EAGA8J,EAAAJ,EAAA9W,KAAA+W,EAAA/W,UApYAxR,EAAAqgB,OAAAA,CAEA,IAAA6G,GAAA,iEACAa,EAAA,eAeA/nB,GAAA6lB,SAAAA,EAsBA7lB,EAAAsnB,YAAAA,EAwDAtnB,EAAA6gB,UAAAA,EA2DA7gB,EAAA6D,KAAAA,EAEA7D,EAAA8gB,WAAA,SAAA0G,GACA,MAAA,MAAAA,EAAA/hB,OAAA,MAAA+hB,EAAAllB,MAAA4kB,IAyCAlnB,EAAA+gB,SAAAA,CAEA,IAAA+H,GAAA,WACA,GAAAjjB,GAAAW,OAAAyU,OAAA,KACA,SAAA,aAAApV,MAuBA7F,GAAA2b,YAAAmN,EAAAX,EAAAxM,EASA3b,EAAAqoB,cAAAS,EAAAX,EAAAE,EAsEAroB,EAAAyjB,2BAAAA,EAuCAzjB,EAAA6kB,oCAAAA,EA8CA7kB,EAAA6oB,oCAAAA,OXwoIMhU,IAAI,SAAS5T,EAAQhB,EAAOD,GAClC6K,UAAU,GAAG,GAAG,GAAG0O,MAAMvZ,EAAQ6K,aAC9Bke,IAAM,IAAIC,IAAI,SAAS/nB,EAAQhB,EAAOD,IY1iJzC,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,mBAAA,cAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,EAAAV,EAAA,eAEAS,EAAAunB,eAAAtnB,EAAAD,EAAAG,aAEArB,KAAA,SAAAqB,GACA,OACAqnB,UAAA,SAAAC,GACA,GAAA9mB,MACA+mB,EAAA,EAEA,iBAAAD,IAAA,gBAAAA,GAAAC,eACAA,EAAAD,EAAAC,aAIA,KADA,GAAAC,GAAAxe,UAAAye,OACAD,GAAAhnB,EAAAb,OAAA4nB,GAAAC,EAAA,WAAA,CAGA,IAAA,GADArkB,GAAA,GAAA0J,OAAA2a,EAAA,UAAA7nB,QACAT,EAAA,EAAAA,EAAAiE,EAAAxD,SAAAT,EACAiE,EAAAjE,GAAAsoB,EAAA,UAAAtoB,EAEA,iCAAAwoB,KAAAF,EAAA5iB,YACApE,EAAAuC,KAAA,GAAA/C,IAAA+B,aAAA4lB,OAAAC,IAAAzmB,OAAAgC,KAAAA,KAEA3C,EAAAuC,KAAA,GAAA/C,IAAAmD,KAAAA,IAGA,KACAqkB,EAAAA,EAAAK,OACA,MAAA/oB,GACA,OAGA,MAAA0B,SZgjJG4C,WAAa,KAAK0kB,IAAI,SAAS1oB,EAAQhB,EAAOD,GACjD6K,UAAU,GAAG,GAAG,GAAG0O,MAAMvZ,EAAQ6K,aAC9Bke,IAAM,IAAIa,IAAI,SAAS3oB,EAAQhB,EAAOD,Ia3lJzC,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,kBAAA,aAAA,cAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,EAAAV,EAAA,sCAAAA,EAAA,eAEAS,EAAAmoB,cAAAloB,EAAAD,EAAAooB,WAAApoB,EAAAue,UAAAve,EAAAG,aAEArB,KAAA,SAAAspB,EAAAjoB,GACA,YAQA,SAAAkoB,GAAAnI,GACA,MAAA,IAAAxS,SAAA,SAAA5D,EAAAwD,GACA,GAAAgb,GAAA,GAAAC,eACAD,GAAAE,KAAA,MAAAtI,GACAoI,EAAAG,QAAAnb,EACAgb,EAAAI,mBAAA,WACA,IAAAJ,EAAAK,aACAL,EAAAM,QAAA,KAAAN,EAAAM,OAAA,KACA,YAAA1I,EAAAsG,OAAA,EAAA,IAAA8B,EAAAO,aACA/e,EAAAwe,EAAAO,cAEAvb,EAAA,GAAA5N,OAAA,gBAAA4oB,EAAAM,OAAA,eAAA1I,MAIAoI,EAAAQ,SAYA,QAAAC,GAAAC,GACA,GAAA,mBAAArqB,SAAAA,OAAAsqB,KACA,MAAAtqB,QAAAsqB,KAAAD,EAEA,MAAA,IAAAtpB,OAAA,kEAIA,QAAAwpB,GAAAC,GACA,GAAA,mBAAA9S,OAAAA,KAAA9V,MACA,MAAA8V,MAAA9V,MAAA4oB,EAEA,MAAA,IAAAzpB,OAAA,iEAIA,QAAA0pB,GAAA7mB,EAAAF,GAkBA,IAAA,GAjBAgnB,IAEA,2DAEA,uCAEA,wEAEA,mFAEA,8DAEAtmB,EAAAR,EAAAf,MAAA,MAGA7B,EAAA,GACA2pB,EAAA3a,KAAA4a,IAAAlnB,EAAA,IACAhD,EAAA,EAAAA,EAAAiqB,IAAAjqB,EAAA,CAEA,GAAAqC,GAAAqB,EAAAV,EAAAhD,EAAA,GACAmqB,EAAA9nB,EAAAT,QAAA,KAKA,IAJAuoB,GAAA,IACA9nB,EAAAA,EAAA8kB,OAAA,EAAAgD,IAGA9nB,EAAA,CACA/B,EAAA+B,EAAA/B,CAEA,KAAA,GADAsD,GAAAomB,EAAAvpB,OACA4T,EAAA,EAAAA,EAAAzQ,EAAAyQ,IAAA,CACA,GAAA+V,GAAAJ,EAAA3V,GAAAtS,KAAAzB,EACA,IAAA8pB,GAAAA,EAAA,GACA,MAAAA,GAAA,MAQA,QAAAC,KACA,GAAA,kBAAA5kB,QAAA8b,gBAAA,kBAAA9b,QAAAyU,OACA,KAAA,IAAA7Z,OAAA,mDAIA,QAAAiqB,GAAApmB,GACA,GAAA,gBAAAA,GACA,KAAA,IAAAyB,WAAA,oCACA,IAAA,gBAAAzB,GAAAnB,SACA,KAAA,IAAA4C,WAAA,kCACA,IAAA,gBAAAzB,GAAAlB,YACAkB,EAAAlB,WAAA,IAAA,GACAkB,EAAAlB,WAAA,EACA,KAAA,IAAA2C,WAAA,+CACA,IAAA,gBAAAzB,GAAAjB,cACAiB,EAAAjB,aAAA,IAAA,GACAiB,EAAAjB,aAAA,EACA,KAAA,IAAA0C,WAAA,qDAEA,QAAA,EAGA,QAAA4kB,GAAArnB,GAKA,IAJA,GACAsnB,GACAC,EAFAC,EAAA,8CAIAD,EAAAC,EAAA3oB,KAAAmB,IACAsnB,EAAAC,EAAA,EAEA,IAAAD,EACA,MAAAA,EAEA,MAAA,IAAAnqB,OAAA,8BAIA,QAAAsqB,GAAAzmB,EAAA0mB,EAAAC,GACA,MAAA,IAAAxc,SAAA,SAAA5D,EAAAwD,GACA,GAAA6c,GAAAF,EAAArG,qBACAliB,KAAA6B,EAAAlB,WACA0d,OAAAxc,EAAAjB,cAGA,IAAA6nB,EAAA5nB,OAAA,CAEA,GAAA6nB,GAAAH,EAAAjG,iBAAAmG,EAAA5nB,OACA6nB,KACAF,EAAAC,EAAA5nB,QAAA6nB,GAGAtgB,EAEA,GAAA3J,IACA+B,aAAAioB,EAAAra,MAAAvM,EAAArB,aACAoB,KAAAC,EAAAD,KACAlB,SAAA+nB,EAAA5nB,OACAF,WAAA8nB,EAAAzoB,KACAY,aAAA6nB,EAAApK,cAGAzS,GAAA,GAAA5N,OAAA,wEAcA,MAAA,SAAAyoB,GAAAV,GACA,MAAA3oB,gBAAAqpB,IAGAV,EAAAA,MAEA3oB,KAAAorB,YAAAzC,EAAAyC,gBACAprB,KAAAurB,uBAAA5C,EAAA4C,2BAEAvrB,KAAAwrB,KAAA7C,EAAA6C,MAAAjC,EAEAvpB,KAAAiqB,MAAAtB,EAAAwB,MAAAF,EAEAjqB,KAAAyrB,KAAA,SAAA1oB,GACA,MAAA,IAAA6L,SAAA,SAAA5D,EAAAwD,GACA,GAAAkd,GAAA,UAAA3oB,EAAA2kB,OAAA,EAAA,EACA,IAAA1nB,KAAAorB,YAAAroB,GACAiI,EAAAhL,KAAAorB,YAAAroB,QACA,IAAA4lB,EAAAgD,UAAAD,EACAld,EAAA,GAAA5N,OAAA,qDAEA,IAAA8qB,EAAA,CAGA,GAAAE,GACA,+CACA9pB,EAAAiB,EAAAjB,MAAA8pB,EACA,IAAA9pB,EAAA,CACA,GAAA+pB,GAAA/pB,EAAA,GAAAd,OACA8qB,EAAA/oB,EAAA2kB,OAAAmE,GACApoB,EAAAzD,KAAAiqB,MAAA6B,EACA9rB,MAAAorB,YAAAroB,GAAAU,EACAuH,EAAAvH,OAEA+K,GAAA,GAAA5N,OAAA,8DAEA,CACA,GAAAmrB,GAAA/rB,KAAAwrB,KAAAzoB,GAAAipB,OAAA,OAEAhsB,MAAAorB,YAAAroB,GAAAgpB,EACAA,EAAA9hB,KAAAe,EAAAwD,KAGAyd,KAAAjsB,QAWAA,KAAAksB,sBAAA,SAAAC,EAAAC,GACA,MAAA,IAAAxd,SAAA,SAAA5D,GACA,GAAAhL,KAAAurB,uBAAAY,GACAnhB,EAAAhL,KAAAurB,uBAAAY,QACA,CACA,GAAAE,GAAA,GAAAzd,SAAA,SAAA5D,EAAAwD,GACA,MAAAxO,MAAAyrB,KAAAU,GAAAliB,KAAA,SAAAqiB,GACA,gBAAAA,KACAA,EAAAlC,EAAAkC,EAAA/pB,QAAA,WAAA,MAEA,mBAAA+pB,GAAAtM,aACAsM,EAAAtM,WAAAoM,GAGAphB,EAAA,GAAAse,GAAA/J,kBAAA+M,KACA9d,IACAyd,KAAAjsB,MACAA,MAAAurB,uBAAAY,GAAAE,EACArhB,EAAAqhB,KAEAJ,KAAAjsB,QAUAA,KAAAusB,SAAA,SAAA9nB,GACA,MAAA,IAAAmK,SAAA,SAAA5D,EAAAwD,GACAxO,KAAAwsB,kBAAA/nB,GAAAwF,KAAA,SAAAwiB,GACA,QAAAC,KACA1hB,EAAAyhB,GAGAzsB,KAAA2sB,iBAAAF,GACAxiB,KAAAe,EAAA0hB,GAEA,SAAAA,IACAT,KAAAjsB,MAAAwO,IACAyd,KAAAjsB,QASAA,KAAA2sB,iBAAA,SAAAloB,GACA,MAAA,IAAAmK,SAAA,SAAA5D,EAAAwD,GACAqc,EAAApmB,GACAzE,KAAAyrB,KAAAhnB,EAAAnB,UAAA2G,KAAA,SAAAxG,GACA,GAAAF,GAAAkB,EAAAlB,WACAC,EAAAiB,EAAAjB,aACAopB,EAAAtC,EAAA7mB,EAAAF,EAAAC,EAGAwH,GADA4hB,EACA,GAAAvrB,IACA+B,aAAAwpB,EACApoB,KAAAC,EAAAD,KACAlB,SAAAmB,EAAAnB,SACAC,WAAAA,EACAC,aAAAA,IAGAiB,IAEA+J,GAAA,SAAAA,IACAyd,KAAAjsB,aASAA,KAAAwsB,kBAAA,SAAA/nB,GACA,MAAA,IAAAmK,SAAA,SAAA5D,EAAAwD,GACAoc,IACAC,EAAApmB,EAEA,IAAA2mB,GAAAprB,KAAAorB,YACA9nB,EAAAmB,EAAAnB,QACAtD,MAAAyrB,KAAAnoB,GAAA2G,KAAA,SAAAxG,GACA,GAAA0oB,GAAArB,EAAArnB,GACAioB,EAAA,UAAAS,EAAAzE,OAAA,EAAA,GACA0E,EAAA9oB,EAAA6B,UAAA,EAAA7B,EAAAwD,YAAA,KAAA,EAMA,OAJA,MAAAqlB,EAAA,IAAAT,GAAA,sBAAA3C,KAAAoD,KACAA,EAAAC,EAAAD,GAGAnsB,KAAAksB,sBAAAC,EAAAC,GACAniB,KAAA,SAAAkhB,GACA,MAAAD,GAAAzmB,EAAA0mB,EAAAC,GACAnhB,KAAAe,GAAA,SAAA,WACAA,EAAAvG,QAGAwnB,KAAAjsB,MAAAwO,GAAA,SAAAA,IACAyd,KAAAjsB,UA5JA,GAAAqpB,GAAAV,Qb6vJGkE,qCAAqC,GAAGpoB,WAAa,KAAKqoB,IAAI,SAASrsB,EAAQhB,EAAOD,Gcj7JzF0O,MAAAH,UACAG,MAAAH,QAAA,SAAAnE,GACA,MAAA,mBAAA5D,OAAAJ,UAAAK,SAAAlF,KAAA6I,KAIA,mBAAAgF,UACArH,WAAAsH,WAKAE,SAAAnJ,UAAAqmB,OACAld,SAAAnJ,UAAAqmB,KAAA,SAAAc,GACA,GAAA,kBAAA/sB,MACA,KAAA,IAAAkG,WAAA,uEAGA,IAAA4c,GAAA5U,MAAAtI,UAAA3C,MAAAlC,KAAAsJ,UAAA,GACA2iB,EAAAhtB,KACAitB,EAAA,aAEAC,EAAA,WACA,MAAAF,GAAAjU,MAAA/Y,eAAAitB,IAAAF,EAAA/sB,KAAA+sB,EACAjK,EAAAnd,OAAAuI,MAAAtI,UAAA3C,MAAAlC,KAAAsJ,aAMA,OAHA4iB,GAAArnB,UAAA5F,KAAA4F,UACAsnB,EAAAtnB,UAAA,GAAAqnB,GAEAC,IAKAhf,MAAAtI,UAAA/C,MACAqL,MAAAtI,UAAA/C,IAAA,SAAA6G,EAAAyjB,GACA,GAAA,SAAAntB,MAAA,OAAAA,KACA,KAAA,IAAAkG,WAAA,8BAEA,IAEAknB,GAFAC,EAAArnB,OAAAhG,MACAmE,EAAAkpB,EAAArsB,SAAA,CAEA,IAAA,kBAAA0I,GACA,KAAA,IAAAxD,WAAAwD,EAAA,qBAEAW,WAAArJ,OAAA,IACAosB,EAAAD,EAMA,KAHA,GAAAG,GAAA,GAAApf,OAAA/J,GACAgD,EAAA,EAEAA,EAAAhD,GAAA,CACA,GAAAopB,GACAC,CACArmB,KAAAkmB,KACAE,EAAAF,EAAAlmB,GACAqmB,EAAA9jB,EAAA3I,KAAAqsB,EAAAG,EAAApmB,EAAAkmB,GACAC,EAAAnmB,GAAAqmB,GAEArmB,IAGA,MAAAmmB,KAKApf,MAAAtI,UAAAjD,SACAuL,MAAAtI,UAAAjD,OAAA,SAAA+G,GACA,GAAA,SAAA1J,MAAA,OAAAA,KACA,KAAA,IAAAkG,WAAA,8BAGA,IAAA7F,GAAA2F,OAAAhG,MACAmE,EAAA9D,EAAAW,SAAA,CACA,IAAA,kBAAA0I,GACA,KAAA,IAAAxD,WAAAwD,EAAA,qBAKA,KAAA,GAFA+jB,MACAN,EAAA9iB,UAAArJ,QAAA,EAAAqJ,UAAA,GAAA,OACA9J,EAAA,EAAAA,EAAA4D,EAAA5D,IACA,GAAAA,IAAAF,GAAA,CACA,GAAAqtB,GAAArtB,EAAAE,EACAmJ,GAAA3I,KAAAosB,EAAAO,EAAAntB,EAAAF,IACAotB,EAAArpB,KAAAspB,GAKA,MAAAD,KAKAvf,MAAAtI,UAAAmM,UACA7D,MAAAtI,UAAAmM,QAAA,SAAArI,EAAAyjB,GACA,GAAAC,GACAjmB,CACA,IAAA,OAAAnH,MAAAwC,SAAAxC,KACA,KAAA,IAAAkG,WAAA,+BAGA,IAAAmnB,GAAArnB,OAAAhG,MACAmE,EAAAkpB,EAAArsB,SAAA,CACA,IAAA,kBAAA0I,GACA,KAAA,IAAAxD,WAAAwD,EAAA,qBAOA,KAJAW,UAAArJ,OAAA,IACAosB,EAAAD,GAEAhmB,EAAA,EACAA,EAAAhD,GAAA,CACA,GAAAopB,EACApmB,KAAAkmB,KACAE,EAAAF,EAAAlmB,GACAuC,EAAA3I,KAAAqsB,EAAAG,EAAApmB,EAAAkmB,IAEAlmB,Ydy7JMwmB,IAAI,SAASltB,EAAQhB,EAAOD,IepjKlC,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,cAAA,qBAAA,kBAAA,kBAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,EAAAV,EAAA,sBAAAA,EAAA,mBAAAA,EAAA,mBAEAS,EAAAjB,WAAAkB,EAAAD,EAAAE,iBAAAF,EAAAunB,eAAAvnB,EAAAmoB,gBAEArpB,KAAA,SAAAoB,EAAAqnB,EAAAY,GA8BA,QAAAuE,GAAAC,EAAAC,GACA,GAAAC,KAWA,QATAF,EAAAC,GAAA/b,QAAA,SAAA1M,GACA,IAAA,GAAA2oB,KAAA3oB,GACAW,OAAAJ,UAAAwN,eAAArS,KAAAsE,EAAA2oB,KACAD,EAAAC,GAAA3oB,EAAA2oB,GAGA,OAAAD,KAGAA,EAGA,QAAAE,GAAAC,GACA,MAAAA,GAAArsB,OAAAqsB,EAAA,mBAGA,QAAAC,GAAAC,EAAAzrB,GACA,MAAA,kBAAAA,GACAyrB,EAAAzrB,OAAAA,GAEAyrB,EApDA,GAAAC,IACA1rB,OAAA,SAAA8B,GAEA,OAAAA,EAAArB,cAAA,IAAAjB,QAAA,uBACAsC,EAAArB,cAAA,IAAAjB,QAAA,6BACAsC,EAAArB,cAAA,IAAAjB,QAAA,0BACAsC,EAAArB,cAAA,IAAAjB,QAAA,0BAEAipB,gBAGAkD,EAAA,WACA,IAEA,KAAA,IAAA1tB,OACA,MAAAstB,GACA,MAAAA,IAuCA,QAOApX,IAAA,SAAA6R,GACA,GAAAuF,GAAAI,GACA,OAAAL,GAAAC,GAAAluB,KAAAuuB,UAAAL,EAAAvF,GAAA3oB,KAAAwuB,qBAAA7F,IAUA8F,QAAA,SAAA9F,GACAA,EAAAiF,EAAAS,EAAA1F,EACA,IAAAuF,GAAAI,IACAzsB,EAAAosB,EAAAC,GAAA9sB,EAAAK,MAAAysB,GAAAzF,EAAAC,UAAAC,EACA,OAAAwF,GAAAtsB,EAAA8mB,EAAAhmB,SAUA4rB,UAAA,SAAA7sB,EAAAinB,GACAA,EAAAiF,EAAAS,EAAA1F,EACA,IAAA+F,GAAA,GAAArF,GAAAV,EACA,OAAA,IAAA/Z,SAAA,SAAA5D,GACA,GAAAojB,GAAAD,EAAA/sB,EAAAK,MAAAC,GAAAinB,EAAAhmB,OACAqI,GAAA4D,QAAAP,IAAA+f,EAAAvrB,IAAA,SAAA8rB,GACA,MAAA,IAAA/f,SAAA,SAAA5D,GACA,QAAA4jB,KACA5jB,EAAA2jB,GAGAD,EAAAnC,SAAAoC,GAAA1kB,KAAAe,EAAA4jB,GAAA,SAAAA,UAGA3C,KAAAjsB,QASAwuB,qBAAA,SAAA7F,GACAA,EAAAiF,EAAAS,EAAA1F,EACA,IAAAkG,GAAApG,EAAAC,UAAAC,EAIA,OAHA,kBAAAA,GAAAhmB,SACAksB,EAAAA,EAAAlsB,OAAAgmB,EAAAhmB,SAEAiM,QAAA5D,QAAA6jB,IAYAC,WAAA,SAAAC,EAAArlB,EAAAslB,EAAA7B,GACA,GAAA,kBAAA4B,GACA,KAAA,IAAAnuB,OAAA,wCACA,IAAA,kBAAAmuB,GAAAE,uBAEA,MAAAF,EAGA,IAAAG,GAAA,WACA,IAEA,MADAlvB,MAAA8W,MAAA7M,KAAAP,EAAAslB,GAAA,SAAAA,GACAD,EAAAhW,MAAAoU,GAAAntB,KAAAqK,WACA,MAAAlK,GAIA,KAHA8tB,GAAA9tB,IACAH,KAAAuuB,UAAApuB,GAAA8J,KAAAP,EAAAslB,GAAA,SAAAA,GAEA7uB,IAEA8rB,KAAAjsB,KAGA,OAFAkvB,GAAAD,uBAAAF,EAEAG,GASAC,aAAA,SAAAJ,GACA,GAAA,kBAAAA,GACA,KAAA,IAAAnuB,OAAA,2CACA,OAAA,kBAAAmuB,GAAAE,uBACAF,EAAAE,uBAGAF,GAYAK,OAAA,SAAAhB,EAAAhN,EAAAiO,EAAAC,GACA,MAAA,IAAA1gB,SAAA,SAAA5D,EAAAwD,GACA,GAAAgb,GAAA,GAAAC,eAeA,IAdAD,EAAAG,QAAAnb,EACAgb,EAAAI,mBAAA,WACA,IAAAJ,EAAAK,aACAL,EAAAM,QAAA,KAAAN,EAAAM,OAAA,IACA9e,EAAAwe,EAAAO,cAEAvb,EAAA,GAAA5N,OAAA,WAAAwgB,EAAA,wBAAAoI,EAAAM,WAIAN,EAAAE,KAAA,OAAAtI,GAGAoI,EAAA+F,iBAAA,eAAA,oBACAD,GAAA,gBAAAA,GAAAE,QAAA,CACA,GAAAA,GAAAF,EAAAE,OACA,KAAA,GAAAC,KAAAD,GACAxpB,OAAAJ,UAAAwN,eAAArS,KAAAyuB,EAAAC,IACAjG,EAAA+F,iBAAAE,EAAAD,EAAAC,IAKA,GAAAC,IAAA7tB,MAAAusB,EACA5rB,UAAA6sB,GAAA,OAAAA,IACAK,EAAA9rB,QAAAyrB,GAGA7F,EAAAQ,KAAAzS,KAAAnG,UAAAse,Yf2jKGC,qBAAqB,EAAEC,kBAAkB,GAAGC,iBAAiB,UAAU,EAAE,EAAE,GAAG,KAAK", "file": "stacktrace-with-promises-and-json-polyfills.min.js", "sourcesContent": ["(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c=\"function\"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error(\"Cannot find module '\"+i+\"'\");throw a.code=\"MODULE_NOT_FOUND\",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u=\"function\"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('error-stack-parser', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.ErrorStackParser = factory(root.StackFrame);\n    }\n}(this, function ErrorStackParser(StackFrame) {\n    'use strict';\n\n    var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+:\\d+/;\n    var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\n    var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\n\n    return {\n        /**\n         * Given an Error object, extract the most information from it.\n         *\n         * @param {Error} error object\n         * @return {Array} of StackFrames\n         */\n        parse: function ErrorStackParser$$parse(error) {\n            if (typeof error.stacktrace !== 'undefined' || typeof error['opera#sourceloc'] !== 'undefined') {\n                return this.parseOpera(error);\n            } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n                return this.parseV8OrIE(error);\n            } else if (error.stack) {\n                return this.parseFFOrSafari(error);\n            } else {\n                throw new Error('Cannot parse given Error object');\n            }\n        },\n\n        // Separate line and column numbers from a string of the form: (URI:Line:Column)\n        extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n            // Fail-fast but return locations like \"(native)\"\n            if (urlLike.indexOf(':') === -1) {\n                return [urlLike];\n            }\n\n            var regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n            var parts = regExp.exec(urlLike.replace(/[()]/g, ''));\n            return [parts[1], parts[2] || undefined, parts[3] || undefined];\n        },\n\n        parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(CHROME_IE_STACK_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                if (line.indexOf('(eval ') > -1) {\n                    // Throw away eval information until we implement stacktrace.js/stackframe#8\n                    line = line.replace(/eval code/g, 'eval').replace(/(\\(eval at [^()]*)|(\\),.*$)/g, '');\n                }\n                var sanitizedLine = line.replace(/^\\s+/, '').replace(/\\(eval code/g, '(');\n\n                // capture and preseve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n                // case it has spaces in it, as the string is split on \\s+ later on\n                var location = sanitizedLine.match(/ (\\((.+):(\\d+):(\\d+)\\)$)/);\n\n                // remove the parenthesized location from the line, if it was matched\n                sanitizedLine = location ? sanitizedLine.replace(location[0], '') : sanitizedLine;\n\n                var tokens = sanitizedLine.split(/\\s+/).slice(1);\n                // if a location was matched, pass it to extractLocation() otherwise pop the last token\n                var locationParts = this.extractLocation(location ? location[1] : tokens.pop());\n                var functionName = tokens.join(' ') || undefined;\n                var fileName = ['eval', '<anonymous>'].indexOf(locationParts[0]) > -1 ? undefined : locationParts[0];\n\n                return new StackFrame({\n                    functionName: functionName,\n                    fileName: fileName,\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        },\n\n        parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                // Throw away eval information until we implement stacktrace.js/stackframe#8\n                if (line.indexOf(' > eval') > -1) {\n                    line = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g, ':$1');\n                }\n\n                if (line.indexOf('@') === -1 && line.indexOf(':') === -1) {\n                    // Safari eval frames only have function names and nothing else\n                    return new StackFrame({\n                        functionName: line\n                    });\n                } else {\n                    var functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n                    var matches = line.match(functionNameRegex);\n                    var functionName = matches && matches[1] ? matches[1] : undefined;\n                    var locationParts = this.extractLocation(line.replace(functionNameRegex, ''));\n\n                    return new StackFrame({\n                        functionName: functionName,\n                        fileName: locationParts[0],\n                        lineNumber: locationParts[1],\n                        columnNumber: locationParts[2],\n                        source: line\n                    });\n                }\n            }, this);\n        },\n\n        parseOpera: function ErrorStackParser$$parseOpera(e) {\n            if (!e.stacktrace || (e.message.indexOf('\\n') > -1 &&\n                e.message.split('\\n').length > e.stacktrace.split('\\n').length)) {\n                return this.parseOpera9(e);\n            } else if (!e.stack) {\n                return this.parseOpera10(e);\n            } else {\n                return this.parseOpera11(e);\n            }\n        },\n\n        parseOpera9: function ErrorStackParser$$parseOpera9(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n            var lines = e.message.split('\\n');\n            var result = [];\n\n            for (var i = 2, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(new StackFrame({\n                        fileName: match[2],\n                        lineNumber: match[1],\n                        source: lines[i]\n                    }));\n                }\n            }\n\n            return result;\n        },\n\n        parseOpera10: function ErrorStackParser$$parseOpera10(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n            var lines = e.stacktrace.split('\\n');\n            var result = [];\n\n            for (var i = 0, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(\n                        new StackFrame({\n                            functionName: match[3] || undefined,\n                            fileName: match[2],\n                            lineNumber: match[1],\n                            source: lines[i]\n                        })\n                    );\n                }\n            }\n\n            return result;\n        },\n\n        // Opera 10.65+ Error.stack very similar to FF/Safari\n        parseOpera11: function ErrorStackParser$$parseOpera11(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n            }, this);\n\n            return filtered.map(function(line) {\n                var tokens = line.split('@');\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionCall = (tokens.shift() || '');\n                var functionName = functionCall\n                    .replace(/<anonymous function(: (\\w+))?>/, '$2')\n                    .replace(/\\([^)]*\\)/g, '') || undefined;\n                var argsRaw;\n                if (functionCall.match(/\\(([^)]*)\\)/)) {\n                    argsRaw = functionCall.replace(/^[^(]+\\(([^)]*)\\)$/, '$1');\n                }\n                var args = (argsRaw === undefined || argsRaw === '[arguments not available]') ?\n                    undefined : argsRaw.split(',');\n\n                return new StackFrame({\n                    functionName: functionName,\n                    args: args,\n                    fileName: locationParts[0],\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        }\n    };\n}));\n", "(function(f){if(typeof exports===\"object\"&&typeof module!==\"undefined\"){module.exports=f()}else if(typeof define===\"function\"&&define.amd){define([],f)}else{var g;if(typeof window!==\"undefined\"){g=window}else if(typeof global!==\"undefined\"){g=global}else if(typeof self!==\"undefined\"){g=self}else{g=this}g.StackTrace = f()}})(function(){var define,module,exports;return (function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c=\"function\"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error(\"Cannot find module '\"+i+\"'\");throw a.code=\"MODULE_NOT_FOUND\",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u=\"function\"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){\n(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('error-stack-parser', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.ErrorStackParser = factory(root.StackFrame);\n    }\n}(this, function ErrorStackParser(StackFrame) {\n    'use strict';\n\n    var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+:\\d+/;\n    var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\n    var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\n\n    return {\n        /**\n         * Given an Error object, extract the most information from it.\n         *\n         * @param {Error} error object\n         * @return {Array} of StackFrames\n         */\n        parse: function ErrorStackParser$$parse(error) {\n            if (typeof error.stacktrace !== 'undefined' || typeof error['opera#sourceloc'] !== 'undefined') {\n                return this.parseOpera(error);\n            } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n                return this.parseV8OrIE(error);\n            } else if (error.stack) {\n                return this.parseFFOrSafari(error);\n            } else {\n                throw new Error('Cannot parse given Error object');\n            }\n        },\n\n        // Separate line and column numbers from a string of the form: (URI:Line:Column)\n        extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n            // Fail-fast but return locations like \"(native)\"\n            if (urlLike.indexOf(':') === -1) {\n                return [urlLike];\n            }\n\n            var regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n            var parts = regExp.exec(urlLike.replace(/[()]/g, ''));\n            return [parts[1], parts[2] || undefined, parts[3] || undefined];\n        },\n\n        parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(CHROME_IE_STACK_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                if (line.indexOf('(eval ') > -1) {\n                    // Throw away eval information until we implement stacktrace.js/stackframe#8\n                    line = line.replace(/eval code/g, 'eval').replace(/(\\(eval at [^()]*)|(\\),.*$)/g, '');\n                }\n                var sanitizedLine = line.replace(/^\\s+/, '').replace(/\\(eval code/g, '(');\n\n                // capture and preseve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n                // case it has spaces in it, as the string is split on \\s+ later on\n                var location = sanitizedLine.match(/ (\\((.+):(\\d+):(\\d+)\\)$)/);\n\n                // remove the parenthesized location from the line, if it was matched\n                sanitizedLine = location ? sanitizedLine.replace(location[0], '') : sanitizedLine;\n\n                var tokens = sanitizedLine.split(/\\s+/).slice(1);\n                // if a location was matched, pass it to extractLocation() otherwise pop the last token\n                var locationParts = this.extractLocation(location ? location[1] : tokens.pop());\n                var functionName = tokens.join(' ') || undefined;\n                var fileName = ['eval', '<anonymous>'].indexOf(locationParts[0]) > -1 ? undefined : locationParts[0];\n\n                return new StackFrame({\n                    functionName: functionName,\n                    fileName: fileName,\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        },\n\n        parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                // Throw away eval information until we implement stacktrace.js/stackframe#8\n                if (line.indexOf(' > eval') > -1) {\n                    line = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g, ':$1');\n                }\n\n                if (line.indexOf('@') === -1 && line.indexOf(':') === -1) {\n                    // Safari eval frames only have function names and nothing else\n                    return new StackFrame({\n                        functionName: line\n                    });\n                } else {\n                    var functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n                    var matches = line.match(functionNameRegex);\n                    var functionName = matches && matches[1] ? matches[1] : undefined;\n                    var locationParts = this.extractLocation(line.replace(functionNameRegex, ''));\n\n                    return new StackFrame({\n                        functionName: functionName,\n                        fileName: locationParts[0],\n                        lineNumber: locationParts[1],\n                        columnNumber: locationParts[2],\n                        source: line\n                    });\n                }\n            }, this);\n        },\n\n        parseOpera: function ErrorStackParser$$parseOpera(e) {\n            if (!e.stacktrace || (e.message.indexOf('\\n') > -1 &&\n                e.message.split('\\n').length > e.stacktrace.split('\\n').length)) {\n                return this.parseOpera9(e);\n            } else if (!e.stack) {\n                return this.parseOpera10(e);\n            } else {\n                return this.parseOpera11(e);\n            }\n        },\n\n        parseOpera9: function ErrorStackParser$$parseOpera9(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n            var lines = e.message.split('\\n');\n            var result = [];\n\n            for (var i = 2, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(new StackFrame({\n                        fileName: match[2],\n                        lineNumber: match[1],\n                        source: lines[i]\n                    }));\n                }\n            }\n\n            return result;\n        },\n\n        parseOpera10: function ErrorStackParser$$parseOpera10(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n            var lines = e.stacktrace.split('\\n');\n            var result = [];\n\n            for (var i = 0, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(\n                        new StackFrame({\n                            functionName: match[3] || undefined,\n                            fileName: match[2],\n                            lineNumber: match[1],\n                            source: lines[i]\n                        })\n                    );\n                }\n            }\n\n            return result;\n        },\n\n        // Opera 10.65+ Error.stack very similar to FF/Safari\n        parseOpera11: function ErrorStackParser$$parseOpera11(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n            }, this);\n\n            return filtered.map(function(line) {\n                var tokens = line.split('@');\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionCall = (tokens.shift() || '');\n                var functionName = functionCall\n                    .replace(/<anonymous function(: (\\w+))?>/, '$2')\n                    .replace(/\\([^)]*\\)/g, '') || undefined;\n                var argsRaw;\n                if (functionCall.match(/\\(([^)]*)\\)/)) {\n                    argsRaw = functionCall.replace(/^[^(]+\\(([^)]*)\\)$/, '$1');\n                }\n                var args = (argsRaw === undefined || argsRaw === '[arguments not available]') ?\n                    undefined : argsRaw.split(',');\n\n                return new StackFrame({\n                    functionName: functionName,\n                    args: args,\n                    fileName: locationParts[0],\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        }\n    };\n}));\n\n},{\"stackframe\":2}],2:[function(require,module,exports){\n(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stackframe', [], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory();\n    } else {\n        root.StackFrame = factory();\n    }\n}(this, function() {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function _capitalize(str) {\n        return str.charAt(0).toUpperCase() + str.substring(1);\n    }\n\n    function _getter(p) {\n        return function() {\n            return this[p];\n        };\n    }\n\n    var booleanProps = ['isConstructor', 'isEval', 'isNative', 'isToplevel'];\n    var numericProps = ['columnNumber', 'lineNumber'];\n    var stringProps = ['fileName', 'functionName', 'source'];\n    var arrayProps = ['args'];\n\n    var props = booleanProps.concat(numericProps, stringProps, arrayProps);\n\n    function StackFrame(obj) {\n        if (!obj) return;\n        for (var i = 0; i < props.length; i++) {\n            if (obj[props[i]] !== undefined) {\n                this['set' + _capitalize(props[i])](obj[props[i]]);\n            }\n        }\n    }\n\n    StackFrame.prototype = {\n        getArgs: function() {\n            return this.args;\n        },\n        setArgs: function(v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        getEvalOrigin: function() {\n            return this.evalOrigin;\n        },\n        setEvalOrigin: function(v) {\n            if (v instanceof StackFrame) {\n                this.evalOrigin = v;\n            } else if (v instanceof Object) {\n                this.evalOrigin = new StackFrame(v);\n            } else {\n                throw new TypeError('Eval Origin must be an Object or StackFrame');\n            }\n        },\n\n        toString: function() {\n            var fileName = this.getFileName() || '';\n            var lineNumber = this.getLineNumber() || '';\n            var columnNumber = this.getColumnNumber() || '';\n            var functionName = this.getFunctionName() || '';\n            if (this.getIsEval()) {\n                if (fileName) {\n                    return '[eval] (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n                }\n                return '[eval]:' + lineNumber + ':' + columnNumber;\n            }\n            if (functionName) {\n                return functionName + ' (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n            }\n            return fileName + ':' + lineNumber + ':' + columnNumber;\n        }\n    };\n\n    StackFrame.fromString = function StackFrame$$fromString(str) {\n        var argsStartIndex = str.indexOf('(');\n        var argsEndIndex = str.lastIndexOf(')');\n\n        var functionName = str.substring(0, argsStartIndex);\n        var args = str.substring(argsStartIndex + 1, argsEndIndex).split(',');\n        var locationString = str.substring(argsEndIndex + 1);\n\n        if (locationString.indexOf('@') === 0) {\n            var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, '');\n            var fileName = parts[1];\n            var lineNumber = parts[2];\n            var columnNumber = parts[3];\n        }\n\n        return new StackFrame({\n            functionName: functionName,\n            args: args || undefined,\n            fileName: fileName,\n            lineNumber: lineNumber || undefined,\n            columnNumber: columnNumber || undefined\n        });\n    };\n\n    for (var i = 0; i < booleanProps.length; i++) {\n        StackFrame.prototype['get' + _capitalize(booleanProps[i])] = _getter(booleanProps[i]);\n        StackFrame.prototype['set' + _capitalize(booleanProps[i])] = (function(p) {\n            return function(v) {\n                this[p] = Boolean(v);\n            };\n        })(booleanProps[i]);\n    }\n\n    for (var j = 0; j < numericProps.length; j++) {\n        StackFrame.prototype['get' + _capitalize(numericProps[j])] = _getter(numericProps[j]);\n        StackFrame.prototype['set' + _capitalize(numericProps[j])] = (function(p) {\n            return function(v) {\n                if (!_isNumber(v)) {\n                    throw new TypeError(p + ' must be a Number');\n                }\n                this[p] = Number(v);\n            };\n        })(numericProps[j]);\n    }\n\n    for (var k = 0; k < stringProps.length; k++) {\n        StackFrame.prototype['get' + _capitalize(stringProps[k])] = _getter(stringProps[k]);\n        StackFrame.prototype['set' + _capitalize(stringProps[k])] = (function(p) {\n            return function(v) {\n                this[p] = String(v);\n            };\n        })(stringProps[k]);\n    }\n\n    return StackFrame;\n}));\n\n},{}],3:[function(require,module,exports){\n(function (process,global){\n/*!\n * @overview es6-promise - a tiny implementation of Promises/A+.\n * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)\n * @license   Licensed under MIT license\n *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE\n * @version   3.3.1\n */\n\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n    typeof define === 'function' && define.amd ? define(factory) :\n    (global.ES6Promise = factory());\n}(this, (function () { 'use strict';\n\nfunction objectOrFunction(x) {\n  return typeof x === 'function' || typeof x === 'object' && x !== null;\n}\n\nfunction isFunction(x) {\n  return typeof x === 'function';\n}\n\nvar _isArray = undefined;\nif (!Array.isArray) {\n  _isArray = function (x) {\n    return Object.prototype.toString.call(x) === '[object Array]';\n  };\n} else {\n  _isArray = Array.isArray;\n}\n\nvar isArray = _isArray;\n\nvar len = 0;\nvar vertxNext = undefined;\nvar customSchedulerFn = undefined;\n\nvar asap = function asap(callback, arg) {\n  queue[len] = callback;\n  queue[len + 1] = arg;\n  len += 2;\n  if (len === 2) {\n    // If len is 2, that means that we need to schedule an async flush.\n    // If additional callbacks are queued before the queue is flushed, they\n    // will be processed by this flush that we are scheduling.\n    if (customSchedulerFn) {\n      customSchedulerFn(flush);\n    } else {\n      scheduleFlush();\n    }\n  }\n};\n\nfunction setScheduler(scheduleFn) {\n  customSchedulerFn = scheduleFn;\n}\n\nfunction setAsap(asapFn) {\n  asap = asapFn;\n}\n\nvar browserWindow = typeof window !== 'undefined' ? window : undefined;\nvar browserGlobal = browserWindow || {};\nvar BrowserMutationObserver = browserGlobal.MutationObserver || browserGlobal.WebKitMutationObserver;\nvar isNode = typeof self === 'undefined' && typeof process !== 'undefined' && ({}).toString.call(process) === '[object process]';\n\n// test for web worker but not in IE10\nvar isWorker = typeof Uint8ClampedArray !== 'undefined' && typeof importScripts !== 'undefined' && typeof MessageChannel !== 'undefined';\n\n// node\nfunction useNextTick() {\n  // node version 0.10.x displays a deprecation warning when nextTick is used recursively\n  // see https://github.com/cujojs/when/issues/410 for details\n  return function () {\n    return process.nextTick(flush);\n  };\n}\n\n// vertx\nfunction useVertxTimer() {\n  return function () {\n    vertxNext(flush);\n  };\n}\n\nfunction useMutationObserver() {\n  var iterations = 0;\n  var observer = new BrowserMutationObserver(flush);\n  var node = document.createTextNode('');\n  observer.observe(node, { characterData: true });\n\n  return function () {\n    node.data = iterations = ++iterations % 2;\n  };\n}\n\n// web worker\nfunction useMessageChannel() {\n  var channel = new MessageChannel();\n  channel.port1.onmessage = flush;\n  return function () {\n    return channel.port2.postMessage(0);\n  };\n}\n\nfunction useSetTimeout() {\n  // Store setTimeout reference so es6-promise will be unaffected by\n  // other code modifying setTimeout (like sinon.useFakeTimers())\n  var globalSetTimeout = setTimeout;\n  return function () {\n    return globalSetTimeout(flush, 1);\n  };\n}\n\nvar queue = new Array(1000);\nfunction flush() {\n  for (var i = 0; i < len; i += 2) {\n    var callback = queue[i];\n    var arg = queue[i + 1];\n\n    callback(arg);\n\n    queue[i] = undefined;\n    queue[i + 1] = undefined;\n  }\n\n  len = 0;\n}\n\nfunction attemptVertx() {\n  try {\n    var r = require;\n    var vertx = r('vertx');\n    vertxNext = vertx.runOnLoop || vertx.runOnContext;\n    return useVertxTimer();\n  } catch (e) {\n    return useSetTimeout();\n  }\n}\n\nvar scheduleFlush = undefined;\n// Decide what async method to use to triggering processing of queued callbacks:\nif (isNode) {\n  scheduleFlush = useNextTick();\n} else if (BrowserMutationObserver) {\n  scheduleFlush = useMutationObserver();\n} else if (isWorker) {\n  scheduleFlush = useMessageChannel();\n} else if (browserWindow === undefined && typeof require === 'function') {\n  scheduleFlush = attemptVertx();\n} else {\n  scheduleFlush = useSetTimeout();\n}\n\nfunction then(onFulfillment, onRejection) {\n  var _arguments = arguments;\n\n  var parent = this;\n\n  var child = new this.constructor(noop);\n\n  if (child[PROMISE_ID] === undefined) {\n    makePromise(child);\n  }\n\n  var _state = parent._state;\n\n  if (_state) {\n    (function () {\n      var callback = _arguments[_state - 1];\n      asap(function () {\n        return invokeCallback(_state, child, callback, parent._result);\n      });\n    })();\n  } else {\n    subscribe(parent, child, onFulfillment, onRejection);\n  }\n\n  return child;\n}\n\n/**\n  `Promise.resolve` returns a promise that will become resolved with the\n  passed `value`. It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    resolve(1);\n  });\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.resolve(1);\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  @method resolve\n  @static\n  @param {Any} value value that the returned promise will be resolved with\n  Useful for tooling.\n  @return {Promise} a promise that will become fulfilled with the given\n  `value`\n*/\nfunction resolve(object) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (object && typeof object === 'object' && object.constructor === Constructor) {\n    return object;\n  }\n\n  var promise = new Constructor(noop);\n  _resolve(promise, object);\n  return promise;\n}\n\nvar PROMISE_ID = Math.random().toString(36).substring(16);\n\nfunction noop() {}\n\nvar PENDING = void 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\n\nvar GET_THEN_ERROR = new ErrorObject();\n\nfunction selfFulfillment() {\n  return new TypeError(\"You cannot resolve a promise with itself\");\n}\n\nfunction cannotReturnOwn() {\n  return new TypeError('A promises callback cannot return that same promise.');\n}\n\nfunction getThen(promise) {\n  try {\n    return promise.then;\n  } catch (error) {\n    GET_THEN_ERROR.error = error;\n    return GET_THEN_ERROR;\n  }\n}\n\nfunction tryThen(then, value, fulfillmentHandler, rejectionHandler) {\n  try {\n    then.call(value, fulfillmentHandler, rejectionHandler);\n  } catch (e) {\n    return e;\n  }\n}\n\nfunction handleForeignThenable(promise, thenable, then) {\n  asap(function (promise) {\n    var sealed = false;\n    var error = tryThen(then, thenable, function (value) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n      if (thenable !== value) {\n        _resolve(promise, value);\n      } else {\n        fulfill(promise, value);\n      }\n    }, function (reason) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n\n      _reject(promise, reason);\n    }, 'Settle: ' + (promise._label || ' unknown promise'));\n\n    if (!sealed && error) {\n      sealed = true;\n      _reject(promise, error);\n    }\n  }, promise);\n}\n\nfunction handleOwnThenable(promise, thenable) {\n  if (thenable._state === FULFILLED) {\n    fulfill(promise, thenable._result);\n  } else if (thenable._state === REJECTED) {\n    _reject(promise, thenable._result);\n  } else {\n    subscribe(thenable, undefined, function (value) {\n      return _resolve(promise, value);\n    }, function (reason) {\n      return _reject(promise, reason);\n    });\n  }\n}\n\nfunction handleMaybeThenable(promise, maybeThenable, then$$) {\n  if (maybeThenable.constructor === promise.constructor && then$$ === then && maybeThenable.constructor.resolve === resolve) {\n    handleOwnThenable(promise, maybeThenable);\n  } else {\n    if (then$$ === GET_THEN_ERROR) {\n      _reject(promise, GET_THEN_ERROR.error);\n    } else if (then$$ === undefined) {\n      fulfill(promise, maybeThenable);\n    } else if (isFunction(then$$)) {\n      handleForeignThenable(promise, maybeThenable, then$$);\n    } else {\n      fulfill(promise, maybeThenable);\n    }\n  }\n}\n\nfunction _resolve(promise, value) {\n  if (promise === value) {\n    _reject(promise, selfFulfillment());\n  } else if (objectOrFunction(value)) {\n    handleMaybeThenable(promise, value, getThen(value));\n  } else {\n    fulfill(promise, value);\n  }\n}\n\nfunction publishRejection(promise) {\n  if (promise._onerror) {\n    promise._onerror(promise._result);\n  }\n\n  publish(promise);\n}\n\nfunction fulfill(promise, value) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n\n  promise._result = value;\n  promise._state = FULFILLED;\n\n  if (promise._subscribers.length !== 0) {\n    asap(publish, promise);\n  }\n}\n\nfunction _reject(promise, reason) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n  promise._state = REJECTED;\n  promise._result = reason;\n\n  asap(publishRejection, promise);\n}\n\nfunction subscribe(parent, child, onFulfillment, onRejection) {\n  var _subscribers = parent._subscribers;\n  var length = _subscribers.length;\n\n  parent._onerror = null;\n\n  _subscribers[length] = child;\n  _subscribers[length + FULFILLED] = onFulfillment;\n  _subscribers[length + REJECTED] = onRejection;\n\n  if (length === 0 && parent._state) {\n    asap(publish, parent);\n  }\n}\n\nfunction publish(promise) {\n  var subscribers = promise._subscribers;\n  var settled = promise._state;\n\n  if (subscribers.length === 0) {\n    return;\n  }\n\n  var child = undefined,\n      callback = undefined,\n      detail = promise._result;\n\n  for (var i = 0; i < subscribers.length; i += 3) {\n    child = subscribers[i];\n    callback = subscribers[i + settled];\n\n    if (child) {\n      invokeCallback(settled, child, callback, detail);\n    } else {\n      callback(detail);\n    }\n  }\n\n  promise._subscribers.length = 0;\n}\n\nfunction ErrorObject() {\n  this.error = null;\n}\n\nvar TRY_CATCH_ERROR = new ErrorObject();\n\nfunction tryCatch(callback, detail) {\n  try {\n    return callback(detail);\n  } catch (e) {\n    TRY_CATCH_ERROR.error = e;\n    return TRY_CATCH_ERROR;\n  }\n}\n\nfunction invokeCallback(settled, promise, callback, detail) {\n  var hasCallback = isFunction(callback),\n      value = undefined,\n      error = undefined,\n      succeeded = undefined,\n      failed = undefined;\n\n  if (hasCallback) {\n    value = tryCatch(callback, detail);\n\n    if (value === TRY_CATCH_ERROR) {\n      failed = true;\n      error = value.error;\n      value = null;\n    } else {\n      succeeded = true;\n    }\n\n    if (promise === value) {\n      _reject(promise, cannotReturnOwn());\n      return;\n    }\n  } else {\n    value = detail;\n    succeeded = true;\n  }\n\n  if (promise._state !== PENDING) {\n    // noop\n  } else if (hasCallback && succeeded) {\n      _resolve(promise, value);\n    } else if (failed) {\n      _reject(promise, error);\n    } else if (settled === FULFILLED) {\n      fulfill(promise, value);\n    } else if (settled === REJECTED) {\n      _reject(promise, value);\n    }\n}\n\nfunction initializePromise(promise, resolver) {\n  try {\n    resolver(function resolvePromise(value) {\n      _resolve(promise, value);\n    }, function rejectPromise(reason) {\n      _reject(promise, reason);\n    });\n  } catch (e) {\n    _reject(promise, e);\n  }\n}\n\nvar id = 0;\nfunction nextId() {\n  return id++;\n}\n\nfunction makePromise(promise) {\n  promise[PROMISE_ID] = id++;\n  promise._state = undefined;\n  promise._result = undefined;\n  promise._subscribers = [];\n}\n\nfunction Enumerator(Constructor, input) {\n  this._instanceConstructor = Constructor;\n  this.promise = new Constructor(noop);\n\n  if (!this.promise[PROMISE_ID]) {\n    makePromise(this.promise);\n  }\n\n  if (isArray(input)) {\n    this._input = input;\n    this.length = input.length;\n    this._remaining = input.length;\n\n    this._result = new Array(this.length);\n\n    if (this.length === 0) {\n      fulfill(this.promise, this._result);\n    } else {\n      this.length = this.length || 0;\n      this._enumerate();\n      if (this._remaining === 0) {\n        fulfill(this.promise, this._result);\n      }\n    }\n  } else {\n    _reject(this.promise, validationError());\n  }\n}\n\nfunction validationError() {\n  return new Error('Array Methods must be provided an Array');\n};\n\nEnumerator.prototype._enumerate = function () {\n  var length = this.length;\n  var _input = this._input;\n\n  for (var i = 0; this._state === PENDING && i < length; i++) {\n    this._eachEntry(_input[i], i);\n  }\n};\n\nEnumerator.prototype._eachEntry = function (entry, i) {\n  var c = this._instanceConstructor;\n  var resolve$$ = c.resolve;\n\n  if (resolve$$ === resolve) {\n    var _then = getThen(entry);\n\n    if (_then === then && entry._state !== PENDING) {\n      this._settledAt(entry._state, i, entry._result);\n    } else if (typeof _then !== 'function') {\n      this._remaining--;\n      this._result[i] = entry;\n    } else if (c === Promise) {\n      var promise = new c(noop);\n      handleMaybeThenable(promise, entry, _then);\n      this._willSettleAt(promise, i);\n    } else {\n      this._willSettleAt(new c(function (resolve$$) {\n        return resolve$$(entry);\n      }), i);\n    }\n  } else {\n    this._willSettleAt(resolve$$(entry), i);\n  }\n};\n\nEnumerator.prototype._settledAt = function (state, i, value) {\n  var promise = this.promise;\n\n  if (promise._state === PENDING) {\n    this._remaining--;\n\n    if (state === REJECTED) {\n      _reject(promise, value);\n    } else {\n      this._result[i] = value;\n    }\n  }\n\n  if (this._remaining === 0) {\n    fulfill(promise, this._result);\n  }\n};\n\nEnumerator.prototype._willSettleAt = function (promise, i) {\n  var enumerator = this;\n\n  subscribe(promise, undefined, function (value) {\n    return enumerator._settledAt(FULFILLED, i, value);\n  }, function (reason) {\n    return enumerator._settledAt(REJECTED, i, reason);\n  });\n};\n\n/**\n  `Promise.all` accepts an array of promises, and returns a new promise which\n  is fulfilled with an array of fulfillment values for the passed promises, or\n  rejected with the reason of the first passed promise to be rejected. It casts all\n  elements of the passed iterable to promises as it runs this algorithm.\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = resolve(2);\n  let promise3 = resolve(3);\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // The array here would be [ 1, 2, 3 ];\n  });\n  ```\n\n  If any of the `promises` given to `all` are rejected, the first promise\n  that is rejected will be given as an argument to the returned promises's\n  rejection handler. For example:\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = reject(new Error(\"2\"));\n  let promise3 = reject(new Error(\"3\"));\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // Code here never runs because there are rejected promises!\n  }, function(error) {\n    // error.message === \"2\"\n  });\n  ```\n\n  @method all\n  @static\n  @param {Array} entries array of promises\n  @param {String} label optional string for labeling the promise.\n  Useful for tooling.\n  @return {Promise} promise that is fulfilled when all `promises` have been\n  fulfilled, or rejected if any of them become rejected.\n  @static\n*/\nfunction all(entries) {\n  return new Enumerator(this, entries).promise;\n}\n\n/**\n  `Promise.race` returns a new promise which is settled in the same way as the\n  first passed promise to settle.\n\n  Example:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 2');\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // result === 'promise 2' because it was resolved before promise1\n    // was resolved.\n  });\n  ```\n\n  `Promise.race` is deterministic in that only the state of the first\n  settled promise matters. For example, even if other promises given to the\n  `promises` array argument are resolved, but the first settled promise has\n  become rejected before the other promises became fulfilled, the returned\n  promise will become rejected:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      reject(new Error('promise 2'));\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // Code here never runs\n  }, function(reason){\n    // reason.message === 'promise 2' because promise 2 became rejected before\n    // promise 1 became fulfilled\n  });\n  ```\n\n  An example real-world use case is implementing timeouts:\n\n  ```javascript\n  Promise.race([ajax('foo.json'), timeout(5000)])\n  ```\n\n  @method race\n  @static\n  @param {Array} promises array of promises to observe\n  Useful for tooling.\n  @return {Promise} a promise which settles in the same way as the first passed\n  promise to settle.\n*/\nfunction race(entries) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (!isArray(entries)) {\n    return new Constructor(function (_, reject) {\n      return reject(new TypeError('You must pass an array to race.'));\n    });\n  } else {\n    return new Constructor(function (resolve, reject) {\n      var length = entries.length;\n      for (var i = 0; i < length; i++) {\n        Constructor.resolve(entries[i]).then(resolve, reject);\n      }\n    });\n  }\n}\n\n/**\n  `Promise.reject` returns a promise rejected with the passed `reason`.\n  It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    reject(new Error('WHOOPS'));\n  });\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.reject(new Error('WHOOPS'));\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  @method reject\n  @static\n  @param {Any} reason value that the returned promise will be rejected with.\n  Useful for tooling.\n  @return {Promise} a promise rejected with the given `reason`.\n*/\nfunction reject(reason) {\n  /*jshint validthis:true */\n  var Constructor = this;\n  var promise = new Constructor(noop);\n  _reject(promise, reason);\n  return promise;\n}\n\nfunction needsResolver() {\n  throw new TypeError('You must pass a resolver function as the first argument to the promise constructor');\n}\n\nfunction needsNew() {\n  throw new TypeError(\"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.\");\n}\n\n/**\n  Promise objects represent the eventual result of an asynchronous operation. The\n  primary way of interacting with a promise is through its `then` method, which\n  registers callbacks to receive either a promise's eventual value or the reason\n  why the promise cannot be fulfilled.\n\n  Terminology\n  -----------\n\n  - `promise` is an object or function with a `then` method whose behavior conforms to this specification.\n  - `thenable` is an object or function that defines a `then` method.\n  - `value` is any legal JavaScript value (including undefined, a thenable, or a promise).\n  - `exception` is a value that is thrown using the throw statement.\n  - `reason` is a value that indicates why a promise was rejected.\n  - `settled` the final resting state of a promise, fulfilled or rejected.\n\n  A promise can be in one of three states: pending, fulfilled, or rejected.\n\n  Promises that are fulfilled have a fulfillment value and are in the fulfilled\n  state.  Promises that are rejected have a rejection reason and are in the\n  rejected state.  A fulfillment value is never a thenable.\n\n  Promises can also be said to *resolve* a value.  If this value is also a\n  promise, then the original promise's settled state will match the value's\n  settled state.  So a promise that *resolves* a promise that rejects will\n  itself reject, and a promise that *resolves* a promise that fulfills will\n  itself fulfill.\n\n\n  Basic Usage:\n  ------------\n\n  ```js\n  let promise = new Promise(function(resolve, reject) {\n    // on success\n    resolve(value);\n\n    // on failure\n    reject(reason);\n  });\n\n  promise.then(function(value) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Advanced Usage:\n  ---------------\n\n  Promises shine when abstracting away asynchronous interactions such as\n  `XMLHttpRequest`s.\n\n  ```js\n  function getJSON(url) {\n    return new Promise(function(resolve, reject){\n      let xhr = new XMLHttpRequest();\n\n      xhr.open('GET', url);\n      xhr.onreadystatechange = handler;\n      xhr.responseType = 'json';\n      xhr.setRequestHeader('Accept', 'application/json');\n      xhr.send();\n\n      function handler() {\n        if (this.readyState === this.DONE) {\n          if (this.status === 200) {\n            resolve(this.response);\n          } else {\n            reject(new Error('getJSON: `' + url + '` failed with status: [' + this.status + ']'));\n          }\n        }\n      };\n    });\n  }\n\n  getJSON('/posts.json').then(function(json) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Unlike callbacks, promises are great composable primitives.\n\n  ```js\n  Promise.all([\n    getJSON('/posts'),\n    getJSON('/comments')\n  ]).then(function(values){\n    values[0] // => postsJSON\n    values[1] // => commentsJSON\n\n    return values;\n  });\n  ```\n\n  @class Promise\n  @param {function} resolver\n  Useful for tooling.\n  @constructor\n*/\nfunction Promise(resolver) {\n  this[PROMISE_ID] = nextId();\n  this._result = this._state = undefined;\n  this._subscribers = [];\n\n  if (noop !== resolver) {\n    typeof resolver !== 'function' && needsResolver();\n    this instanceof Promise ? initializePromise(this, resolver) : needsNew();\n  }\n}\n\nPromise.all = all;\nPromise.race = race;\nPromise.resolve = resolve;\nPromise.reject = reject;\nPromise._setScheduler = setScheduler;\nPromise._setAsap = setAsap;\nPromise._asap = asap;\n\nPromise.prototype = {\n  constructor: Promise,\n\n  /**\n    The primary way of interacting with a promise is through its `then` method,\n    which registers callbacks to receive either a promise's eventual value or the\n    reason why the promise cannot be fulfilled.\n  \n    ```js\n    findUser().then(function(user){\n      // user is available\n    }, function(reason){\n      // user is unavailable, and you are given the reason why\n    });\n    ```\n  \n    Chaining\n    --------\n  \n    The return value of `then` is itself a promise.  This second, 'downstream'\n    promise is resolved with the return value of the first promise's fulfillment\n    or rejection handler, or rejected if the handler throws an exception.\n  \n    ```js\n    findUser().then(function (user) {\n      return user.name;\n    }, function (reason) {\n      return 'default name';\n    }).then(function (userName) {\n      // If `findUser` fulfilled, `userName` will be the user's name, otherwise it\n      // will be `'default name'`\n    });\n  \n    findUser().then(function (user) {\n      throw new Error('Found user, but still unhappy');\n    }, function (reason) {\n      throw new Error('`findUser` rejected and we're unhappy');\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // if `findUser` fulfilled, `reason` will be 'Found user, but still unhappy'.\n      // If `findUser` rejected, `reason` will be '`findUser` rejected and we're unhappy'.\n    });\n    ```\n    If the downstream promise does not specify a rejection handler, rejection reasons will be propagated further downstream.\n  \n    ```js\n    findUser().then(function (user) {\n      throw new PedagogicalException('Upstream error');\n    }).then(function (value) {\n      // never reached\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // The `PedgagocialException` is propagated all the way down to here\n    });\n    ```\n  \n    Assimilation\n    ------------\n  \n    Sometimes the value you want to propagate to a downstream promise can only be\n    retrieved asynchronously. This can be achieved by returning a promise in the\n    fulfillment or rejection handler. The downstream promise will then be pending\n    until the returned promise is settled. This is called *assimilation*.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // The user's comments are now available\n    });\n    ```\n  \n    If the assimliated promise rejects, then the downstream promise will also reject.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // If `findCommentsByAuthor` fulfills, we'll have the value here\n    }, function (reason) {\n      // If `findCommentsByAuthor` rejects, we'll have the reason here\n    });\n    ```\n  \n    Simple Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let result;\n  \n    try {\n      result = findResult();\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n    findResult(function(result, err){\n      if (err) {\n        // failure\n      } else {\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findResult().then(function(result){\n      // success\n    }, function(reason){\n      // failure\n    });\n    ```\n  \n    Advanced Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let author, books;\n  \n    try {\n      author = findAuthor();\n      books  = findBooksByAuthor(author);\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n  \n    function foundBooks(books) {\n  \n    }\n  \n    function failure(reason) {\n  \n    }\n  \n    findAuthor(function(author, err){\n      if (err) {\n        failure(err);\n        // failure\n      } else {\n        try {\n          findBoooksByAuthor(author, function(books, err) {\n            if (err) {\n              failure(err);\n            } else {\n              try {\n                foundBooks(books);\n              } catch(reason) {\n                failure(reason);\n              }\n            }\n          });\n        } catch(error) {\n          failure(err);\n        }\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findAuthor().\n      then(findBooksByAuthor).\n      then(function(books){\n        // found books\n    }).catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method then\n    @param {Function} onFulfilled\n    @param {Function} onRejected\n    Useful for tooling.\n    @return {Promise}\n  */\n  then: then,\n\n  /**\n    `catch` is simply sugar for `then(undefined, onRejection)` which makes it the same\n    as the catch block of a try/catch statement.\n  \n    ```js\n    function findAuthor(){\n      throw new Error('couldn't find that author');\n    }\n  \n    // synchronous\n    try {\n      findAuthor();\n    } catch(reason) {\n      // something went wrong\n    }\n  \n    // async with promises\n    findAuthor().catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method catch\n    @param {Function} onRejection\n    Useful for tooling.\n    @return {Promise}\n  */\n  'catch': function _catch(onRejection) {\n    return this.then(null, onRejection);\n  }\n};\n\nfunction polyfill() {\n    var local = undefined;\n\n    if (typeof global !== 'undefined') {\n        local = global;\n    } else if (typeof self !== 'undefined') {\n        local = self;\n    } else {\n        try {\n            local = Function('return this')();\n        } catch (e) {\n            throw new Error('polyfill failed because global object is unavailable in this environment');\n        }\n    }\n\n    var P = local.Promise;\n\n    if (P) {\n        var promiseToString = null;\n        try {\n            promiseToString = Object.prototype.toString.call(P.resolve());\n        } catch (e) {\n            // silently ignored\n        }\n\n        if (promiseToString === '[object Promise]' && !P.cast) {\n            return;\n        }\n    }\n\n    local.Promise = Promise;\n}\n\npolyfill();\n// Strange compat..\nPromise.polyfill = polyfill;\nPromise.Promise = Promise;\n\nreturn Promise;\n\n})));\n\n}).call(this,require('_process'),typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n\n},{\"_process\":5}],4:[function(require,module,exports){\n(function (global){\n/*! JSON v3.3.2 | http://bestiejs.github.io/json3 | Copyright 2012-2014, Kit Cambridge | http://kit.mit-license.org */\n;(function () {\n  // Detect the `define` function exposed by asynchronous module loaders. The\n  // strict `define` check is necessary for compatibility with `r.js`.\n  var isLoader = typeof define === \"function\" && define.amd;\n\n  // A set of types used to distinguish objects from primitives.\n  var objectTypes = {\n    \"function\": true,\n    \"object\": true\n  };\n\n  // Detect the `exports` object exposed by CommonJS implementations.\n  var freeExports = objectTypes[typeof exports] && exports && !exports.nodeType && exports;\n\n  // Use the `global` object exposed by Node (including Browserify via\n  // `insert-module-globals`), Narwhal, and Ringo as the default context,\n  // and the `window` object in browsers. Rhino exports a `global` function\n  // instead.\n  var root = objectTypes[typeof window] && window || this,\n      freeGlobal = freeExports && objectTypes[typeof module] && module && !module.nodeType && typeof global == \"object\" && global;\n\n  if (freeGlobal && (freeGlobal[\"global\"] === freeGlobal || freeGlobal[\"window\"] === freeGlobal || freeGlobal[\"self\"] === freeGlobal)) {\n    root = freeGlobal;\n  }\n\n  // Public: Initializes JSON 3 using the given `context` object, attaching the\n  // `stringify` and `parse` functions to the specified `exports` object.\n  function runInContext(context, exports) {\n    context || (context = root[\"Object\"]());\n    exports || (exports = root[\"Object\"]());\n\n    // Native constructor aliases.\n    var Number = context[\"Number\"] || root[\"Number\"],\n        String = context[\"String\"] || root[\"String\"],\n        Object = context[\"Object\"] || root[\"Object\"],\n        Date = context[\"Date\"] || root[\"Date\"],\n        SyntaxError = context[\"SyntaxError\"] || root[\"SyntaxError\"],\n        TypeError = context[\"TypeError\"] || root[\"TypeError\"],\n        Math = context[\"Math\"] || root[\"Math\"],\n        nativeJSON = context[\"JSON\"] || root[\"JSON\"];\n\n    // Delegate to the native `stringify` and `parse` implementations.\n    if (typeof nativeJSON == \"object\" && nativeJSON) {\n      exports.stringify = nativeJSON.stringify;\n      exports.parse = nativeJSON.parse;\n    }\n\n    // Convenience aliases.\n    var objectProto = Object.prototype,\n        getClass = objectProto.toString,\n        isProperty, forEach, undef;\n\n    // Test the `Date#getUTC*` methods. Based on work by @Yaffle.\n    var isExtended = new Date(-3509827334573292);\n    try {\n      // The `getUTCFullYear`, `Month`, and `Date` methods return nonsensical\n      // results for certain dates in Opera >= 10.53.\n      isExtended = isExtended.getUTCFullYear() == -109252 && isExtended.getUTCMonth() === 0 && isExtended.getUTCDate() === 1 &&\n        // Safari < 2.0.2 stores the internal millisecond time value correctly,\n        // but clips the values returned by the date methods to the range of\n        // signed 32-bit integers ([-2 ** 31, 2 ** 31 - 1]).\n        isExtended.getUTCHours() == 10 && isExtended.getUTCMinutes() == 37 && isExtended.getUTCSeconds() == 6 && isExtended.getUTCMilliseconds() == 708;\n    } catch (exception) {}\n\n    // Internal: Determines whether the native `JSON.stringify` and `parse`\n    // implementations are spec-compliant. Based on work by Ken Snyder.\n    function has(name) {\n      if (has[name] !== undef) {\n        // Return cached feature test result.\n        return has[name];\n      }\n      var isSupported;\n      if (name == \"bug-string-char-index\") {\n        // IE <= 7 doesn't support accessing string characters using square\n        // bracket notation. IE 8 only supports this for primitives.\n        isSupported = \"a\"[0] != \"a\";\n      } else if (name == \"json\") {\n        // Indicates whether both `JSON.stringify` and `JSON.parse` are\n        // supported.\n        isSupported = has(\"json-stringify\") && has(\"json-parse\");\n      } else {\n        var value, serialized = '{\"a\":[1,true,false,null,\"\\\\u0000\\\\b\\\\n\\\\f\\\\r\\\\t\"]}';\n        // Test `JSON.stringify`.\n        if (name == \"json-stringify\") {\n          var stringify = exports.stringify, stringifySupported = typeof stringify == \"function\" && isExtended;\n          if (stringifySupported) {\n            // A test function object with a custom `toJSON` method.\n            (value = function () {\n              return 1;\n            }).toJSON = value;\n            try {\n              stringifySupported =\n                // Firefox 3.1b1 and b2 serialize string, number, and boolean\n                // primitives as object literals.\n                stringify(0) === \"0\" &&\n                // FF 3.1b1, b2, and JSON 2 serialize wrapped primitives as object\n                // literals.\n                stringify(new Number()) === \"0\" &&\n                stringify(new String()) == '\"\"' &&\n                // FF 3.1b1, 2 throw an error if the value is `null`, `undefined`, or\n                // does not define a canonical JSON representation (this applies to\n                // objects with `toJSON` properties as well, *unless* they are nested\n                // within an object or array).\n                stringify(getClass) === undef &&\n                // IE 8 serializes `undefined` as `\"undefined\"`. Safari <= 5.1.7 and\n                // FF 3.1b3 pass this test.\n                stringify(undef) === undef &&\n                // Safari <= 5.1.7 and FF 3.1b3 throw `Error`s and `TypeError`s,\n                // respectively, if the value is omitted entirely.\n                stringify() === undef &&\n                // FF 3.1b1, 2 throw an error if the given value is not a number,\n                // string, array, object, Boolean, or `null` literal. This applies to\n                // objects with custom `toJSON` methods as well, unless they are nested\n                // inside object or array literals. YUI 3.0.0b1 ignores custom `toJSON`\n                // methods entirely.\n                stringify(value) === \"1\" &&\n                stringify([value]) == \"[1]\" &&\n                // Prototype <= 1.6.1 serializes `[undefined]` as `\"[]\"` instead of\n                // `\"[null]\"`.\n                stringify([undef]) == \"[null]\" &&\n                // YUI 3.0.0b1 fails to serialize `null` literals.\n                stringify(null) == \"null\" &&\n                // FF 3.1b1, 2 halts serialization if an array contains a function:\n                // `[1, true, getClass, 1]` serializes as \"[1,true,],\". FF 3.1b3\n                // elides non-JSON values from objects and arrays, unless they\n                // define custom `toJSON` methods.\n                stringify([undef, getClass, null]) == \"[null,null,null]\" &&\n                // Simple serialization test. FF 3.1b1 uses Unicode escape sequences\n                // where character escape codes are expected (e.g., `\\b` => `\\u0008`).\n                stringify({ \"a\": [value, true, false, null, \"\\x00\\b\\n\\f\\r\\t\"] }) == serialized &&\n                // FF 3.1b1 and b2 ignore the `filter` and `width` arguments.\n                stringify(null, value) === \"1\" &&\n                stringify([1, 2], null, 1) == \"[\\n 1,\\n 2\\n]\" &&\n                // JSON 2, Prototype <= 1.7, and older WebKit builds incorrectly\n                // serialize extended years.\n                stringify(new Date(-8.64e15)) == '\"-271821-04-20T00:00:00.000Z\"' &&\n                // The milliseconds are optional in ES 5, but required in 5.1.\n                stringify(new Date(8.64e15)) == '\"+275760-09-13T00:00:00.000Z\"' &&\n                // Firefox <= 11.0 incorrectly serializes years prior to 0 as negative\n                // four-digit years instead of six-digit years. Credits: @Yaffle.\n                stringify(new Date(-621987552e5)) == '\"-000001-01-01T00:00:00.000Z\"' &&\n                // Safari <= 5.1.5 and Opera >= 10.53 incorrectly serialize millisecond\n                // values less than 1000. Credits: @Yaffle.\n                stringify(new Date(-1)) == '\"1969-12-31T23:59:59.999Z\"';\n            } catch (exception) {\n              stringifySupported = false;\n            }\n          }\n          isSupported = stringifySupported;\n        }\n        // Test `JSON.parse`.\n        if (name == \"json-parse\") {\n          var parse = exports.parse;\n          if (typeof parse == \"function\") {\n            try {\n              // FF 3.1b1, b2 will throw an exception if a bare literal is provided.\n              // Conforming implementations should also coerce the initial argument to\n              // a string prior to parsing.\n              if (parse(\"0\") === 0 && !parse(false)) {\n                // Simple parsing test.\n                value = parse(serialized);\n                var parseSupported = value[\"a\"].length == 5 && value[\"a\"][0] === 1;\n                if (parseSupported) {\n                  try {\n                    // Safari <= 5.1.2 and FF 3.1b1 allow unescaped tabs in strings.\n                    parseSupported = !parse('\"\\t\"');\n                  } catch (exception) {}\n                  if (parseSupported) {\n                    try {\n                      // FF 4.0 and 4.0.1 allow leading `+` signs and leading\n                      // decimal points. FF 4.0, 4.0.1, and IE 9-10 also allow\n                      // certain octal literals.\n                      parseSupported = parse(\"01\") !== 1;\n                    } catch (exception) {}\n                  }\n                  if (parseSupported) {\n                    try {\n                      // FF 4.0, 4.0.1, and Rhino 1.7R3-R4 allow trailing decimal\n                      // points. These environments, along with FF 3.1b1 and 2,\n                      // also allow trailing commas in JSON objects and arrays.\n                      parseSupported = parse(\"1.\") !== 1;\n                    } catch (exception) {}\n                  }\n                }\n              }\n            } catch (exception) {\n              parseSupported = false;\n            }\n          }\n          isSupported = parseSupported;\n        }\n      }\n      return has[name] = !!isSupported;\n    }\n\n    if (!has(\"json\")) {\n      // Common `[[Class]]` name aliases.\n      var functionClass = \"[object Function]\",\n          dateClass = \"[object Date]\",\n          numberClass = \"[object Number]\",\n          stringClass = \"[object String]\",\n          arrayClass = \"[object Array]\",\n          booleanClass = \"[object Boolean]\";\n\n      // Detect incomplete support for accessing string characters by index.\n      var charIndexBuggy = has(\"bug-string-char-index\");\n\n      // Define additional utility methods if the `Date` methods are buggy.\n      if (!isExtended) {\n        var floor = Math.floor;\n        // A mapping between the months of the year and the number of days between\n        // January 1st and the first of the respective month.\n        var Months = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334];\n        // Internal: Calculates the number of days between the Unix epoch and the\n        // first day of the given month.\n        var getDay = function (year, month) {\n          return Months[month] + 365 * (year - 1970) + floor((year - 1969 + (month = +(month > 1))) / 4) - floor((year - 1901 + month) / 100) + floor((year - 1601 + month) / 400);\n        };\n      }\n\n      // Internal: Determines if a property is a direct property of the given\n      // object. Delegates to the native `Object#hasOwnProperty` method.\n      if (!(isProperty = objectProto.hasOwnProperty)) {\n        isProperty = function (property) {\n          var members = {}, constructor;\n          if ((members.__proto__ = null, members.__proto__ = {\n            // The *proto* property cannot be set multiple times in recent\n            // versions of Firefox and SeaMonkey.\n            \"toString\": 1\n          }, members).toString != getClass) {\n            // Safari <= 2.0.3 doesn't implement `Object#hasOwnProperty`, but\n            // supports the mutable *proto* property.\n            isProperty = function (property) {\n              // Capture and break the object's prototype chain (see section 8.6.2\n              // of the ES 5.1 spec). The parenthesized expression prevents an\n              // unsafe transformation by the Closure Compiler.\n              var original = this.__proto__, result = property in (this.__proto__ = null, this);\n              // Restore the original prototype chain.\n              this.__proto__ = original;\n              return result;\n            };\n          } else {\n            // Capture a reference to the top-level `Object` constructor.\n            constructor = members.constructor;\n            // Use the `constructor` property to simulate `Object#hasOwnProperty` in\n            // other environments.\n            isProperty = function (property) {\n              var parent = (this.constructor || constructor).prototype;\n              return property in this && !(property in parent && this[property] === parent[property]);\n            };\n          }\n          members = null;\n          return isProperty.call(this, property);\n        };\n      }\n\n      // Internal: Normalizes the `for...in` iteration algorithm across\n      // environments. Each enumerated key is yielded to a `callback` function.\n      forEach = function (object, callback) {\n        var size = 0, Properties, members, property;\n\n        // Tests for bugs in the current environment's `for...in` algorithm. The\n        // `valueOf` property inherits the non-enumerable flag from\n        // `Object.prototype` in older versions of IE, Netscape, and Mozilla.\n        (Properties = function () {\n          this.valueOf = 0;\n        }).prototype.valueOf = 0;\n\n        // Iterate over a new instance of the `Properties` class.\n        members = new Properties();\n        for (property in members) {\n          // Ignore all properties inherited from `Object.prototype`.\n          if (isProperty.call(members, property)) {\n            size++;\n          }\n        }\n        Properties = members = null;\n\n        // Normalize the iteration algorithm.\n        if (!size) {\n          // A list of non-enumerable properties inherited from `Object.prototype`.\n          members = [\"valueOf\", \"toString\", \"toLocaleString\", \"propertyIsEnumerable\", \"isPrototypeOf\", \"hasOwnProperty\", \"constructor\"];\n          // IE <= 8, Mozilla 1.0, and Netscape 6.2 ignore shadowed non-enumerable\n          // properties.\n          forEach = function (object, callback) {\n            var isFunction = getClass.call(object) == functionClass, property, length;\n            var hasProperty = !isFunction && typeof object.constructor != \"function\" && objectTypes[typeof object.hasOwnProperty] && object.hasOwnProperty || isProperty;\n            for (property in object) {\n              // Gecko <= 1.0 enumerates the `prototype` property of functions under\n              // certain conditions; IE does not.\n              if (!(isFunction && property == \"prototype\") && hasProperty.call(object, property)) {\n                callback(property);\n              }\n            }\n            // Manually invoke the callback for each non-enumerable property.\n            for (length = members.length; property = members[--length]; hasProperty.call(object, property) && callback(property));\n          };\n        } else if (size == 2) {\n          // Safari <= 2.0.4 enumerates shadowed properties twice.\n          forEach = function (object, callback) {\n            // Create a set of iterated properties.\n            var members = {}, isFunction = getClass.call(object) == functionClass, property;\n            for (property in object) {\n              // Store each property name to prevent double enumeration. The\n              // `prototype` property of functions is not enumerated due to cross-\n              // environment inconsistencies.\n              if (!(isFunction && property == \"prototype\") && !isProperty.call(members, property) && (members[property] = 1) && isProperty.call(object, property)) {\n                callback(property);\n              }\n            }\n          };\n        } else {\n          // No bugs detected; use the standard `for...in` algorithm.\n          forEach = function (object, callback) {\n            var isFunction = getClass.call(object) == functionClass, property, isConstructor;\n            for (property in object) {\n              if (!(isFunction && property == \"prototype\") && isProperty.call(object, property) && !(isConstructor = property === \"constructor\")) {\n                callback(property);\n              }\n            }\n            // Manually invoke the callback for the `constructor` property due to\n            // cross-environment inconsistencies.\n            if (isConstructor || isProperty.call(object, (property = \"constructor\"))) {\n              callback(property);\n            }\n          };\n        }\n        return forEach(object, callback);\n      };\n\n      // Public: Serializes a JavaScript `value` as a JSON string. The optional\n      // `filter` argument may specify either a function that alters how object and\n      // array members are serialized, or an array of strings and numbers that\n      // indicates which properties should be serialized. The optional `width`\n      // argument may be either a string or number that specifies the indentation\n      // level of the output.\n      if (!has(\"json-stringify\")) {\n        // Internal: A map of control characters and their escaped equivalents.\n        var Escapes = {\n          92: \"\\\\\\\\\",\n          34: '\\\\\"',\n          8: \"\\\\b\",\n          12: \"\\\\f\",\n          10: \"\\\\n\",\n          13: \"\\\\r\",\n          9: \"\\\\t\"\n        };\n\n        // Internal: Converts `value` into a zero-padded string such that its\n        // length is at least equal to `width`. The `width` must be <= 6.\n        var leadingZeroes = \"000000\";\n        var toPaddedString = function (width, value) {\n          // The `|| 0` expression is necessary to work around a bug in\n          // Opera <= 7.54u2 where `0 == -0`, but `String(-0) !== \"0\"`.\n          return (leadingZeroes + (value || 0)).slice(-width);\n        };\n\n        // Internal: Double-quotes a string `value`, replacing all ASCII control\n        // characters (characters with code unit values between 0 and 31) with\n        // their escaped equivalents. This is an implementation of the\n        // `Quote(value)` operation defined in ES 5.1 section 15.12.3.\n        var unicodePrefix = \"\\\\u00\";\n        var quote = function (value) {\n          var result = '\"', index = 0, length = value.length, useCharIndex = !charIndexBuggy || length > 10;\n          var symbols = useCharIndex && (charIndexBuggy ? value.split(\"\") : value);\n          for (; index < length; index++) {\n            var charCode = value.charCodeAt(index);\n            // If the character is a control character, append its Unicode or\n            // shorthand escape sequence; otherwise, append the character as-is.\n            switch (charCode) {\n              case 8: case 9: case 10: case 12: case 13: case 34: case 92:\n                result += Escapes[charCode];\n                break;\n              default:\n                if (charCode < 32) {\n                  result += unicodePrefix + toPaddedString(2, charCode.toString(16));\n                  break;\n                }\n                result += useCharIndex ? symbols[index] : value.charAt(index);\n            }\n          }\n          return result + '\"';\n        };\n\n        // Internal: Recursively serializes an object. Implements the\n        // `Str(key, holder)`, `JO(value)`, and `JA(value)` operations.\n        var serialize = function (property, object, callback, properties, whitespace, indentation, stack) {\n          var value, className, year, month, date, time, hours, minutes, seconds, milliseconds, results, element, index, length, prefix, result;\n          try {\n            // Necessary for host object support.\n            value = object[property];\n          } catch (exception) {}\n          if (typeof value == \"object\" && value) {\n            className = getClass.call(value);\n            if (className == dateClass && !isProperty.call(value, \"toJSON\")) {\n              if (value > -1 / 0 && value < 1 / 0) {\n                // Dates are serialized according to the `Date#toJSON` method\n                // specified in ES 5.1 section *********. See section *********\n                // for the ISO 8601 date time string format.\n                if (getDay) {\n                  // Manually compute the year, month, date, hours, minutes,\n                  // seconds, and milliseconds if the `getUTC*` methods are\n                  // buggy. Adapted from @Yaffle's `date-shim` project.\n                  date = floor(value / 864e5);\n                  for (year = floor(date / 365.2425) + 1970 - 1; getDay(year + 1, 0) <= date; year++);\n                  for (month = floor((date - getDay(year, 0)) / 30.42); getDay(year, month + 1) <= date; month++);\n                  date = 1 + date - getDay(year, month);\n                  // The `time` value specifies the time within the day (see ES\n                  // 5.1 section 15.9.1.2). The formula `(A % B + B) % B` is used\n                  // to compute `A modulo B`, as the `%` operator does not\n                  // correspond to the `modulo` operation for negative numbers.\n                  time = (value % 864e5 + 864e5) % 864e5;\n                  // The hours, minutes, seconds, and milliseconds are obtained by\n                  // decomposing the time within the day. See section 15.9.1.10.\n                  hours = floor(time / 36e5) % 24;\n                  minutes = floor(time / 6e4) % 60;\n                  seconds = floor(time / 1e3) % 60;\n                  milliseconds = time % 1e3;\n                } else {\n                  year = value.getUTCFullYear();\n                  month = value.getUTCMonth();\n                  date = value.getUTCDate();\n                  hours = value.getUTCHours();\n                  minutes = value.getUTCMinutes();\n                  seconds = value.getUTCSeconds();\n                  milliseconds = value.getUTCMilliseconds();\n                }\n                // Serialize extended years correctly.\n                value = (year <= 0 || year >= 1e4 ? (year < 0 ? \"-\" : \"+\") + toPaddedString(6, year < 0 ? -year : year) : toPaddedString(4, year)) +\n                  \"-\" + toPaddedString(2, month + 1) + \"-\" + toPaddedString(2, date) +\n                  // Months, dates, hours, minutes, and seconds should have two\n                  // digits; milliseconds should have three.\n                  \"T\" + toPaddedString(2, hours) + \":\" + toPaddedString(2, minutes) + \":\" + toPaddedString(2, seconds) +\n                  // Milliseconds are optional in ES 5.0, but required in 5.1.\n                  \".\" + toPaddedString(3, milliseconds) + \"Z\";\n              } else {\n                value = null;\n              }\n            } else if (typeof value.toJSON == \"function\" && ((className != numberClass && className != stringClass && className != arrayClass) || isProperty.call(value, \"toJSON\"))) {\n              // Prototype <= 1.6.1 adds non-standard `toJSON` methods to the\n              // `Number`, `String`, `Date`, and `Array` prototypes. JSON 3\n              // ignores all `toJSON` methods on these objects unless they are\n              // defined directly on an instance.\n              value = value.toJSON(property);\n            }\n          }\n          if (callback) {\n            // If a replacement function was provided, call it to obtain the value\n            // for serialization.\n            value = callback.call(object, property, value);\n          }\n          if (value === null) {\n            return \"null\";\n          }\n          className = getClass.call(value);\n          if (className == booleanClass) {\n            // Booleans are represented literally.\n            return \"\" + value;\n          } else if (className == numberClass) {\n            // JSON numbers must be finite. `Infinity` and `NaN` are serialized as\n            // `\"null\"`.\n            return value > -1 / 0 && value < 1 / 0 ? \"\" + value : \"null\";\n          } else if (className == stringClass) {\n            // Strings are double-quoted and escaped.\n            return quote(\"\" + value);\n          }\n          // Recursively serialize objects and arrays.\n          if (typeof value == \"object\") {\n            // Check for cyclic structures. This is a linear search; performance\n            // is inversely proportional to the number of unique nested objects.\n            for (length = stack.length; length--;) {\n              if (stack[length] === value) {\n                // Cyclic structures cannot be serialized by `JSON.stringify`.\n                throw TypeError();\n              }\n            }\n            // Add the object to the stack of traversed objects.\n            stack.push(value);\n            results = [];\n            // Save the current indentation level and indent one additional level.\n            prefix = indentation;\n            indentation += whitespace;\n            if (className == arrayClass) {\n              // Recursively serialize array elements.\n              for (index = 0, length = value.length; index < length; index++) {\n                element = serialize(index, value, callback, properties, whitespace, indentation, stack);\n                results.push(element === undef ? \"null\" : element);\n              }\n              result = results.length ? (whitespace ? \"[\\n\" + indentation + results.join(\",\\n\" + indentation) + \"\\n\" + prefix + \"]\" : (\"[\" + results.join(\",\") + \"]\")) : \"[]\";\n            } else {\n              // Recursively serialize object members. Members are selected from\n              // either a user-specified list of property names, or the object\n              // itself.\n              forEach(properties || value, function (property) {\n                var element = serialize(property, value, callback, properties, whitespace, indentation, stack);\n                if (element !== undef) {\n                  // According to ES 5.1 section 15.12.3: \"If `gap` {whitespace}\n                  // is not the empty string, let `member` {quote(property) + \":\"}\n                  // be the concatenation of `member` and the `space` character.\"\n                  // The \"`space` character\" refers to the literal space\n                  // character, not the `space` {width} argument provided to\n                  // `JSON.stringify`.\n                  results.push(quote(property) + \":\" + (whitespace ? \" \" : \"\") + element);\n                }\n              });\n              result = results.length ? (whitespace ? \"{\\n\" + indentation + results.join(\",\\n\" + indentation) + \"\\n\" + prefix + \"}\" : (\"{\" + results.join(\",\") + \"}\")) : \"{}\";\n            }\n            // Remove the object from the traversed object stack.\n            stack.pop();\n            return result;\n          }\n        };\n\n        // Public: `JSON.stringify`. See ES 5.1 section 15.12.3.\n        exports.stringify = function (source, filter, width) {\n          var whitespace, callback, properties, className;\n          if (objectTypes[typeof filter] && filter) {\n            if ((className = getClass.call(filter)) == functionClass) {\n              callback = filter;\n            } else if (className == arrayClass) {\n              // Convert the property names array into a makeshift set.\n              properties = {};\n              for (var index = 0, length = filter.length, value; index < length; value = filter[index++], ((className = getClass.call(value)), className == stringClass || className == numberClass) && (properties[value] = 1));\n            }\n          }\n          if (width) {\n            if ((className = getClass.call(width)) == numberClass) {\n              // Convert the `width` to an integer and create a string containing\n              // `width` number of space characters.\n              if ((width -= width % 1) > 0) {\n                for (whitespace = \"\", width > 10 && (width = 10); whitespace.length < width; whitespace += \" \");\n              }\n            } else if (className == stringClass) {\n              whitespace = width.length <= 10 ? width : width.slice(0, 10);\n            }\n          }\n          // Opera <= 7.54u2 discards the values associated with empty string keys\n          // (`\"\"`) only if they are used directly within an object member list\n          // (e.g., `!(\"\" in { \"\": 1})`).\n          return serialize(\"\", (value = {}, value[\"\"] = source, value), callback, properties, whitespace, \"\", []);\n        };\n      }\n\n      // Public: Parses a JSON source string.\n      if (!has(\"json-parse\")) {\n        var fromCharCode = String.fromCharCode;\n\n        // Internal: A map of escaped control characters and their unescaped\n        // equivalents.\n        var Unescapes = {\n          92: \"\\\\\",\n          34: '\"',\n          47: \"/\",\n          98: \"\\b\",\n          116: \"\\t\",\n          110: \"\\n\",\n          102: \"\\f\",\n          114: \"\\r\"\n        };\n\n        // Internal: Stores the parser state.\n        var Index, Source;\n\n        // Internal: Resets the parser state and throws a `SyntaxError`.\n        var abort = function () {\n          Index = Source = null;\n          throw SyntaxError();\n        };\n\n        // Internal: Returns the next token, or `\"$\"` if the parser has reached\n        // the end of the source string. A token may be a string, number, `null`\n        // literal, or Boolean literal.\n        var lex = function () {\n          var source = Source, length = source.length, value, begin, position, isSigned, charCode;\n          while (Index < length) {\n            charCode = source.charCodeAt(Index);\n            switch (charCode) {\n              case 9: case 10: case 13: case 32:\n                // Skip whitespace tokens, including tabs, carriage returns, line\n                // feeds, and space characters.\n                Index++;\n                break;\n              case 123: case 125: case 91: case 93: case 58: case 44:\n                // Parse a punctuator token (`{`, `}`, `[`, `]`, `:`, or `,`) at\n                // the current position.\n                value = charIndexBuggy ? source.charAt(Index) : source[Index];\n                Index++;\n                return value;\n              case 34:\n                // `\"` delimits a JSON string; advance to the next character and\n                // begin parsing the string. String tokens are prefixed with the\n                // sentinel `@` character to distinguish them from punctuators and\n                // end-of-string tokens.\n                for (value = \"@\", Index++; Index < length;) {\n                  charCode = source.charCodeAt(Index);\n                  if (charCode < 32) {\n                    // Unescaped ASCII control characters (those with a code unit\n                    // less than the space character) are not permitted.\n                    abort();\n                  } else if (charCode == 92) {\n                    // A reverse solidus (`\\`) marks the beginning of an escaped\n                    // control character (including `\"`, `\\`, and `/`) or Unicode\n                    // escape sequence.\n                    charCode = source.charCodeAt(++Index);\n                    switch (charCode) {\n                      case 92: case 34: case 47: case 98: case 116: case 110: case 102: case 114:\n                        // Revive escaped control characters.\n                        value += Unescapes[charCode];\n                        Index++;\n                        break;\n                      case 117:\n                        // `\\u` marks the beginning of a Unicode escape sequence.\n                        // Advance to the first character and validate the\n                        // four-digit code point.\n                        begin = ++Index;\n                        for (position = Index + 4; Index < position; Index++) {\n                          charCode = source.charCodeAt(Index);\n                          // A valid sequence comprises four hexdigits (case-\n                          // insensitive) that form a single hexadecimal value.\n                          if (!(charCode >= 48 && charCode <= 57 || charCode >= 97 && charCode <= 102 || charCode >= 65 && charCode <= 70)) {\n                            // Invalid Unicode escape sequence.\n                            abort();\n                          }\n                        }\n                        // Revive the escaped character.\n                        value += fromCharCode(\"0x\" + source.slice(begin, Index));\n                        break;\n                      default:\n                        // Invalid escape sequence.\n                        abort();\n                    }\n                  } else {\n                    if (charCode == 34) {\n                      // An unescaped double-quote character marks the end of the\n                      // string.\n                      break;\n                    }\n                    charCode = source.charCodeAt(Index);\n                    begin = Index;\n                    // Optimize for the common case where a string is valid.\n                    while (charCode >= 32 && charCode != 92 && charCode != 34) {\n                      charCode = source.charCodeAt(++Index);\n                    }\n                    // Append the string as-is.\n                    value += source.slice(begin, Index);\n                  }\n                }\n                if (source.charCodeAt(Index) == 34) {\n                  // Advance to the next character and return the revived string.\n                  Index++;\n                  return value;\n                }\n                // Unterminated string.\n                abort();\n              default:\n                // Parse numbers and literals.\n                begin = Index;\n                // Advance past the negative sign, if one is specified.\n                if (charCode == 45) {\n                  isSigned = true;\n                  charCode = source.charCodeAt(++Index);\n                }\n                // Parse an integer or floating-point value.\n                if (charCode >= 48 && charCode <= 57) {\n                  // Leading zeroes are interpreted as octal literals.\n                  if (charCode == 48 && ((charCode = source.charCodeAt(Index + 1)), charCode >= 48 && charCode <= 57)) {\n                    // Illegal octal literal.\n                    abort();\n                  }\n                  isSigned = false;\n                  // Parse the integer component.\n                  for (; Index < length && ((charCode = source.charCodeAt(Index)), charCode >= 48 && charCode <= 57); Index++);\n                  // Floats cannot contain a leading decimal point; however, this\n                  // case is already accounted for by the parser.\n                  if (source.charCodeAt(Index) == 46) {\n                    position = ++Index;\n                    // Parse the decimal component.\n                    for (; position < length && ((charCode = source.charCodeAt(position)), charCode >= 48 && charCode <= 57); position++);\n                    if (position == Index) {\n                      // Illegal trailing decimal.\n                      abort();\n                    }\n                    Index = position;\n                  }\n                  // Parse exponents. The `e` denoting the exponent is\n                  // case-insensitive.\n                  charCode = source.charCodeAt(Index);\n                  if (charCode == 101 || charCode == 69) {\n                    charCode = source.charCodeAt(++Index);\n                    // Skip past the sign following the exponent, if one is\n                    // specified.\n                    if (charCode == 43 || charCode == 45) {\n                      Index++;\n                    }\n                    // Parse the exponential component.\n                    for (position = Index; position < length && ((charCode = source.charCodeAt(position)), charCode >= 48 && charCode <= 57); position++);\n                    if (position == Index) {\n                      // Illegal empty exponent.\n                      abort();\n                    }\n                    Index = position;\n                  }\n                  // Coerce the parsed value to a JavaScript number.\n                  return +source.slice(begin, Index);\n                }\n                // A negative sign may only precede numbers.\n                if (isSigned) {\n                  abort();\n                }\n                // `true`, `false`, and `null` literals.\n                if (source.slice(Index, Index + 4) == \"true\") {\n                  Index += 4;\n                  return true;\n                } else if (source.slice(Index, Index + 5) == \"false\") {\n                  Index += 5;\n                  return false;\n                } else if (source.slice(Index, Index + 4) == \"null\") {\n                  Index += 4;\n                  return null;\n                }\n                // Unrecognized token.\n                abort();\n            }\n          }\n          // Return the sentinel `$` character if the parser has reached the end\n          // of the source string.\n          return \"$\";\n        };\n\n        // Internal: Parses a JSON `value` token.\n        var get = function (value) {\n          var results, hasMembers;\n          if (value == \"$\") {\n            // Unexpected end of input.\n            abort();\n          }\n          if (typeof value == \"string\") {\n            if ((charIndexBuggy ? value.charAt(0) : value[0]) == \"@\") {\n              // Remove the sentinel `@` character.\n              return value.slice(1);\n            }\n            // Parse object and array literals.\n            if (value == \"[\") {\n              // Parses a JSON array, returning a new JavaScript array.\n              results = [];\n              for (;; hasMembers || (hasMembers = true)) {\n                value = lex();\n                // A closing square bracket marks the end of the array literal.\n                if (value == \"]\") {\n                  break;\n                }\n                // If the array literal contains elements, the current token\n                // should be a comma separating the previous element from the\n                // next.\n                if (hasMembers) {\n                  if (value == \",\") {\n                    value = lex();\n                    if (value == \"]\") {\n                      // Unexpected trailing `,` in array literal.\n                      abort();\n                    }\n                  } else {\n                    // A `,` must separate each array element.\n                    abort();\n                  }\n                }\n                // Elisions and leading commas are not permitted.\n                if (value == \",\") {\n                  abort();\n                }\n                results.push(get(value));\n              }\n              return results;\n            } else if (value == \"{\") {\n              // Parses a JSON object, returning a new JavaScript object.\n              results = {};\n              for (;; hasMembers || (hasMembers = true)) {\n                value = lex();\n                // A closing curly brace marks the end of the object literal.\n                if (value == \"}\") {\n                  break;\n                }\n                // If the object literal contains members, the current token\n                // should be a comma separator.\n                if (hasMembers) {\n                  if (value == \",\") {\n                    value = lex();\n                    if (value == \"}\") {\n                      // Unexpected trailing `,` in object literal.\n                      abort();\n                    }\n                  } else {\n                    // A `,` must separate each object member.\n                    abort();\n                  }\n                }\n                // Leading commas are not permitted, object property names must be\n                // double-quoted strings, and a `:` must separate each property\n                // name and value.\n                if (value == \",\" || typeof value != \"string\" || (charIndexBuggy ? value.charAt(0) : value[0]) != \"@\" || lex() != \":\") {\n                  abort();\n                }\n                results[value.slice(1)] = get(lex());\n              }\n              return results;\n            }\n            // Unexpected token encountered.\n            abort();\n          }\n          return value;\n        };\n\n        // Internal: Updates a traversed object member.\n        var update = function (source, property, callback) {\n          var element = walk(source, property, callback);\n          if (element === undef) {\n            delete source[property];\n          } else {\n            source[property] = element;\n          }\n        };\n\n        // Internal: Recursively traverses a parsed JSON object, invoking the\n        // `callback` function for each value. This is an implementation of the\n        // `Walk(holder, name)` operation defined in ES 5.1 section 15.12.2.\n        var walk = function (source, property, callback) {\n          var value = source[property], length;\n          if (typeof value == \"object\" && value) {\n            // `forEach` can't be used to traverse an array in Opera <= 8.54\n            // because its `Object#hasOwnProperty` implementation returns `false`\n            // for array indices (e.g., `![1, 2, 3].hasOwnProperty(\"0\")`).\n            if (getClass.call(value) == arrayClass) {\n              for (length = value.length; length--;) {\n                update(value, length, callback);\n              }\n            } else {\n              forEach(value, function (property) {\n                update(value, property, callback);\n              });\n            }\n          }\n          return callback.call(source, property, value);\n        };\n\n        // Public: `JSON.parse`. See ES 5.1 section 15.12.2.\n        exports.parse = function (source, callback) {\n          var result, value;\n          Index = 0;\n          Source = \"\" + source;\n          result = get(lex());\n          // If a JSON string contains multiple tokens, it is invalid.\n          if (lex() != \"$\") {\n            abort();\n          }\n          // Reset the parser state.\n          Index = Source = null;\n          return callback && getClass.call(callback) == functionClass ? walk((value = {}, value[\"\"] = result, value), \"\", callback) : result;\n        };\n      }\n    }\n\n    exports[\"runInContext\"] = runInContext;\n    return exports;\n  }\n\n  if (freeExports && !isLoader) {\n    // Export for CommonJS environments.\n    runInContext(root, freeExports);\n  } else {\n    // Export for web browsers and JavaScript engines.\n    var nativeJSON = root.JSON,\n        previousJSON = root[\"JSON3\"],\n        isRestored = false;\n\n    var JSON3 = runInContext(root, (root[\"JSON3\"] = {\n      // Public: Restores the original value of the global `JSON` object and\n      // returns a reference to the `JSON3` object.\n      \"noConflict\": function () {\n        if (!isRestored) {\n          isRestored = true;\n          root.JSON = nativeJSON;\n          root[\"JSON3\"] = previousJSON;\n          nativeJSON = previousJSON = null;\n        }\n        return JSON3;\n      }\n    }));\n\n    root.JSON = {\n      \"parse\": JSON3.parse,\n      \"stringify\": JSON3.stringify\n    };\n  }\n\n  // Export for asynchronous module loaders.\n  if (isLoader) {\n    define(function () {\n      return JSON3;\n    });\n  }\n}).call(this);\n\n}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n\n},{}],5:[function(require,module,exports){\n// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n},{}],6:[function(require,module,exports){\n/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar has = Object.prototype.hasOwnProperty;\n\n/**\n * A data structure which is a combination of an array and a set. Adding a new\n * member is O(1), testing for membership is O(1), and finding the index of an\n * element is O(1). Removing elements from the set is not supported. Only\n * strings are supported for membership.\n */\nfunction ArraySet() {\n  this._array = [];\n  this._set = Object.create(null);\n}\n\n/**\n * Static method for creating ArraySet instances from an existing array.\n */\nArraySet.fromArray = function ArraySet_fromArray(aArray, aAllowDuplicates) {\n  var set = new ArraySet();\n  for (var i = 0, len = aArray.length; i < len; i++) {\n    set.add(aArray[i], aAllowDuplicates);\n  }\n  return set;\n};\n\n/**\n * Return how many unique items are in this ArraySet. If duplicates have been\n * added, than those do not count towards the size.\n *\n * @returns Number\n */\nArraySet.prototype.size = function ArraySet_size() {\n  return Object.getOwnPropertyNames(this._set).length;\n};\n\n/**\n * Add the given string to this set.\n *\n * @param String aStr\n */\nArraySet.prototype.add = function ArraySet_add(aStr, aAllowDuplicates) {\n  var sStr = util.toSetString(aStr);\n  var isDuplicate = has.call(this._set, sStr);\n  var idx = this._array.length;\n  if (!isDuplicate || aAllowDuplicates) {\n    this._array.push(aStr);\n  }\n  if (!isDuplicate) {\n    this._set[sStr] = idx;\n  }\n};\n\n/**\n * Is the given string a member of this set?\n *\n * @param String aStr\n */\nArraySet.prototype.has = function ArraySet_has(aStr) {\n  var sStr = util.toSetString(aStr);\n  return has.call(this._set, sStr);\n};\n\n/**\n * What is the index of the given string in the array?\n *\n * @param String aStr\n */\nArraySet.prototype.indexOf = function ArraySet_indexOf(aStr) {\n  var sStr = util.toSetString(aStr);\n  if (has.call(this._set, sStr)) {\n    return this._set[sStr];\n  }\n  throw new Error('\"' + aStr + '\" is not in the set.');\n};\n\n/**\n * What is the element at the given index?\n *\n * @param Number aIdx\n */\nArraySet.prototype.at = function ArraySet_at(aIdx) {\n  if (aIdx >= 0 && aIdx < this._array.length) {\n    return this._array[aIdx];\n  }\n  throw new Error('No element indexed by ' + aIdx);\n};\n\n/**\n * Returns the array representation of this set (which has the proper indices\n * indicated by indexOf). Note that this is a copy of the internal array used\n * for storing the members so that no one can mess with internal state.\n */\nArraySet.prototype.toArray = function ArraySet_toArray() {\n  return this._array.slice();\n};\n\nexports.ArraySet = ArraySet;\n\n},{\"./util\":12}],7:[function(require,module,exports){\n/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n *\n * Based on the Base 64 VLQ implementation in Closure Compiler:\n * https://code.google.com/p/closure-compiler/source/browse/trunk/src/com/google/debugging/sourcemap/Base64VLQ.java\n *\n * Copyright 2011 The Closure Compiler Authors. All rights reserved.\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *  * Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above\n *    copyright notice, this list of conditions and the following\n *    disclaimer in the documentation and/or other materials provided\n *    with the distribution.\n *  * Neither the name of Google Inc. nor the names of its\n *    contributors may be used to endorse or promote products derived\n *    from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n * \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar base64 = require('./base64');\n\n// A single base 64 digit can contain 6 bits of data. For the base 64 variable\n// length quantities we use in the source map spec, the first bit is the sign,\n// the next four bits are the actual value, and the 6th bit is the\n// continuation bit. The continuation bit tells us whether there are more\n// digits in this value following this digit.\n//\n//   Continuation\n//   |    Sign\n//   |    |\n//   V    V\n//   101011\n\nvar VLQ_BASE_SHIFT = 5;\n\n// binary: 100000\nvar VLQ_BASE = 1 << VLQ_BASE_SHIFT;\n\n// binary: 011111\nvar VLQ_BASE_MASK = VLQ_BASE - 1;\n\n// binary: 100000\nvar VLQ_CONTINUATION_BIT = VLQ_BASE;\n\n/**\n * Converts from a two-complement value to a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   1 becomes 2 (10 binary), -1 becomes 3 (11 binary)\n *   2 becomes 4 (100 binary), -2 becomes 5 (101 binary)\n */\nfunction toVLQSigned(aValue) {\n  return aValue < 0\n    ? ((-aValue) << 1) + 1\n    : (aValue << 1) + 0;\n}\n\n/**\n * Converts to a two-complement value from a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   2 (10 binary) becomes 1, 3 (11 binary) becomes -1\n *   4 (100 binary) becomes 2, 5 (101 binary) becomes -2\n */\nfunction fromVLQSigned(aValue) {\n  var isNegative = (aValue & 1) === 1;\n  var shifted = aValue >> 1;\n  return isNegative\n    ? -shifted\n    : shifted;\n}\n\n/**\n * Returns the base 64 VLQ encoded value.\n */\nexports.encode = function base64VLQ_encode(aValue) {\n  var encoded = \"\";\n  var digit;\n\n  var vlq = toVLQSigned(aValue);\n\n  do {\n    digit = vlq & VLQ_BASE_MASK;\n    vlq >>>= VLQ_BASE_SHIFT;\n    if (vlq > 0) {\n      // There are still more digits in this value, so we must make sure the\n      // continuation bit is marked.\n      digit |= VLQ_CONTINUATION_BIT;\n    }\n    encoded += base64.encode(digit);\n  } while (vlq > 0);\n\n  return encoded;\n};\n\n/**\n * Decodes the next base 64 VLQ value from the given string and returns the\n * value and the rest of the string via the out parameter.\n */\nexports.decode = function base64VLQ_decode(aStr, aIndex, aOutParam) {\n  var strLen = aStr.length;\n  var result = 0;\n  var shift = 0;\n  var continuation, digit;\n\n  do {\n    if (aIndex >= strLen) {\n      throw new Error(\"Expected more digits in base 64 VLQ value.\");\n    }\n\n    digit = base64.decode(aStr.charCodeAt(aIndex++));\n    if (digit === -1) {\n      throw new Error(\"Invalid base64 digit: \" + aStr.charAt(aIndex - 1));\n    }\n\n    continuation = !!(digit & VLQ_CONTINUATION_BIT);\n    digit &= VLQ_BASE_MASK;\n    result = result + (digit << shift);\n    shift += VLQ_BASE_SHIFT;\n  } while (continuation);\n\n  aOutParam.value = fromVLQSigned(result);\n  aOutParam.rest = aIndex;\n};\n\n},{\"./base64\":8}],8:[function(require,module,exports){\n/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar intToCharMap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/'.split('');\n\n/**\n * Encode an integer in the range of 0 to 63 to a single base 64 digit.\n */\nexports.encode = function (number) {\n  if (0 <= number && number < intToCharMap.length) {\n    return intToCharMap[number];\n  }\n  throw new TypeError(\"Must be between 0 and 63: \" + number);\n};\n\n/**\n * Decode a single base 64 character code digit to an integer. Returns -1 on\n * failure.\n */\nexports.decode = function (charCode) {\n  var bigA = 65;     // 'A'\n  var bigZ = 90;     // 'Z'\n\n  var littleA = 97;  // 'a'\n  var littleZ = 122; // 'z'\n\n  var zero = 48;     // '0'\n  var nine = 57;     // '9'\n\n  var plus = 43;     // '+'\n  var slash = 47;    // '/'\n\n  var littleOffset = 26;\n  var numberOffset = 52;\n\n  // 0 - 25: ABCDEFGHIJKLMNOPQRSTUVWXYZ\n  if (bigA <= charCode && charCode <= bigZ) {\n    return (charCode - bigA);\n  }\n\n  // 26 - 51: abcdefghijklmnopqrstuvwxyz\n  if (littleA <= charCode && charCode <= littleZ) {\n    return (charCode - littleA + littleOffset);\n  }\n\n  // 52 - 61: **********\n  if (zero <= charCode && charCode <= nine) {\n    return (charCode - zero + numberOffset);\n  }\n\n  // 62: +\n  if (charCode == plus) {\n    return 62;\n  }\n\n  // 63: /\n  if (charCode == slash) {\n    return 63;\n  }\n\n  // Invalid base64 digit.\n  return -1;\n};\n\n},{}],9:[function(require,module,exports){\n/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nexports.GREATEST_LOWER_BOUND = 1;\nexports.LEAST_UPPER_BOUND = 2;\n\n/**\n * Recursive implementation of binary search.\n *\n * @param aLow Indices here and lower do not contain the needle.\n * @param aHigh Indices here and higher do not contain the needle.\n * @param aNeedle The element being searched for.\n * @param aHaystack The non-empty array being searched.\n * @param aCompare Function which takes two elements and returns -1, 0, or 1.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n */\nfunction recursiveSearch(aLow, aHigh, aNeedle, aHaystack, aCompare, aBias) {\n  // This function terminates when one of the following is true:\n  //\n  //   1. We find the exact element we are looking for.\n  //\n  //   2. We did not find the exact element, but we can return the index of\n  //      the next-closest element.\n  //\n  //   3. We did not find the exact element, and there is no next-closest\n  //      element than the one we are searching for, so we return -1.\n  var mid = Math.floor((aHigh - aLow) / 2) + aLow;\n  var cmp = aCompare(aNeedle, aHaystack[mid], true);\n  if (cmp === 0) {\n    // Found the element we are looking for.\n    return mid;\n  }\n  else if (cmp > 0) {\n    // Our needle is greater than aHaystack[mid].\n    if (aHigh - mid > 1) {\n      // The element is in the upper half.\n      return recursiveSearch(mid, aHigh, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // The exact needle element was not found in this haystack. Determine if\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return aHigh < aHaystack.length ? aHigh : -1;\n    } else {\n      return mid;\n    }\n  }\n  else {\n    // Our needle is less than aHaystack[mid].\n    if (mid - aLow > 1) {\n      // The element is in the lower half.\n      return recursiveSearch(aLow, mid, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return mid;\n    } else {\n      return aLow < 0 ? -1 : aLow;\n    }\n  }\n}\n\n/**\n * This is an implementation of binary search which will always try and return\n * the index of the closest element if there is no exact hit. This is because\n * mappings between original and generated line/col pairs are single points,\n * and there is an implicit region between each of them, so a miss just means\n * that you aren't on the very start of a region.\n *\n * @param aNeedle The element you are looking for.\n * @param aHaystack The array that is being searched.\n * @param aCompare A function which takes the needle and an element in the\n *     array and returns -1, 0, or 1 depending on whether the needle is less\n *     than, equal to, or greater than the element, respectively.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'binarySearch.GREATEST_LOWER_BOUND'.\n */\nexports.search = function search(aNeedle, aHaystack, aCompare, aBias) {\n  if (aHaystack.length === 0) {\n    return -1;\n  }\n\n  var index = recursiveSearch(-1, aHaystack.length, aNeedle, aHaystack,\n                              aCompare, aBias || exports.GREATEST_LOWER_BOUND);\n  if (index < 0) {\n    return -1;\n  }\n\n  // We have found either the exact element, or the next-closest element than\n  // the one we are searching for. However, there may be more than one such\n  // element. Make sure we always return the smallest of these.\n  while (index - 1 >= 0) {\n    if (aCompare(aHaystack[index], aHaystack[index - 1], true) !== 0) {\n      break;\n    }\n    --index;\n  }\n\n  return index;\n};\n\n},{}],10:[function(require,module,exports){\n/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n// It turns out that some (most?) JavaScript engines don't self-host\n// `Array.prototype.sort`. This makes sense because C++ will likely remain\n// faster than JS when doing raw CPU-intensive sorting. However, when using a\n// custom comparator function, calling back and forth between the VM's C++ and\n// JIT'd JS is rather slow *and* loses JIT type information, resulting in\n// worse generated code for the comparator function than would be optimal. In\n// fact, when sorting with a comparator, these costs outweigh the benefits of\n// sorting in C++. By using our own JS-implemented Quick Sort (below), we get\n// a ~3500ms mean speed-up in `bench/bench.html`.\n\n/**\n * Swap the elements indexed by `x` and `y` in the array `ary`.\n *\n * @param {Array} ary\n *        The array.\n * @param {Number} x\n *        The index of the first item.\n * @param {Number} y\n *        The index of the second item.\n */\nfunction swap(ary, x, y) {\n  var temp = ary[x];\n  ary[x] = ary[y];\n  ary[y] = temp;\n}\n\n/**\n * Returns a random integer within the range `low .. high` inclusive.\n *\n * @param {Number} low\n *        The lower bound on the range.\n * @param {Number} high\n *        The upper bound on the range.\n */\nfunction randomIntInRange(low, high) {\n  return Math.round(low + (Math.random() * (high - low)));\n}\n\n/**\n * The Quick Sort algorithm.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n * @param {Number} p\n *        Start index of the array\n * @param {Number} r\n *        End index of the array\n */\nfunction doQuickSort(ary, comparator, p, r) {\n  // If our lower bound is less than our upper bound, we (1) partition the\n  // array into two pieces and (2) recurse on each half. If it is not, this is\n  // the empty array and our base case.\n\n  if (p < r) {\n    // (1) Partitioning.\n    //\n    // The partitioning chooses a pivot between `p` and `r` and moves all\n    // elements that are less than or equal to the pivot to the before it, and\n    // all the elements that are greater than it after it. The effect is that\n    // once partition is done, the pivot is in the exact place it will be when\n    // the array is put in sorted order, and it will not need to be moved\n    // again. This runs in O(n) time.\n\n    // Always choose a random pivot so that an input array which is reverse\n    // sorted does not cause O(n^2) running time.\n    var pivotIndex = randomIntInRange(p, r);\n    var i = p - 1;\n\n    swap(ary, pivotIndex, r);\n    var pivot = ary[r];\n\n    // Immediately after `j` is incremented in this loop, the following hold\n    // true:\n    //\n    //   * Every element in `ary[p .. i]` is less than or equal to the pivot.\n    //\n    //   * Every element in `ary[i+1 .. j-1]` is greater than the pivot.\n    for (var j = p; j < r; j++) {\n      if (comparator(ary[j], pivot) <= 0) {\n        i += 1;\n        swap(ary, i, j);\n      }\n    }\n\n    swap(ary, i + 1, j);\n    var q = i + 1;\n\n    // (2) Recurse on each half.\n\n    doQuickSort(ary, comparator, p, q - 1);\n    doQuickSort(ary, comparator, q + 1, r);\n  }\n}\n\n/**\n * Sort the given array in-place with the given comparator function.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n */\nexports.quickSort = function (ary, comparator) {\n  doQuickSort(ary, comparator, 0, ary.length - 1);\n};\n\n},{}],11:[function(require,module,exports){\n/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar binarySearch = require('./binary-search');\nvar ArraySet = require('./array-set').ArraySet;\nvar base64VLQ = require('./base64-vlq');\nvar quickSort = require('./quick-sort').quickSort;\n\nfunction SourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  return sourceMap.sections != null\n    ? new IndexedSourceMapConsumer(sourceMap)\n    : new BasicSourceMapConsumer(sourceMap);\n}\n\nSourceMapConsumer.fromSourceMap = function(aSourceMap) {\n  return BasicSourceMapConsumer.fromSourceMap(aSourceMap);\n}\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nSourceMapConsumer.prototype._version = 3;\n\n// `__generatedMappings` and `__originalMappings` are arrays that hold the\n// parsed mapping coordinates from the source map's \"mappings\" attribute. They\n// are lazily instantiated, accessed via the `_generatedMappings` and\n// `_originalMappings` getters respectively, and we only parse the mappings\n// and create these arrays once queried for a source location. We jump through\n// these hoops because there can be many thousands of mappings, and parsing\n// them is expensive, so we only want to do it if we must.\n//\n// Each object in the arrays is of the form:\n//\n//     {\n//       generatedLine: The line number in the generated code,\n//       generatedColumn: The column number in the generated code,\n//       source: The path to the original source file that generated this\n//               chunk of code,\n//       originalLine: The line number in the original source that\n//                     corresponds to this chunk of generated code,\n//       originalColumn: The column number in the original source that\n//                       corresponds to this chunk of generated code,\n//       name: The name of the original symbol which generated this chunk of\n//             code.\n//     }\n//\n// All properties except for `generatedLine` and `generatedColumn` can be\n// `null`.\n//\n// `_generatedMappings` is ordered by the generated positions.\n//\n// `_originalMappings` is ordered by the original positions.\n\nSourceMapConsumer.prototype.__generatedMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_generatedMappings', {\n  get: function () {\n    if (!this.__generatedMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__generatedMappings;\n  }\n});\n\nSourceMapConsumer.prototype.__originalMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_originalMappings', {\n  get: function () {\n    if (!this.__originalMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__originalMappings;\n  }\n});\n\nSourceMapConsumer.prototype._charIsMappingSeparator =\n  function SourceMapConsumer_charIsMappingSeparator(aStr, index) {\n    var c = aStr.charAt(index);\n    return c === \";\" || c === \",\";\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    throw new Error(\"Subclasses must implement _parseMappings\");\n  };\n\nSourceMapConsumer.GENERATED_ORDER = 1;\nSourceMapConsumer.ORIGINAL_ORDER = 2;\n\nSourceMapConsumer.GREATEST_LOWER_BOUND = 1;\nSourceMapConsumer.LEAST_UPPER_BOUND = 2;\n\n/**\n * Iterate over each mapping between an original source/line/column and a\n * generated line/column in this source map.\n *\n * @param Function aCallback\n *        The function that is called with each mapping.\n * @param Object aContext\n *        Optional. If specified, this object will be the value of `this` every\n *        time that `aCallback` is called.\n * @param aOrder\n *        Either `SourceMapConsumer.GENERATED_ORDER` or\n *        `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to\n *        iterate over the mappings sorted by the generated file's line/column\n *        order or the original's source/line/column order, respectively. Defaults to\n *        `SourceMapConsumer.GENERATED_ORDER`.\n */\nSourceMapConsumer.prototype.eachMapping =\n  function SourceMapConsumer_eachMapping(aCallback, aContext, aOrder) {\n    var context = aContext || null;\n    var order = aOrder || SourceMapConsumer.GENERATED_ORDER;\n\n    var mappings;\n    switch (order) {\n    case SourceMapConsumer.GENERATED_ORDER:\n      mappings = this._generatedMappings;\n      break;\n    case SourceMapConsumer.ORIGINAL_ORDER:\n      mappings = this._originalMappings;\n      break;\n    default:\n      throw new Error(\"Unknown order of iteration.\");\n    }\n\n    var sourceRoot = this.sourceRoot;\n    mappings.map(function (mapping) {\n      var source = mapping.source === null ? null : this._sources.at(mapping.source);\n      if (source != null && sourceRoot != null) {\n        source = util.join(sourceRoot, source);\n      }\n      return {\n        source: source,\n        generatedLine: mapping.generatedLine,\n        generatedColumn: mapping.generatedColumn,\n        originalLine: mapping.originalLine,\n        originalColumn: mapping.originalColumn,\n        name: mapping.name === null ? null : this._names.at(mapping.name)\n      };\n    }, this).forEach(aCallback, context);\n  };\n\n/**\n * Returns all generated line and column information for the original source,\n * line, and column provided. If no column is provided, returns all mappings\n * corresponding to a either the line we are searching for or the next\n * closest line that has any mappings. Otherwise, returns all mappings\n * corresponding to the given line and either the column we are searching for\n * or the next closest column that has any offsets.\n *\n * The only argument is an object with the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: Optional. the column number in the original source.\n *\n * and an array of objects is returned, each with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nSourceMapConsumer.prototype.allGeneratedPositionsFor =\n  function SourceMapConsumer_allGeneratedPositionsFor(aArgs) {\n    var line = util.getArg(aArgs, 'line');\n\n    // When there is no exact match, BasicSourceMapConsumer.prototype._findMapping\n    // returns the index of the closest mapping less than the needle. By\n    // setting needle.originalColumn to 0, we thus find the last mapping for\n    // the given line, provided such a mapping exists.\n    var needle = {\n      source: util.getArg(aArgs, 'source'),\n      originalLine: line,\n      originalColumn: util.getArg(aArgs, 'column', 0)\n    };\n\n    if (this.sourceRoot != null) {\n      needle.source = util.relative(this.sourceRoot, needle.source);\n    }\n    if (!this._sources.has(needle.source)) {\n      return [];\n    }\n    needle.source = this._sources.indexOf(needle.source);\n\n    var mappings = [];\n\n    var index = this._findMapping(needle,\n                                  this._originalMappings,\n                                  \"originalLine\",\n                                  \"originalColumn\",\n                                  util.compareByOriginalPositions,\n                                  binarySearch.LEAST_UPPER_BOUND);\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (aArgs.column === undefined) {\n        var originalLine = mapping.originalLine;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we found. Since\n        // mappings are sorted, this is guaranteed to find all mappings for\n        // the line we found.\n        while (mapping && mapping.originalLine === originalLine) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      } else {\n        var originalColumn = mapping.originalColumn;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we were searching for.\n        // Since mappings are sorted, this is guaranteed to find all mappings for\n        // the line we are searching for.\n        while (mapping &&\n               mapping.originalLine === line &&\n               mapping.originalColumn == originalColumn) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      }\n    }\n\n    return mappings;\n  };\n\nexports.SourceMapConsumer = SourceMapConsumer;\n\n/**\n * A BasicSourceMapConsumer instance represents a parsed source map which we can\n * query for information about the original file positions by giving it a file\n * position in the generated source.\n *\n * The only parameter is the raw source map (either as a JSON string, or\n * already parsed to an object). According to the spec, source maps have the\n * following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - sources: An array of URLs to the original source files.\n *   - names: An array of identifiers which can be referrenced by individual mappings.\n *   - sourceRoot: Optional. The URL root from which all sources are relative.\n *   - sourcesContent: Optional. An array of contents of the original source files.\n *   - mappings: A string of base64 VLQs which contain the actual mappings.\n *   - file: Optional. The generated file this source map is associated with.\n *\n * Here is an example source map, taken from the source map spec[0]:\n *\n *     {\n *       version : 3,\n *       file: \"out.js\",\n *       sourceRoot : \"\",\n *       sources: [\"foo.js\", \"bar.js\"],\n *       names: [\"src\", \"maps\", \"are\", \"fun\"],\n *       mappings: \"AA,AB;;ABCDE;\"\n *     }\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit?pli=1#\n */\nfunction BasicSourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sources = util.getArg(sourceMap, 'sources');\n  // Sass 3.3 leaves out the 'names' array, so we deviate from the spec (which\n  // requires the array) to play nice here.\n  var names = util.getArg(sourceMap, 'names', []);\n  var sourceRoot = util.getArg(sourceMap, 'sourceRoot', null);\n  var sourcesContent = util.getArg(sourceMap, 'sourcesContent', null);\n  var mappings = util.getArg(sourceMap, 'mappings');\n  var file = util.getArg(sourceMap, 'file', null);\n\n  // Once again, Sass deviates from the spec and supplies the version as a\n  // string rather than a number, so we use loose equality checking here.\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  sources = sources\n    .map(String)\n    // Some source maps produce relative source paths like \"./foo.js\" instead of\n    // \"foo.js\".  Normalize these first so that future comparisons will succeed.\n    // See bugzil.la/1090768.\n    .map(util.normalize)\n    // Always ensure that absolute sources are internally stored relative to\n    // the source root, if the source root is absolute. Not doing this would\n    // be particularly problematic when the source root is a prefix of the\n    // source (valid, but why??). See github issue #199 and bugzil.la/1188982.\n    .map(function (source) {\n      return sourceRoot && util.isAbsolute(sourceRoot) && util.isAbsolute(source)\n        ? util.relative(sourceRoot, source)\n        : source;\n    });\n\n  // Pass `true` below to allow duplicate names and sources. While source maps\n  // are intended to be compressed and deduplicated, the TypeScript compiler\n  // sometimes generates source maps with duplicates in them. See Github issue\n  // #72 and bugzil.la/889492.\n  this._names = ArraySet.fromArray(names.map(String), true);\n  this._sources = ArraySet.fromArray(sources, true);\n\n  this.sourceRoot = sourceRoot;\n  this.sourcesContent = sourcesContent;\n  this._mappings = mappings;\n  this.file = file;\n}\n\nBasicSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nBasicSourceMapConsumer.prototype.consumer = SourceMapConsumer;\n\n/**\n * Create a BasicSourceMapConsumer from a SourceMapGenerator.\n *\n * @param SourceMapGenerator aSourceMap\n *        The source map that will be consumed.\n * @returns BasicSourceMapConsumer\n */\nBasicSourceMapConsumer.fromSourceMap =\n  function SourceMapConsumer_fromSourceMap(aSourceMap) {\n    var smc = Object.create(BasicSourceMapConsumer.prototype);\n\n    var names = smc._names = ArraySet.fromArray(aSourceMap._names.toArray(), true);\n    var sources = smc._sources = ArraySet.fromArray(aSourceMap._sources.toArray(), true);\n    smc.sourceRoot = aSourceMap._sourceRoot;\n    smc.sourcesContent = aSourceMap._generateSourcesContent(smc._sources.toArray(),\n                                                            smc.sourceRoot);\n    smc.file = aSourceMap._file;\n\n    // Because we are modifying the entries (by converting string sources and\n    // names to indices into the sources and names ArraySets), we have to make\n    // a copy of the entry or else bad things happen. Shared mutable state\n    // strikes again! See github issue #191.\n\n    var generatedMappings = aSourceMap._mappings.toArray().slice();\n    var destGeneratedMappings = smc.__generatedMappings = [];\n    var destOriginalMappings = smc.__originalMappings = [];\n\n    for (var i = 0, length = generatedMappings.length; i < length; i++) {\n      var srcMapping = generatedMappings[i];\n      var destMapping = new Mapping;\n      destMapping.generatedLine = srcMapping.generatedLine;\n      destMapping.generatedColumn = srcMapping.generatedColumn;\n\n      if (srcMapping.source) {\n        destMapping.source = sources.indexOf(srcMapping.source);\n        destMapping.originalLine = srcMapping.originalLine;\n        destMapping.originalColumn = srcMapping.originalColumn;\n\n        if (srcMapping.name) {\n          destMapping.name = names.indexOf(srcMapping.name);\n        }\n\n        destOriginalMappings.push(destMapping);\n      }\n\n      destGeneratedMappings.push(destMapping);\n    }\n\n    quickSort(smc.__originalMappings, util.compareByOriginalPositions);\n\n    return smc;\n  };\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nBasicSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(BasicSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    return this._sources.toArray().map(function (s) {\n      return this.sourceRoot != null ? util.join(this.sourceRoot, s) : s;\n    }, this);\n  }\n});\n\n/**\n * Provide the JIT with a nice shape / hidden class.\n */\nfunction Mapping() {\n  this.generatedLine = 0;\n  this.generatedColumn = 0;\n  this.source = null;\n  this.originalLine = null;\n  this.originalColumn = null;\n  this.name = null;\n}\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nBasicSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    var generatedLine = 1;\n    var previousGeneratedColumn = 0;\n    var previousOriginalLine = 0;\n    var previousOriginalColumn = 0;\n    var previousSource = 0;\n    var previousName = 0;\n    var length = aStr.length;\n    var index = 0;\n    var cachedSegments = {};\n    var temp = {};\n    var originalMappings = [];\n    var generatedMappings = [];\n    var mapping, str, segment, end, value;\n\n    while (index < length) {\n      if (aStr.charAt(index) === ';') {\n        generatedLine++;\n        index++;\n        previousGeneratedColumn = 0;\n      }\n      else if (aStr.charAt(index) === ',') {\n        index++;\n      }\n      else {\n        mapping = new Mapping();\n        mapping.generatedLine = generatedLine;\n\n        // Because each offset is encoded relative to the previous one,\n        // many segments often have the same encoding. We can exploit this\n        // fact by caching the parsed variable length fields of each segment,\n        // allowing us to avoid a second parse if we encounter the same\n        // segment again.\n        for (end = index; end < length; end++) {\n          if (this._charIsMappingSeparator(aStr, end)) {\n            break;\n          }\n        }\n        str = aStr.slice(index, end);\n\n        segment = cachedSegments[str];\n        if (segment) {\n          index += str.length;\n        } else {\n          segment = [];\n          while (index < end) {\n            base64VLQ.decode(aStr, index, temp);\n            value = temp.value;\n            index = temp.rest;\n            segment.push(value);\n          }\n\n          if (segment.length === 2) {\n            throw new Error('Found a source, but no line and column');\n          }\n\n          if (segment.length === 3) {\n            throw new Error('Found a source and line, but no column');\n          }\n\n          cachedSegments[str] = segment;\n        }\n\n        // Generated column.\n        mapping.generatedColumn = previousGeneratedColumn + segment[0];\n        previousGeneratedColumn = mapping.generatedColumn;\n\n        if (segment.length > 1) {\n          // Original source.\n          mapping.source = previousSource + segment[1];\n          previousSource += segment[1];\n\n          // Original line.\n          mapping.originalLine = previousOriginalLine + segment[2];\n          previousOriginalLine = mapping.originalLine;\n          // Lines are stored 0-based\n          mapping.originalLine += 1;\n\n          // Original column.\n          mapping.originalColumn = previousOriginalColumn + segment[3];\n          previousOriginalColumn = mapping.originalColumn;\n\n          if (segment.length > 4) {\n            // Original name.\n            mapping.name = previousName + segment[4];\n            previousName += segment[4];\n          }\n        }\n\n        generatedMappings.push(mapping);\n        if (typeof mapping.originalLine === 'number') {\n          originalMappings.push(mapping);\n        }\n      }\n    }\n\n    quickSort(generatedMappings, util.compareByGeneratedPositionsDeflated);\n    this.__generatedMappings = generatedMappings;\n\n    quickSort(originalMappings, util.compareByOriginalPositions);\n    this.__originalMappings = originalMappings;\n  };\n\n/**\n * Find the mapping that best matches the hypothetical \"needle\" mapping that\n * we are searching for in the given \"haystack\" of mappings.\n */\nBasicSourceMapConsumer.prototype._findMapping =\n  function SourceMapConsumer_findMapping(aNeedle, aMappings, aLineName,\n                                         aColumnName, aComparator, aBias) {\n    // To return the position we are searching for, we must first find the\n    // mapping for the given position and then return the opposite position it\n    // points to. Because the mappings are sorted, we can use binary search to\n    // find the best mapping.\n\n    if (aNeedle[aLineName] <= 0) {\n      throw new TypeError('Line must be greater than or equal to 1, got '\n                          + aNeedle[aLineName]);\n    }\n    if (aNeedle[aColumnName] < 0) {\n      throw new TypeError('Column must be greater than or equal to 0, got '\n                          + aNeedle[aColumnName]);\n    }\n\n    return binarySearch.search(aNeedle, aMappings, aComparator, aBias);\n  };\n\n/**\n * Compute the last column for each generated mapping. The last column is\n * inclusive.\n */\nBasicSourceMapConsumer.prototype.computeColumnSpans =\n  function SourceMapConsumer_computeColumnSpans() {\n    for (var index = 0; index < this._generatedMappings.length; ++index) {\n      var mapping = this._generatedMappings[index];\n\n      // Mappings do not contain a field for the last generated columnt. We\n      // can come up with an optimistic estimate, however, by assuming that\n      // mappings are contiguous (i.e. given two consecutive mappings, the\n      // first mapping ends where the second one starts).\n      if (index + 1 < this._generatedMappings.length) {\n        var nextMapping = this._generatedMappings[index + 1];\n\n        if (mapping.generatedLine === nextMapping.generatedLine) {\n          mapping.lastGeneratedColumn = nextMapping.generatedColumn - 1;\n          continue;\n        }\n      }\n\n      // The last mapping for each line spans the entire line.\n      mapping.lastGeneratedColumn = Infinity;\n    }\n  };\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.\n *   - column: The column number in the generated source.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.\n *   - column: The column number in the original source, or null.\n *   - name: The original identifier, or null.\n */\nBasicSourceMapConsumer.prototype.originalPositionFor =\n  function SourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._generatedMappings,\n      \"generatedLine\",\n      \"generatedColumn\",\n      util.compareByGeneratedPositionsDeflated,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._generatedMappings[index];\n\n      if (mapping.generatedLine === needle.generatedLine) {\n        var source = util.getArg(mapping, 'source', null);\n        if (source !== null) {\n          source = this._sources.at(source);\n          if (this.sourceRoot != null) {\n            source = util.join(this.sourceRoot, source);\n          }\n        }\n        var name = util.getArg(mapping, 'name', null);\n        if (name !== null) {\n          name = this._names.at(name);\n        }\n        return {\n          source: source,\n          line: util.getArg(mapping, 'originalLine', null),\n          column: util.getArg(mapping, 'originalColumn', null),\n          name: name\n        };\n      }\n    }\n\n    return {\n      source: null,\n      line: null,\n      column: null,\n      name: null\n    };\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nBasicSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function BasicSourceMapConsumer_hasContentsOfAllSources() {\n    if (!this.sourcesContent) {\n      return false;\n    }\n    return this.sourcesContent.length >= this._sources.size() &&\n      !this.sourcesContent.some(function (sc) { return sc == null; });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nBasicSourceMapConsumer.prototype.sourceContentFor =\n  function SourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    if (!this.sourcesContent) {\n      return null;\n    }\n\n    if (this.sourceRoot != null) {\n      aSource = util.relative(this.sourceRoot, aSource);\n    }\n\n    if (this._sources.has(aSource)) {\n      return this.sourcesContent[this._sources.indexOf(aSource)];\n    }\n\n    var url;\n    if (this.sourceRoot != null\n        && (url = util.urlParse(this.sourceRoot))) {\n      // XXX: file:// URIs and absolute paths lead to unexpected behavior for\n      // many users. We can help them out when they expect file:// URIs to\n      // behave like it would if they were running a local HTTP server. See\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=885597.\n      var fileUriAbsPath = aSource.replace(/^file:\\/\\//, \"\");\n      if (url.scheme == \"file\"\n          && this._sources.has(fileUriAbsPath)) {\n        return this.sourcesContent[this._sources.indexOf(fileUriAbsPath)]\n      }\n\n      if ((!url.path || url.path == \"/\")\n          && this._sources.has(\"/\" + aSource)) {\n        return this.sourcesContent[this._sources.indexOf(\"/\" + aSource)];\n      }\n    }\n\n    // This function is used recursively from\n    // IndexedSourceMapConsumer.prototype.sourceContentFor. In that case, we\n    // don't want to throw if we can't find the source - we just want to\n    // return null, so we provide a flag to exit gracefully.\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: The column number in the original source.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nBasicSourceMapConsumer.prototype.generatedPositionFor =\n  function SourceMapConsumer_generatedPositionFor(aArgs) {\n    var source = util.getArg(aArgs, 'source');\n    if (this.sourceRoot != null) {\n      source = util.relative(this.sourceRoot, source);\n    }\n    if (!this._sources.has(source)) {\n      return {\n        line: null,\n        column: null,\n        lastColumn: null\n      };\n    }\n    source = this._sources.indexOf(source);\n\n    var needle = {\n      source: source,\n      originalLine: util.getArg(aArgs, 'line'),\n      originalColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._originalMappings,\n      \"originalLine\",\n      \"originalColumn\",\n      util.compareByOriginalPositions,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (mapping.source === needle.source) {\n        return {\n          line: util.getArg(mapping, 'generatedLine', null),\n          column: util.getArg(mapping, 'generatedColumn', null),\n          lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n        };\n      }\n    }\n\n    return {\n      line: null,\n      column: null,\n      lastColumn: null\n    };\n  };\n\nexports.BasicSourceMapConsumer = BasicSourceMapConsumer;\n\n/**\n * An IndexedSourceMapConsumer instance represents a parsed source map which\n * we can query for information. It differs from BasicSourceMapConsumer in\n * that it takes \"indexed\" source maps (i.e. ones with a \"sections\" field) as\n * input.\n *\n * The only parameter is a raw source map (either as a JSON string, or already\n * parsed to an object). According to the spec for indexed source maps, they\n * have the following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - file: Optional. The generated file this source map is associated with.\n *   - sections: A list of section definitions.\n *\n * Each value under the \"sections\" field has two fields:\n *   - offset: The offset into the original specified at which this section\n *       begins to apply, defined as an object with a \"line\" and \"column\"\n *       field.\n *   - map: A source map definition. This source map could also be indexed,\n *       but doesn't have to be.\n *\n * Instead of the \"map\" field, it's also possible to have a \"url\" field\n * specifying a URL to retrieve a source map from, but that's currently\n * unsupported.\n *\n * Here's an example source map, taken from the source map spec[0], but\n * modified to omit a section which uses the \"url\" field.\n *\n *  {\n *    version : 3,\n *    file: \"app.js\",\n *    sections: [{\n *      offset: {line:100, column:10},\n *      map: {\n *        version : 3,\n *        file: \"section.js\",\n *        sources: [\"foo.js\", \"bar.js\"],\n *        names: [\"src\", \"maps\", \"are\", \"fun\"],\n *        mappings: \"AAAA,E;;ABCDE;\"\n *      }\n *    }],\n *  }\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit#heading=h.535es3xeprgt\n */\nfunction IndexedSourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sections = util.getArg(sourceMap, 'sections');\n\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n\n  var lastOffset = {\n    line: -1,\n    column: 0\n  };\n  this._sections = sections.map(function (s) {\n    if (s.url) {\n      // The url field will require support for asynchronicity.\n      // See https://github.com/mozilla/source-map/issues/16\n      throw new Error('Support for url field in sections not implemented.');\n    }\n    var offset = util.getArg(s, 'offset');\n    var offsetLine = util.getArg(offset, 'line');\n    var offsetColumn = util.getArg(offset, 'column');\n\n    if (offsetLine < lastOffset.line ||\n        (offsetLine === lastOffset.line && offsetColumn < lastOffset.column)) {\n      throw new Error('Section offsets must be ordered and non-overlapping.');\n    }\n    lastOffset = offset;\n\n    return {\n      generatedOffset: {\n        // The offset fields are 0-based, but we use 1-based indices when\n        // encoding/decoding from VLQ.\n        generatedLine: offsetLine + 1,\n        generatedColumn: offsetColumn + 1\n      },\n      consumer: new SourceMapConsumer(util.getArg(s, 'map'))\n    }\n  });\n}\n\nIndexedSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nIndexedSourceMapConsumer.prototype.constructor = SourceMapConsumer;\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nIndexedSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(IndexedSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    var sources = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      for (var j = 0; j < this._sections[i].consumer.sources.length; j++) {\n        sources.push(this._sections[i].consumer.sources[j]);\n      }\n    }\n    return sources;\n  }\n});\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.\n *   - column: The column number in the generated source.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.\n *   - column: The column number in the original source, or null.\n *   - name: The original identifier, or null.\n */\nIndexedSourceMapConsumer.prototype.originalPositionFor =\n  function IndexedSourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    // Find the section containing the generated position we're trying to map\n    // to an original position.\n    var sectionIndex = binarySearch.search(needle, this._sections,\n      function(needle, section) {\n        var cmp = needle.generatedLine - section.generatedOffset.generatedLine;\n        if (cmp) {\n          return cmp;\n        }\n\n        return (needle.generatedColumn -\n                section.generatedOffset.generatedColumn);\n      });\n    var section = this._sections[sectionIndex];\n\n    if (!section) {\n      return {\n        source: null,\n        line: null,\n        column: null,\n        name: null\n      };\n    }\n\n    return section.consumer.originalPositionFor({\n      line: needle.generatedLine -\n        (section.generatedOffset.generatedLine - 1),\n      column: needle.generatedColumn -\n        (section.generatedOffset.generatedLine === needle.generatedLine\n         ? section.generatedOffset.generatedColumn - 1\n         : 0),\n      bias: aArgs.bias\n    });\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nIndexedSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function IndexedSourceMapConsumer_hasContentsOfAllSources() {\n    return this._sections.every(function (s) {\n      return s.consumer.hasContentsOfAllSources();\n    });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nIndexedSourceMapConsumer.prototype.sourceContentFor =\n  function IndexedSourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      var content = section.consumer.sourceContentFor(aSource, true);\n      if (content) {\n        return content;\n      }\n    }\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: The column number in the original source.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nIndexedSourceMapConsumer.prototype.generatedPositionFor =\n  function IndexedSourceMapConsumer_generatedPositionFor(aArgs) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      // Only consider this section if the requested source is in the list of\n      // sources of the consumer.\n      if (section.consumer.sources.indexOf(util.getArg(aArgs, 'source')) === -1) {\n        continue;\n      }\n      var generatedPosition = section.consumer.generatedPositionFor(aArgs);\n      if (generatedPosition) {\n        var ret = {\n          line: generatedPosition.line +\n            (section.generatedOffset.generatedLine - 1),\n          column: generatedPosition.column +\n            (section.generatedOffset.generatedLine === generatedPosition.line\n             ? section.generatedOffset.generatedColumn - 1\n             : 0)\n        };\n        return ret;\n      }\n    }\n\n    return {\n      line: null,\n      column: null\n    };\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nIndexedSourceMapConsumer.prototype._parseMappings =\n  function IndexedSourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    this.__generatedMappings = [];\n    this.__originalMappings = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n      var sectionMappings = section.consumer._generatedMappings;\n      for (var j = 0; j < sectionMappings.length; j++) {\n        var mapping = sectionMappings[j];\n\n        var source = section.consumer._sources.at(mapping.source);\n        if (section.consumer.sourceRoot !== null) {\n          source = util.join(section.consumer.sourceRoot, source);\n        }\n        this._sources.add(source);\n        source = this._sources.indexOf(source);\n\n        var name = section.consumer._names.at(mapping.name);\n        this._names.add(name);\n        name = this._names.indexOf(name);\n\n        // The mappings coming from the consumer for the section have\n        // generated positions relative to the start of the section, so we\n        // need to offset them to be relative to the start of the concatenated\n        // generated file.\n        var adjustedMapping = {\n          source: source,\n          generatedLine: mapping.generatedLine +\n            (section.generatedOffset.generatedLine - 1),\n          generatedColumn: mapping.generatedColumn +\n            (section.generatedOffset.generatedLine === mapping.generatedLine\n            ? section.generatedOffset.generatedColumn - 1\n            : 0),\n          originalLine: mapping.originalLine,\n          originalColumn: mapping.originalColumn,\n          name: name\n        };\n\n        this.__generatedMappings.push(adjustedMapping);\n        if (typeof adjustedMapping.originalLine === 'number') {\n          this.__originalMappings.push(adjustedMapping);\n        }\n      }\n    }\n\n    quickSort(this.__generatedMappings, util.compareByGeneratedPositionsDeflated);\n    quickSort(this.__originalMappings, util.compareByOriginalPositions);\n  };\n\nexports.IndexedSourceMapConsumer = IndexedSourceMapConsumer;\n\n},{\"./array-set\":6,\"./base64-vlq\":7,\"./binary-search\":9,\"./quick-sort\":10,\"./util\":12}],12:[function(require,module,exports){\n/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n/**\n * This is a helper function for getting values from parameter/options\n * objects.\n *\n * @param args The object we are extracting values from\n * @param name The name of the property we are getting.\n * @param defaultValue An optional value to return if the property is missing\n * from the object. If this is not specified and the property is missing, an\n * error will be thrown.\n */\nfunction getArg(aArgs, aName, aDefaultValue) {\n  if (aName in aArgs) {\n    return aArgs[aName];\n  } else if (arguments.length === 3) {\n    return aDefaultValue;\n  } else {\n    throw new Error('\"' + aName + '\" is a required argument.');\n  }\n}\nexports.getArg = getArg;\n\nvar urlRegexp = /^(?:([\\w+\\-.]+):)?\\/\\/(?:(\\w+:\\w+)@)?([\\w.]*)(?::(\\d+))?(\\S*)$/;\nvar dataUrlRegexp = /^data:.+\\,.+$/;\n\nfunction urlParse(aUrl) {\n  var match = aUrl.match(urlRegexp);\n  if (!match) {\n    return null;\n  }\n  return {\n    scheme: match[1],\n    auth: match[2],\n    host: match[3],\n    port: match[4],\n    path: match[5]\n  };\n}\nexports.urlParse = urlParse;\n\nfunction urlGenerate(aParsedUrl) {\n  var url = '';\n  if (aParsedUrl.scheme) {\n    url += aParsedUrl.scheme + ':';\n  }\n  url += '//';\n  if (aParsedUrl.auth) {\n    url += aParsedUrl.auth + '@';\n  }\n  if (aParsedUrl.host) {\n    url += aParsedUrl.host;\n  }\n  if (aParsedUrl.port) {\n    url += \":\" + aParsedUrl.port\n  }\n  if (aParsedUrl.path) {\n    url += aParsedUrl.path;\n  }\n  return url;\n}\nexports.urlGenerate = urlGenerate;\n\n/**\n * Normalizes a path, or the path portion of a URL:\n *\n * - Replaces consecutive slashes with one slash.\n * - Removes unnecessary '.' parts.\n * - Removes unnecessary '<dir>/..' parts.\n *\n * Based on code in the Node.js 'path' core module.\n *\n * @param aPath The path or url to normalize.\n */\nfunction normalize(aPath) {\n  var path = aPath;\n  var url = urlParse(aPath);\n  if (url) {\n    if (!url.path) {\n      return aPath;\n    }\n    path = url.path;\n  }\n  var isAbsolute = exports.isAbsolute(path);\n\n  var parts = path.split(/\\/+/);\n  for (var part, up = 0, i = parts.length - 1; i >= 0; i--) {\n    part = parts[i];\n    if (part === '.') {\n      parts.splice(i, 1);\n    } else if (part === '..') {\n      up++;\n    } else if (up > 0) {\n      if (part === '') {\n        // The first part is blank if the path is absolute. Trying to go\n        // above the root is a no-op. Therefore we can remove all '..' parts\n        // directly after the root.\n        parts.splice(i + 1, up);\n        up = 0;\n      } else {\n        parts.splice(i, 2);\n        up--;\n      }\n    }\n  }\n  path = parts.join('/');\n\n  if (path === '') {\n    path = isAbsolute ? '/' : '.';\n  }\n\n  if (url) {\n    url.path = path;\n    return urlGenerate(url);\n  }\n  return path;\n}\nexports.normalize = normalize;\n\n/**\n * Joins two paths/URLs.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be joined with the root.\n *\n * - If aPath is a URL or a data URI, aPath is returned, unless aPath is a\n *   scheme-relative URL: Then the scheme of aRoot, if any, is prepended\n *   first.\n * - Otherwise aPath is a path. If aRoot is a URL, then its path portion\n *   is updated with the result and aRoot is returned. Otherwise the result\n *   is returned.\n *   - If aPath is absolute, the result is aPath.\n *   - Otherwise the two paths are joined with a slash.\n * - Joining for example 'http://' and 'www.example.com' is also supported.\n */\nfunction join(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n  if (aPath === \"\") {\n    aPath = \".\";\n  }\n  var aPathUrl = urlParse(aPath);\n  var aRootUrl = urlParse(aRoot);\n  if (aRootUrl) {\n    aRoot = aRootUrl.path || '/';\n  }\n\n  // `join(foo, '//www.example.org')`\n  if (aPathUrl && !aPathUrl.scheme) {\n    if (aRootUrl) {\n      aPathUrl.scheme = aRootUrl.scheme;\n    }\n    return urlGenerate(aPathUrl);\n  }\n\n  if (aPathUrl || aPath.match(dataUrlRegexp)) {\n    return aPath;\n  }\n\n  // `join('http://', 'www.example.com')`\n  if (aRootUrl && !aRootUrl.host && !aRootUrl.path) {\n    aRootUrl.host = aPath;\n    return urlGenerate(aRootUrl);\n  }\n\n  var joined = aPath.charAt(0) === '/'\n    ? aPath\n    : normalize(aRoot.replace(/\\/+$/, '') + '/' + aPath);\n\n  if (aRootUrl) {\n    aRootUrl.path = joined;\n    return urlGenerate(aRootUrl);\n  }\n  return joined;\n}\nexports.join = join;\n\nexports.isAbsolute = function (aPath) {\n  return aPath.charAt(0) === '/' || !!aPath.match(urlRegexp);\n};\n\n/**\n * Make a path relative to a URL or another path.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be made relative to aRoot.\n */\nfunction relative(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n\n  aRoot = aRoot.replace(/\\/$/, '');\n\n  // It is possible for the path to be above the root. In this case, simply\n  // checking whether the root is a prefix of the path won't work. Instead, we\n  // need to remove components from the root one by one, until either we find\n  // a prefix that fits, or we run out of components to remove.\n  var level = 0;\n  while (aPath.indexOf(aRoot + '/') !== 0) {\n    var index = aRoot.lastIndexOf(\"/\");\n    if (index < 0) {\n      return aPath;\n    }\n\n    // If the only part of the root that is left is the scheme (i.e. http://,\n    // file:///, etc.), one or more slashes (/), or simply nothing at all, we\n    // have exhausted all components, so the path is not relative to the root.\n    aRoot = aRoot.slice(0, index);\n    if (aRoot.match(/^([^\\/]+:\\/)?\\/*$/)) {\n      return aPath;\n    }\n\n    ++level;\n  }\n\n  // Make sure we add a \"../\" for each component we removed from the root.\n  return Array(level + 1).join(\"../\") + aPath.substr(aRoot.length + 1);\n}\nexports.relative = relative;\n\nvar supportsNullProto = (function () {\n  var obj = Object.create(null);\n  return !('__proto__' in obj);\n}());\n\nfunction identity (s) {\n  return s;\n}\n\n/**\n * Because behavior goes wacky when you set `__proto__` on objects, we\n * have to prefix all the strings in our set with an arbitrary character.\n *\n * See https://github.com/mozilla/source-map/pull/31 and\n * https://github.com/mozilla/source-map/issues/30\n *\n * @param String aStr\n */\nfunction toSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return '$' + aStr;\n  }\n\n  return aStr;\n}\nexports.toSetString = supportsNullProto ? identity : toSetString;\n\nfunction fromSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return aStr.slice(1);\n  }\n\n  return aStr;\n}\nexports.fromSetString = supportsNullProto ? identity : fromSetString;\n\nfunction isProtoString(s) {\n  if (!s) {\n    return false;\n  }\n\n  var length = s.length;\n\n  if (length < 9 /* \"__proto__\".length */) {\n    return false;\n  }\n\n  if (s.charCodeAt(length - 1) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 2) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 3) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 4) !== 116 /* 't' */ ||\n      s.charCodeAt(length - 5) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 6) !== 114 /* 'r' */ ||\n      s.charCodeAt(length - 7) !== 112 /* 'p' */ ||\n      s.charCodeAt(length - 8) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 9) !== 95  /* '_' */) {\n    return false;\n  }\n\n  for (var i = length - 10; i >= 0; i--) {\n    if (s.charCodeAt(i) !== 36 /* '$' */) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Comparator between two mappings where the original positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same original source/line/column, but different generated\n * line and column the same. Useful when searching for a mapping with a\n * stubbed out mapping.\n */\nfunction compareByOriginalPositions(mappingA, mappingB, onlyCompareOriginal) {\n  var cmp = mappingA.source - mappingB.source;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0 || onlyCompareOriginal) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return mappingA.name - mappingB.name;\n}\nexports.compareByOriginalPositions = compareByOriginalPositions;\n\n/**\n * Comparator between two mappings with deflated source and name indices where\n * the generated positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same generated line and column, but different\n * source/name/original line and column the same. Useful when searching for a\n * mapping with a stubbed out mapping.\n */\nfunction compareByGeneratedPositionsDeflated(mappingA, mappingB, onlyCompareGenerated) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0 || onlyCompareGenerated) {\n    return cmp;\n  }\n\n  cmp = mappingA.source - mappingB.source;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return mappingA.name - mappingB.name;\n}\nexports.compareByGeneratedPositionsDeflated = compareByGeneratedPositionsDeflated;\n\nfunction strcmp(aStr1, aStr2) {\n  if (aStr1 === aStr2) {\n    return 0;\n  }\n\n  if (aStr1 > aStr2) {\n    return 1;\n  }\n\n  return -1;\n}\n\n/**\n * Comparator between two mappings with inflated source and name strings where\n * the generated positions are compared.\n */\nfunction compareByGeneratedPositionsInflated(mappingA, mappingB) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsInflated = compareByGeneratedPositionsInflated;\n\n},{}],13:[function(require,module,exports){\narguments[4][2][0].apply(exports,arguments)\n},{\"dup\":2}],14:[function(require,module,exports){\n(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stack-generator', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.StackGenerator = factory(root.StackFrame);\n    }\n}(this, function(StackFrame) {\n    return {\n        backtrace: function StackGenerator$$backtrace(opts) {\n            var stack = [];\n            var maxStackSize = 10;\n\n            if (typeof opts === 'object' && typeof opts.maxStackSize === 'number') {\n                maxStackSize = opts.maxStackSize;\n            }\n\n            var curr = arguments.callee;\n            while (curr && stack.length < maxStackSize && curr['arguments']) {\n                // Allow V8 optimizations\n                var args = new Array(curr['arguments'].length);\n                for (var i = 0; i < args.length; ++i) {\n                    args[i] = curr['arguments'][i];\n                }\n                if (/function(?:\\s+([\\w$]+))+\\s*\\(/.test(curr.toString())) {\n                    stack.push(new StackFrame({functionName: RegExp.$1 || undefined, args: args}));\n                } else {\n                    stack.push(new StackFrame({args: args}));\n                }\n\n                try {\n                    curr = curr.caller;\n                } catch (e) {\n                    break;\n                }\n            }\n            return stack;\n        }\n    };\n}));\n\n},{\"stackframe\":13}],15:[function(require,module,exports){\narguments[4][2][0].apply(exports,arguments)\n},{\"dup\":2}],16:[function(require,module,exports){\n(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stacktrace-gps', ['source-map', 'stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('source-map/lib/source-map-consumer'), require('stackframe'));\n    } else {\n        root.StackTraceGPS = factory(root.SourceMap || root.sourceMap, root.StackFrame);\n    }\n}(this, function(SourceMap, StackFrame) {\n    'use strict';\n\n    /**\n     * Make a X-Domain request to url and callback.\n     *\n     * @param {String} url\n     * @returns {Promise} with response text if fulfilled\n     */\n    function _xdr(url) {\n        return new Promise(function(resolve, reject) {\n            var req = new XMLHttpRequest();\n            req.open('get', url);\n            req.onerror = reject;\n            req.onreadystatechange = function onreadystatechange() {\n                if (req.readyState === 4) {\n                    if ((req.status >= 200 && req.status < 300) ||\n                        (url.substr(0, 7) === 'file://' && req.responseText)) {\n                        resolve(req.responseText);\n                    } else {\n                        reject(new Error('HTTP status: ' + req.status + ' retrieving ' + url));\n                    }\n                }\n            };\n            req.send();\n        });\n\n    }\n\n    /**\n     * Convert a Base64-encoded string into its original representation.\n     * Used for inline sourcemaps.\n     *\n     * @param {String} b64str Base-64 encoded string\n     * @returns {String} original representation of the base64-encoded string.\n     */\n    function _atob(b64str) {\n        if (typeof window !== 'undefined' && window.atob) {\n            return window.atob(b64str);\n        } else {\n            throw new Error('You must supply a polyfill for window.atob in this environment');\n        }\n    }\n\n    function _parseJson(string) {\n        if (typeof JSON !== 'undefined' && JSON.parse) {\n            return JSON.parse(string);\n        } else {\n            throw new Error('You must supply a polyfill for JSON.parse in this environment');\n        }\n    }\n\n    function _findFunctionName(source, lineNumber/*, columnNumber*/) {\n        var syntaxes = [\n            // {name} = function ({args}) TODO args capture\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*function\\b/,\n            // function {name}({args}) m[1]=name m[2]=args\n            /function\\s+([^('\"`]*?)\\s*\\(([^)]*)\\)/,\n            // {name} = eval()\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*(?:eval|new Function)\\b/,\n            // fn_name() {\n            /\\b(?!(?:if|for|switch|while|with|catch)\\b)(?:(?:static)\\s+)?(\\S+)\\s*\\(.*?\\)\\s*\\{/,\n            // {name} = () => {\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*\\(.*?\\)\\s*=>/\n        ];\n        var lines = source.split('\\n');\n\n        // Walk backwards in the source lines until we find the line which matches one of the patterns above\n        var code = '';\n        var maxLines = Math.min(lineNumber, 20);\n        for (var i = 0; i < maxLines; ++i) {\n            // lineNo is 1-based, source[] is 0-based\n            var line = lines[lineNumber - i - 1];\n            var commentPos = line.indexOf('//');\n            if (commentPos >= 0) {\n                line = line.substr(0, commentPos);\n            }\n\n            if (line) {\n                code = line + code;\n                var len = syntaxes.length;\n                for (var index = 0; index < len; index++) {\n                    var m = syntaxes[index].exec(code);\n                    if (m && m[1]) {\n                        return m[1];\n                    }\n                }\n            }\n        }\n        return undefined;\n    }\n\n    function _ensureSupportedEnvironment() {\n        if (typeof Object.defineProperty !== 'function' || typeof Object.create !== 'function') {\n            throw new Error('Unable to consume source maps in older browsers');\n        }\n    }\n\n    function _ensureStackFrameIsLegit(stackframe) {\n        if (typeof stackframe !== 'object') {\n            throw new TypeError('Given StackFrame is not an object');\n        } else if (typeof stackframe.fileName !== 'string') {\n            throw new TypeError('Given file name is not a String');\n        } else if (typeof stackframe.lineNumber !== 'number' ||\n            stackframe.lineNumber % 1 !== 0 ||\n            stackframe.lineNumber < 1) {\n            throw new TypeError('Given line number must be a positive integer');\n        } else if (typeof stackframe.columnNumber !== 'number' ||\n            stackframe.columnNumber % 1 !== 0 ||\n            stackframe.columnNumber < 0) {\n            throw new TypeError('Given column number must be a non-negative integer');\n        }\n        return true;\n    }\n\n    function _findSourceMappingURL(source) {\n        var sourceMappingUrlRegExp = /\\/\\/[#@] ?sourceMappingURL=([^\\s'\"]+)\\s*$/mg;\n        var lastSourceMappingUrl;\n        var matchSourceMappingUrl;\n        // eslint-disable-next-line no-cond-assign\n        while (matchSourceMappingUrl = sourceMappingUrlRegExp.exec(source)) {\n            lastSourceMappingUrl = matchSourceMappingUrl[1];\n        }\n        if (lastSourceMappingUrl) {\n            return lastSourceMappingUrl;\n        } else {\n            throw new Error('sourceMappingURL not found');\n        }\n    }\n\n    function _extractLocationInfoFromSourceMapSource(stackframe, sourceMapConsumer, sourceCache) {\n        return new Promise(function(resolve, reject) {\n            var loc = sourceMapConsumer.originalPositionFor({\n                line: stackframe.lineNumber,\n                column: stackframe.columnNumber\n            });\n\n            if (loc.source) {\n                // cache mapped sources\n                var mappedSource = sourceMapConsumer.sourceContentFor(loc.source);\n                if (mappedSource) {\n                    sourceCache[loc.source] = mappedSource;\n                }\n\n                resolve(\n                    // given stackframe and source location, update stackframe\n                    new StackFrame({\n                        functionName: loc.name || stackframe.functionName,\n                        args: stackframe.args,\n                        fileName: loc.source,\n                        lineNumber: loc.line,\n                        columnNumber: loc.column\n                    }));\n            } else {\n                reject(new Error('Could not get original source for given stackframe and source map'));\n            }\n        });\n    }\n\n    /**\n     * @constructor\n     * @param {Object} opts\n     *      opts.sourceCache = {url: \"Source String\"} => preload source cache\n     *      opts.sourceMapConsumerCache = {/path/file.js.map: SourceMapConsumer}\n     *      opts.offline = True to prevent network requests.\n     *              Best effort without sources or source maps.\n     *      opts.ajax = Promise returning function to make X-Domain requests\n     */\n    return function StackTraceGPS(opts) {\n        if (!(this instanceof StackTraceGPS)) {\n            return new StackTraceGPS(opts);\n        }\n        opts = opts || {};\n\n        this.sourceCache = opts.sourceCache || {};\n        this.sourceMapConsumerCache = opts.sourceMapConsumerCache || {};\n\n        this.ajax = opts.ajax || _xdr;\n\n        this._atob = opts.atob || _atob;\n\n        this._get = function _get(location) {\n            return new Promise(function(resolve, reject) {\n                var isDataUrl = location.substr(0, 5) === 'data:';\n                if (this.sourceCache[location]) {\n                    resolve(this.sourceCache[location]);\n                } else if (opts.offline && !isDataUrl) {\n                    reject(new Error('Cannot make network requests in offline mode'));\n                } else {\n                    if (isDataUrl) {\n                        // data URLs can have parameters.\n                        // see http://tools.ietf.org/html/rfc2397\n                        var supportedEncodingRegexp =\n                            /^data:application\\/json;([\\w=:\"-]+;)*base64,/;\n                        var match = location.match(supportedEncodingRegexp);\n                        if (match) {\n                            var sourceMapStart = match[0].length;\n                            var encodedSource = location.substr(sourceMapStart);\n                            var source = this._atob(encodedSource);\n                            this.sourceCache[location] = source;\n                            resolve(source);\n                        } else {\n                            reject(new Error('The encoding of the inline sourcemap is not supported'));\n                        }\n                    } else {\n                        var xhrPromise = this.ajax(location, {method: 'get'});\n                        // Cache the Promise to prevent duplicate in-flight requests\n                        this.sourceCache[location] = xhrPromise;\n                        xhrPromise.then(resolve, reject);\n                    }\n                }\n            }.bind(this));\n        };\n\n        /**\n         * Creating SourceMapConsumers is expensive, so this wraps the creation of a\n         * SourceMapConsumer in a per-instance cache.\n         *\n         * @param {String} sourceMappingURL = URL to fetch source map from\n         * @param {String} defaultSourceRoot = Default source root for source map if undefined\n         * @returns {Promise} that resolves a SourceMapConsumer\n         */\n        this._getSourceMapConsumer = function _getSourceMapConsumer(sourceMappingURL, defaultSourceRoot) {\n            return new Promise(function(resolve) {\n                if (this.sourceMapConsumerCache[sourceMappingURL]) {\n                    resolve(this.sourceMapConsumerCache[sourceMappingURL]);\n                } else {\n                    var sourceMapConsumerPromise = new Promise(function(resolve, reject) {\n                        return this._get(sourceMappingURL).then(function(sourceMapSource) {\n                            if (typeof sourceMapSource === 'string') {\n                                sourceMapSource = _parseJson(sourceMapSource.replace(/^\\)\\]\\}'/, ''));\n                            }\n                            if (typeof sourceMapSource.sourceRoot === 'undefined') {\n                                sourceMapSource.sourceRoot = defaultSourceRoot;\n                            }\n\n                            resolve(new SourceMap.SourceMapConsumer(sourceMapSource));\n                        }, reject);\n                    }.bind(this));\n                    this.sourceMapConsumerCache[sourceMappingURL] = sourceMapConsumerPromise;\n                    resolve(sourceMapConsumerPromise);\n                }\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, enhance function name and use source maps for a\n         * better StackFrame.\n         *\n         * @param {StackFrame} stackframe object\n         * @returns {Promise} that resolves with with source-mapped StackFrame\n         */\n        this.pinpoint = function StackTraceGPS$$pinpoint(stackframe) {\n            return new Promise(function(resolve, reject) {\n                this.getMappedLocation(stackframe).then(function(mappedStackFrame) {\n                    function resolveMappedStackFrame() {\n                        resolve(mappedStackFrame);\n                    }\n\n                    this.findFunctionName(mappedStackFrame)\n                        .then(resolve, resolveMappedStackFrame)\n                        // eslint-disable-next-line no-unexpected-multiline\n                        ['catch'](resolveMappedStackFrame);\n                }.bind(this), reject);\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, guess function name from location information.\n         *\n         * @param {StackFrame} stackframe\n         * @returns {Promise} that resolves with enhanced StackFrame.\n         */\n        this.findFunctionName = function StackTraceGPS$$findFunctionName(stackframe) {\n            return new Promise(function(resolve, reject) {\n                _ensureStackFrameIsLegit(stackframe);\n                this._get(stackframe.fileName).then(function getSourceCallback(source) {\n                    var lineNumber = stackframe.lineNumber;\n                    var columnNumber = stackframe.columnNumber;\n                    var guessedFunctionName = _findFunctionName(source, lineNumber, columnNumber);\n                    // Only replace functionName if we found something\n                    if (guessedFunctionName) {\n                        resolve(new StackFrame({\n                            functionName: guessedFunctionName,\n                            args: stackframe.args,\n                            fileName: stackframe.fileName,\n                            lineNumber: lineNumber,\n                            columnNumber: columnNumber\n                        }));\n                    } else {\n                        resolve(stackframe);\n                    }\n                }, reject)['catch'](reject);\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, seek source-mapped location and return new enhanced StackFrame.\n         *\n         * @param {StackFrame} stackframe\n         * @returns {Promise} that resolves with enhanced StackFrame.\n         */\n        this.getMappedLocation = function StackTraceGPS$$getMappedLocation(stackframe) {\n            return new Promise(function(resolve, reject) {\n                _ensureSupportedEnvironment();\n                _ensureStackFrameIsLegit(stackframe);\n\n                var sourceCache = this.sourceCache;\n                var fileName = stackframe.fileName;\n                this._get(fileName).then(function(source) {\n                    var sourceMappingURL = _findSourceMappingURL(source);\n                    var isDataUrl = sourceMappingURL.substr(0, 5) === 'data:';\n                    var defaultSourceRoot = fileName.substring(0, fileName.lastIndexOf('/') + 1);\n\n                    if (sourceMappingURL[0] !== '/' && !isDataUrl && !(/^https?:\\/\\/|^\\/\\//i).test(sourceMappingURL)) {\n                        sourceMappingURL = defaultSourceRoot + sourceMappingURL;\n                    }\n\n                    return this._getSourceMapConsumer(sourceMappingURL, defaultSourceRoot)\n                        .then(function(sourceMapConsumer) {\n                            return _extractLocationInfoFromSourceMapSource(stackframe, sourceMapConsumer, sourceCache)\n                                .then(resolve)['catch'](function() {\n                                    resolve(stackframe);\n                                });\n                        });\n                }.bind(this), reject)['catch'](reject);\n            }.bind(this));\n        };\n    };\n}));\n\n},{\"source-map/lib/source-map-consumer\":11,\"stackframe\":15}],17:[function(require,module,exports){\n// Polyfill for old browsers\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/isArray\nif (!Array.isArray) {\n    Array.isArray = function(arg) {\n        return Object.prototype.toString.call(arg) === '[object Array]';\n    };\n}\n\nif (typeof Promise === 'undefined') {\n    ES6Promise.polyfill();\n}\n\n// ES5 Polyfills\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/bind\nif (!Function.prototype.bind) {\n    Function.prototype.bind = function(oThis) {\n        if (typeof this !== 'function') {\n            throw new TypeError('Function.prototype.bind - what is trying to be bound is not callable');\n        }\n\n        var aArgs = Array.prototype.slice.call(arguments, 1);\n        var fToBind = this;\n        var NoOp = function() {\n        };\n        var fBound = function() {\n            return fToBind.apply(this instanceof NoOp && oThis ? this : oThis,\n                aArgs.concat(Array.prototype.slice.call(arguments)));\n        };\n\n        NoOp.prototype = this.prototype;\n        fBound.prototype = new NoOp();\n\n        return fBound;\n    };\n}\n\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map\nif (!Array.prototype.map) {\n    Array.prototype.map = function(callback, thisArg) {\n        if (this === void 0 || this === null) {\n            throw new TypeError('this is null or not defined');\n        }\n        var O = Object(this);\n        var len = O.length >>> 0;\n        var T;\n        if (typeof callback !== 'function') {\n            throw new TypeError(callback + ' is not a function');\n        }\n        if (arguments.length > 1) {\n            T = thisArg;\n        }\n\n        var A = new Array(len);\n        var k = 0;\n\n        while (k < len) {\n            var kValue;\n            var mappedValue;\n            if (k in O) {\n                kValue = O[k];\n                mappedValue = callback.call(T, kValue, k, O);\n                A[k] = mappedValue;\n            }\n            k++;\n        }\n\n        return A;\n    };\n}\n\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/filter\nif (!Array.prototype.filter) {\n    Array.prototype.filter = function(callback/*, thisArg*/) {\n        if (this === void 0 || this === null) {\n            throw new TypeError('this is null or not defined');\n        }\n\n        var t = Object(this);\n        var len = t.length >>> 0;\n        if (typeof callback !== 'function') {\n            throw new TypeError(callback + ' is not a function');\n        }\n\n        var res = [];\n        var thisArg = arguments.length >= 2 ? arguments[1] : void 0;\n        for (var i = 0; i < len; i++) {\n            if (i in t) {\n                var val = t[i];\n                if (callback.call(thisArg, val, i, t)) {\n                    res.push(val);\n                }\n            }\n        }\n\n        return res;\n    };\n}\n\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach\nif (!Array.prototype.forEach) {\n    Array.prototype.forEach = function(callback, thisArg) {\n        var T;\n        var k;\n        if (this === null || this === undefined) {\n            throw new TypeError(' this is null or not defined');\n        }\n\n        var O = Object(this);\n        var len = O.length >>> 0;\n        if (typeof callback !== 'function') {\n            throw new TypeError(callback + ' is not a function');\n        }\n\n        if (arguments.length > 1) {\n            T = thisArg;\n        }\n        k = 0;\n        while (k < len) {\n            var kValue;\n            if (k in O) {\n                kValue = O[k];\n                callback.call(T, kValue, k, O);\n            }\n            k++;\n        }\n    };\n}\n\n},{}],18:[function(require,module,exports){\n(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stacktrace', ['error-stack-parser', 'stack-generator', 'stacktrace-gps'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('error-stack-parser'), require('stack-generator'), require('stacktrace-gps'));\n    } else {\n        root.StackTrace = factory(root.ErrorStackParser, root.StackGenerator, root.StackTraceGPS);\n    }\n}(this, function StackTrace(ErrorStackParser, StackGenerator, StackTraceGPS) {\n    var _options = {\n        filter: function(stackframe) {\n            // Filter out stackframes for this library by default\n            return (stackframe.functionName || '').indexOf('StackTrace$$') === -1 &&\n                (stackframe.functionName || '').indexOf('ErrorStackParser$$') === -1 &&\n                (stackframe.functionName || '').indexOf('StackTraceGPS$$') === -1 &&\n                (stackframe.functionName || '').indexOf('StackGenerator$$') === -1;\n        },\n        sourceCache: {}\n    };\n\n    var _generateError = function StackTrace$$GenerateError() {\n        try {\n            // Error must be thrown to get stack in IE\n            throw new Error();\n        } catch (err) {\n            return err;\n        }\n    };\n\n    /**\n     * Merge 2 given Objects. If a conflict occurs the second object wins.\n     * Does not do deep merges.\n     *\n     * @param {Object} first base object\n     * @param {Object} second overrides\n     * @returns {Object} merged first and second\n     * @private\n     */\n    function _merge(first, second) {\n        var target = {};\n\n        [first, second].forEach(function(obj) {\n            for (var prop in obj) {\n                if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n                    target[prop] = obj[prop];\n                }\n            }\n            return target;\n        });\n\n        return target;\n    }\n\n    function _isShapedLikeParsableError(err) {\n        return err.stack || err['opera#sourceloc'];\n    }\n\n    function _filtered(stackframes, filter) {\n        if (typeof filter === 'function') {\n            return stackframes.filter(filter);\n        }\n        return stackframes;\n    }\n\n    return {\n        /**\n         * Get a backtrace from invocation point.\n         *\n         * @param {Object} opts\n         * @returns {Array} of StackFrame\n         */\n        get: function StackTrace$$get(opts) {\n            var err = _generateError();\n            return _isShapedLikeParsableError(err) ? this.fromError(err, opts) : this.generateArtificially(opts);\n        },\n\n        /**\n         * Get a backtrace from invocation point.\n         * IMPORTANT: Does not handle source maps or guess function names!\n         *\n         * @param {Object} opts\n         * @returns {Array} of StackFrame\n         */\n        getSync: function StackTrace$$getSync(opts) {\n            opts = _merge(_options, opts);\n            var err = _generateError();\n            var stack = _isShapedLikeParsableError(err) ? ErrorStackParser.parse(err) : StackGenerator.backtrace(opts);\n            return _filtered(stack, opts.filter);\n        },\n\n        /**\n         * Given an error object, parse it.\n         *\n         * @param {Error} error object\n         * @param {Object} opts\n         * @returns {Promise} for Array[StackFrame}\n         */\n        fromError: function StackTrace$$fromError(error, opts) {\n            opts = _merge(_options, opts);\n            var gps = new StackTraceGPS(opts);\n            return new Promise(function(resolve) {\n                var stackframes = _filtered(ErrorStackParser.parse(error), opts.filter);\n                resolve(Promise.all(stackframes.map(function(sf) {\n                    return new Promise(function(resolve) {\n                        function resolveOriginal() {\n                            resolve(sf);\n                        }\n\n                        gps.pinpoint(sf).then(resolve, resolveOriginal)['catch'](resolveOriginal);\n                    });\n                })));\n            }.bind(this));\n        },\n\n        /**\n         * Use StackGenerator to generate a backtrace.\n         *\n         * @param {Object} opts\n         * @returns {Promise} of Array[StackFrame]\n         */\n        generateArtificially: function StackTrace$$generateArtificially(opts) {\n            opts = _merge(_options, opts);\n            var stackFrames = StackGenerator.backtrace(opts);\n            if (typeof opts.filter === 'function') {\n                stackFrames = stackFrames.filter(opts.filter);\n            }\n            return Promise.resolve(stackFrames);\n        },\n\n        /**\n         * Given a function, wrap it such that invocations trigger a callback that\n         * is called with a stack trace.\n         *\n         * @param {Function} fn to be instrumented\n         * @param {Function} callback function to call with a stack trace on invocation\n         * @param {Function} errback optional function to call with error if unable to get stack trace.\n         * @param {Object} thisArg optional context object (e.g. window)\n         */\n        instrument: function StackTrace$$instrument(fn, callback, errback, thisArg) {\n            if (typeof fn !== 'function') {\n                throw new Error('Cannot instrument non-function object');\n            } else if (typeof fn.__stacktraceOriginalFn === 'function') {\n                // Already instrumented, return given Function\n                return fn;\n            }\n\n            var instrumented = function StackTrace$$instrumented() {\n                try {\n                    this.get().then(callback, errback)['catch'](errback);\n                    return fn.apply(thisArg || this, arguments);\n                } catch (e) {\n                    if (_isShapedLikeParsableError(e)) {\n                        this.fromError(e).then(callback, errback)['catch'](errback);\n                    }\n                    throw e;\n                }\n            }.bind(this);\n            instrumented.__stacktraceOriginalFn = fn;\n\n            return instrumented;\n        },\n\n        /**\n         * Given a function that has been instrumented,\n         * revert the function to it's original (non-instrumented) state.\n         *\n         * @param {Function} fn to de-instrument\n         */\n        deinstrument: function StackTrace$$deinstrument(fn) {\n            if (typeof fn !== 'function') {\n                throw new Error('Cannot de-instrument non-function object');\n            } else if (typeof fn.__stacktraceOriginalFn === 'function') {\n                return fn.__stacktraceOriginalFn;\n            } else {\n                // Function not instrumented, return original\n                return fn;\n            }\n        },\n\n        /**\n         * Given an error message and Array of StackFrames, serialize and POST to given URL.\n         *\n         * @param {Array} stackframes\n         * @param {String} url\n         * @param {String} errorMsg\n         * @param {Object} requestOptions\n         */\n        report: function StackTrace$$report(stackframes, url, errorMsg, requestOptions) {\n            return new Promise(function(resolve, reject) {\n                var req = new XMLHttpRequest();\n                req.onerror = reject;\n                req.onreadystatechange = function onreadystatechange() {\n                    if (req.readyState === 4) {\n                        if (req.status >= 200 && req.status < 400) {\n                            resolve(req.responseText);\n                        } else {\n                            reject(new Error('POST to ' + url + ' failed with status: ' + req.status));\n                        }\n                    }\n                };\n                req.open('post', url);\n\n                // Set request headers\n                req.setRequestHeader('Content-Type', 'application/json');\n                if (requestOptions && typeof requestOptions.headers === 'object') {\n                    var headers = requestOptions.headers;\n                    for (var header in headers) {\n                        if (Object.prototype.hasOwnProperty.call(headers, header)) {\n                            req.setRequestHeader(header, headers[header]);\n                        }\n                    }\n                }\n\n                var reportPayload = {stack: stackframes};\n                if (errorMsg !== undefined && errorMsg !== null) {\n                    reportPayload.message = errorMsg;\n                }\n\n                req.send(JSON.stringify(reportPayload));\n            });\n        }\n    };\n}));\n\n},{\"error-stack-parser\":1,\"stack-generator\":14,\"stacktrace-gps\":16}]},{},[3,4,17,18])(18)\n});\n\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stackframe', [], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory();\n    } else {\n        root.StackFrame = factory();\n    }\n}(this, function() {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function _capitalize(str) {\n        return str.charAt(0).toUpperCase() + str.substring(1);\n    }\n\n    function _getter(p) {\n        return function() {\n            return this[p];\n        };\n    }\n\n    var booleanProps = ['isConstructor', 'isEval', 'isNative', 'isToplevel'];\n    var numericProps = ['columnNumber', 'lineNumber'];\n    var stringProps = ['fileName', 'functionName', 'source'];\n    var arrayProps = ['args'];\n\n    var props = booleanProps.concat(numericProps, stringProps, arrayProps);\n\n    function StackFrame(obj) {\n        if (!obj) return;\n        for (var i = 0; i < props.length; i++) {\n            if (obj[props[i]] !== undefined) {\n                this['set' + _capitalize(props[i])](obj[props[i]]);\n            }\n        }\n    }\n\n    StackFrame.prototype = {\n        getArgs: function() {\n            return this.args;\n        },\n        setArgs: function(v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        getEvalOrigin: function() {\n            return this.evalOrigin;\n        },\n        setEvalOrigin: function(v) {\n            if (v instanceof StackFrame) {\n                this.evalOrigin = v;\n            } else if (v instanceof Object) {\n                this.evalOrigin = new StackFrame(v);\n            } else {\n                throw new TypeError('Eval Origin must be an Object or StackFrame');\n            }\n        },\n\n        toString: function() {\n            var fileName = this.getFileName() || '';\n            var lineNumber = this.getLineNumber() || '';\n            var columnNumber = this.getColumnNumber() || '';\n            var functionName = this.getFunctionName() || '';\n            if (this.getIsEval()) {\n                if (fileName) {\n                    return '[eval] (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n                }\n                return '[eval]:' + lineNumber + ':' + columnNumber;\n            }\n            if (functionName) {\n                return functionName + ' (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n            }\n            return fileName + ':' + lineNumber + ':' + columnNumber;\n        }\n    };\n\n    StackFrame.fromString = function StackFrame$$fromString(str) {\n        var argsStartIndex = str.indexOf('(');\n        var argsEndIndex = str.lastIndexOf(')');\n\n        var functionName = str.substring(0, argsStartIndex);\n        var args = str.substring(argsStartIndex + 1, argsEndIndex).split(',');\n        var locationString = str.substring(argsEndIndex + 1);\n\n        if (locationString.indexOf('@') === 0) {\n            var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, '');\n            var fileName = parts[1];\n            var lineNumber = parts[2];\n            var columnNumber = parts[3];\n        }\n\n        return new StackFrame({\n            functionName: functionName,\n            args: args || undefined,\n            fileName: fileName,\n            lineNumber: lineNumber || undefined,\n            columnNumber: columnNumber || undefined\n        });\n    };\n\n    for (var i = 0; i < booleanProps.length; i++) {\n        StackFrame.prototype['get' + _capitalize(booleanProps[i])] = _getter(booleanProps[i]);\n        StackFrame.prototype['set' + _capitalize(booleanProps[i])] = (function(p) {\n            return function(v) {\n                this[p] = Boolean(v);\n            };\n        })(booleanProps[i]);\n    }\n\n    for (var j = 0; j < numericProps.length; j++) {\n        StackFrame.prototype['get' + _capitalize(numericProps[j])] = _getter(numericProps[j]);\n        StackFrame.prototype['set' + _capitalize(numericProps[j])] = (function(p) {\n            return function(v) {\n                if (!_isNumber(v)) {\n                    throw new TypeError(p + ' must be a Number');\n                }\n                this[p] = Number(v);\n            };\n        })(numericProps[j]);\n    }\n\n    for (var k = 0; k < stringProps.length; k++) {\n        StackFrame.prototype['get' + _capitalize(stringProps[k])] = _getter(stringProps[k]);\n        StackFrame.prototype['set' + _capitalize(stringProps[k])] = (function(p) {\n            return function(v) {\n                this[p] = String(v);\n            };\n        })(stringProps[k]);\n    }\n\n    return StackFrame;\n}));\n", "/*!\n * @overview es6-promise - a tiny implementation of Promises/A+.\n * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)\n * @license   Licensed under MIT license\n *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE\n * @version   3.3.1\n */\n\n(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n    typeof define === 'function' && define.amd ? define(factory) :\n    (global.ES6Promise = factory());\n}(this, (function () { 'use strict';\n\nfunction objectOrFunction(x) {\n  return typeof x === 'function' || typeof x === 'object' && x !== null;\n}\n\nfunction isFunction(x) {\n  return typeof x === 'function';\n}\n\nvar _isArray = undefined;\nif (!Array.isArray) {\n  _isArray = function (x) {\n    return Object.prototype.toString.call(x) === '[object Array]';\n  };\n} else {\n  _isArray = Array.isArray;\n}\n\nvar isArray = _isArray;\n\nvar len = 0;\nvar vertxNext = undefined;\nvar customSchedulerFn = undefined;\n\nvar asap = function asap(callback, arg) {\n  queue[len] = callback;\n  queue[len + 1] = arg;\n  len += 2;\n  if (len === 2) {\n    // If len is 2, that means that we need to schedule an async flush.\n    // If additional callbacks are queued before the queue is flushed, they\n    // will be processed by this flush that we are scheduling.\n    if (customSchedulerFn) {\n      customSchedulerFn(flush);\n    } else {\n      scheduleFlush();\n    }\n  }\n};\n\nfunction setScheduler(scheduleFn) {\n  customSchedulerFn = scheduleFn;\n}\n\nfunction setAsap(asapFn) {\n  asap = asapFn;\n}\n\nvar browserWindow = typeof window !== 'undefined' ? window : undefined;\nvar browserGlobal = browserWindow || {};\nvar BrowserMutationObserver = browserGlobal.MutationObserver || browserGlobal.WebKitMutationObserver;\nvar isNode = typeof self === 'undefined' && typeof process !== 'undefined' && ({}).toString.call(process) === '[object process]';\n\n// test for web worker but not in IE10\nvar isWorker = typeof Uint8ClampedArray !== 'undefined' && typeof importScripts !== 'undefined' && typeof MessageChannel !== 'undefined';\n\n// node\nfunction useNextTick() {\n  // node version 0.10.x displays a deprecation warning when nextTick is used recursively\n  // see https://github.com/cujojs/when/issues/410 for details\n  return function () {\n    return process.nextTick(flush);\n  };\n}\n\n// vertx\nfunction useVertxTimer() {\n  return function () {\n    vertxNext(flush);\n  };\n}\n\nfunction useMutationObserver() {\n  var iterations = 0;\n  var observer = new BrowserMutationObserver(flush);\n  var node = document.createTextNode('');\n  observer.observe(node, { characterData: true });\n\n  return function () {\n    node.data = iterations = ++iterations % 2;\n  };\n}\n\n// web worker\nfunction useMessageChannel() {\n  var channel = new MessageChannel();\n  channel.port1.onmessage = flush;\n  return function () {\n    return channel.port2.postMessage(0);\n  };\n}\n\nfunction useSetTimeout() {\n  // Store setTimeout reference so es6-promise will be unaffected by\n  // other code modifying setTimeout (like sinon.useFakeTimers())\n  var globalSetTimeout = setTimeout;\n  return function () {\n    return globalSetTimeout(flush, 1);\n  };\n}\n\nvar queue = new Array(1000);\nfunction flush() {\n  for (var i = 0; i < len; i += 2) {\n    var callback = queue[i];\n    var arg = queue[i + 1];\n\n    callback(arg);\n\n    queue[i] = undefined;\n    queue[i + 1] = undefined;\n  }\n\n  len = 0;\n}\n\nfunction attemptVertx() {\n  try {\n    var r = require;\n    var vertx = r('vertx');\n    vertxNext = vertx.runOnLoop || vertx.runOnContext;\n    return useVertxTimer();\n  } catch (e) {\n    return useSetTimeout();\n  }\n}\n\nvar scheduleFlush = undefined;\n// Decide what async method to use to triggering processing of queued callbacks:\nif (isNode) {\n  scheduleFlush = useNextTick();\n} else if (BrowserMutationObserver) {\n  scheduleFlush = useMutationObserver();\n} else if (isWorker) {\n  scheduleFlush = useMessageChannel();\n} else if (browserWindow === undefined && typeof require === 'function') {\n  scheduleFlush = attemptVertx();\n} else {\n  scheduleFlush = useSetTimeout();\n}\n\nfunction then(onFulfillment, onRejection) {\n  var _arguments = arguments;\n\n  var parent = this;\n\n  var child = new this.constructor(noop);\n\n  if (child[PROMISE_ID] === undefined) {\n    makePromise(child);\n  }\n\n  var _state = parent._state;\n\n  if (_state) {\n    (function () {\n      var callback = _arguments[_state - 1];\n      asap(function () {\n        return invokeCallback(_state, child, callback, parent._result);\n      });\n    })();\n  } else {\n    subscribe(parent, child, onFulfillment, onRejection);\n  }\n\n  return child;\n}\n\n/**\n  `Promise.resolve` returns a promise that will become resolved with the\n  passed `value`. It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    resolve(1);\n  });\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.resolve(1);\n\n  promise.then(function(value){\n    // value === 1\n  });\n  ```\n\n  @method resolve\n  @static\n  @param {Any} value value that the returned promise will be resolved with\n  Useful for tooling.\n  @return {Promise} a promise that will become fulfilled with the given\n  `value`\n*/\nfunction resolve(object) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (object && typeof object === 'object' && object.constructor === Constructor) {\n    return object;\n  }\n\n  var promise = new Constructor(noop);\n  _resolve(promise, object);\n  return promise;\n}\n\nvar PROMISE_ID = Math.random().toString(36).substring(16);\n\nfunction noop() {}\n\nvar PENDING = void 0;\nvar FULFILLED = 1;\nvar REJECTED = 2;\n\nvar GET_THEN_ERROR = new ErrorObject();\n\nfunction selfFulfillment() {\n  return new TypeError(\"You cannot resolve a promise with itself\");\n}\n\nfunction cannotReturnOwn() {\n  return new TypeError('A promises callback cannot return that same promise.');\n}\n\nfunction getThen(promise) {\n  try {\n    return promise.then;\n  } catch (error) {\n    GET_THEN_ERROR.error = error;\n    return GET_THEN_ERROR;\n  }\n}\n\nfunction tryThen(then, value, fulfillmentHandler, rejectionHandler) {\n  try {\n    then.call(value, fulfillmentHandler, rejectionHandler);\n  } catch (e) {\n    return e;\n  }\n}\n\nfunction handleForeignThenable(promise, thenable, then) {\n  asap(function (promise) {\n    var sealed = false;\n    var error = tryThen(then, thenable, function (value) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n      if (thenable !== value) {\n        _resolve(promise, value);\n      } else {\n        fulfill(promise, value);\n      }\n    }, function (reason) {\n      if (sealed) {\n        return;\n      }\n      sealed = true;\n\n      _reject(promise, reason);\n    }, 'Settle: ' + (promise._label || ' unknown promise'));\n\n    if (!sealed && error) {\n      sealed = true;\n      _reject(promise, error);\n    }\n  }, promise);\n}\n\nfunction handleOwnThenable(promise, thenable) {\n  if (thenable._state === FULFILLED) {\n    fulfill(promise, thenable._result);\n  } else if (thenable._state === REJECTED) {\n    _reject(promise, thenable._result);\n  } else {\n    subscribe(thenable, undefined, function (value) {\n      return _resolve(promise, value);\n    }, function (reason) {\n      return _reject(promise, reason);\n    });\n  }\n}\n\nfunction handleMaybeThenable(promise, maybeThenable, then$$) {\n  if (maybeThenable.constructor === promise.constructor && then$$ === then && maybeThenable.constructor.resolve === resolve) {\n    handleOwnThenable(promise, maybeThenable);\n  } else {\n    if (then$$ === GET_THEN_ERROR) {\n      _reject(promise, GET_THEN_ERROR.error);\n    } else if (then$$ === undefined) {\n      fulfill(promise, maybeThenable);\n    } else if (isFunction(then$$)) {\n      handleForeignThenable(promise, maybeThenable, then$$);\n    } else {\n      fulfill(promise, maybeThenable);\n    }\n  }\n}\n\nfunction _resolve(promise, value) {\n  if (promise === value) {\n    _reject(promise, selfFulfillment());\n  } else if (objectOrFunction(value)) {\n    handleMaybeThenable(promise, value, getThen(value));\n  } else {\n    fulfill(promise, value);\n  }\n}\n\nfunction publishRejection(promise) {\n  if (promise._onerror) {\n    promise._onerror(promise._result);\n  }\n\n  publish(promise);\n}\n\nfunction fulfill(promise, value) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n\n  promise._result = value;\n  promise._state = FULFILLED;\n\n  if (promise._subscribers.length !== 0) {\n    asap(publish, promise);\n  }\n}\n\nfunction _reject(promise, reason) {\n  if (promise._state !== PENDING) {\n    return;\n  }\n  promise._state = REJECTED;\n  promise._result = reason;\n\n  asap(publishRejection, promise);\n}\n\nfunction subscribe(parent, child, onFulfillment, onRejection) {\n  var _subscribers = parent._subscribers;\n  var length = _subscribers.length;\n\n  parent._onerror = null;\n\n  _subscribers[length] = child;\n  _subscribers[length + FULFILLED] = onFulfillment;\n  _subscribers[length + REJECTED] = onRejection;\n\n  if (length === 0 && parent._state) {\n    asap(publish, parent);\n  }\n}\n\nfunction publish(promise) {\n  var subscribers = promise._subscribers;\n  var settled = promise._state;\n\n  if (subscribers.length === 0) {\n    return;\n  }\n\n  var child = undefined,\n      callback = undefined,\n      detail = promise._result;\n\n  for (var i = 0; i < subscribers.length; i += 3) {\n    child = subscribers[i];\n    callback = subscribers[i + settled];\n\n    if (child) {\n      invokeCallback(settled, child, callback, detail);\n    } else {\n      callback(detail);\n    }\n  }\n\n  promise._subscribers.length = 0;\n}\n\nfunction ErrorObject() {\n  this.error = null;\n}\n\nvar TRY_CATCH_ERROR = new ErrorObject();\n\nfunction tryCatch(callback, detail) {\n  try {\n    return callback(detail);\n  } catch (e) {\n    TRY_CATCH_ERROR.error = e;\n    return TRY_CATCH_ERROR;\n  }\n}\n\nfunction invokeCallback(settled, promise, callback, detail) {\n  var hasCallback = isFunction(callback),\n      value = undefined,\n      error = undefined,\n      succeeded = undefined,\n      failed = undefined;\n\n  if (hasCallback) {\n    value = tryCatch(callback, detail);\n\n    if (value === TRY_CATCH_ERROR) {\n      failed = true;\n      error = value.error;\n      value = null;\n    } else {\n      succeeded = true;\n    }\n\n    if (promise === value) {\n      _reject(promise, cannotReturnOwn());\n      return;\n    }\n  } else {\n    value = detail;\n    succeeded = true;\n  }\n\n  if (promise._state !== PENDING) {\n    // noop\n  } else if (hasCallback && succeeded) {\n      _resolve(promise, value);\n    } else if (failed) {\n      _reject(promise, error);\n    } else if (settled === FULFILLED) {\n      fulfill(promise, value);\n    } else if (settled === REJECTED) {\n      _reject(promise, value);\n    }\n}\n\nfunction initializePromise(promise, resolver) {\n  try {\n    resolver(function resolvePromise(value) {\n      _resolve(promise, value);\n    }, function rejectPromise(reason) {\n      _reject(promise, reason);\n    });\n  } catch (e) {\n    _reject(promise, e);\n  }\n}\n\nvar id = 0;\nfunction nextId() {\n  return id++;\n}\n\nfunction makePromise(promise) {\n  promise[PROMISE_ID] = id++;\n  promise._state = undefined;\n  promise._result = undefined;\n  promise._subscribers = [];\n}\n\nfunction Enumerator(Constructor, input) {\n  this._instanceConstructor = Constructor;\n  this.promise = new Constructor(noop);\n\n  if (!this.promise[PROMISE_ID]) {\n    makePromise(this.promise);\n  }\n\n  if (isArray(input)) {\n    this._input = input;\n    this.length = input.length;\n    this._remaining = input.length;\n\n    this._result = new Array(this.length);\n\n    if (this.length === 0) {\n      fulfill(this.promise, this._result);\n    } else {\n      this.length = this.length || 0;\n      this._enumerate();\n      if (this._remaining === 0) {\n        fulfill(this.promise, this._result);\n      }\n    }\n  } else {\n    _reject(this.promise, validationError());\n  }\n}\n\nfunction validationError() {\n  return new Error('Array Methods must be provided an Array');\n};\n\nEnumerator.prototype._enumerate = function () {\n  var length = this.length;\n  var _input = this._input;\n\n  for (var i = 0; this._state === PENDING && i < length; i++) {\n    this._eachEntry(_input[i], i);\n  }\n};\n\nEnumerator.prototype._eachEntry = function (entry, i) {\n  var c = this._instanceConstructor;\n  var resolve$$ = c.resolve;\n\n  if (resolve$$ === resolve) {\n    var _then = getThen(entry);\n\n    if (_then === then && entry._state !== PENDING) {\n      this._settledAt(entry._state, i, entry._result);\n    } else if (typeof _then !== 'function') {\n      this._remaining--;\n      this._result[i] = entry;\n    } else if (c === Promise) {\n      var promise = new c(noop);\n      handleMaybeThenable(promise, entry, _then);\n      this._willSettleAt(promise, i);\n    } else {\n      this._willSettleAt(new c(function (resolve$$) {\n        return resolve$$(entry);\n      }), i);\n    }\n  } else {\n    this._willSettleAt(resolve$$(entry), i);\n  }\n};\n\nEnumerator.prototype._settledAt = function (state, i, value) {\n  var promise = this.promise;\n\n  if (promise._state === PENDING) {\n    this._remaining--;\n\n    if (state === REJECTED) {\n      _reject(promise, value);\n    } else {\n      this._result[i] = value;\n    }\n  }\n\n  if (this._remaining === 0) {\n    fulfill(promise, this._result);\n  }\n};\n\nEnumerator.prototype._willSettleAt = function (promise, i) {\n  var enumerator = this;\n\n  subscribe(promise, undefined, function (value) {\n    return enumerator._settledAt(FULFILLED, i, value);\n  }, function (reason) {\n    return enumerator._settledAt(REJECTED, i, reason);\n  });\n};\n\n/**\n  `Promise.all` accepts an array of promises, and returns a new promise which\n  is fulfilled with an array of fulfillment values for the passed promises, or\n  rejected with the reason of the first passed promise to be rejected. It casts all\n  elements of the passed iterable to promises as it runs this algorithm.\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = resolve(2);\n  let promise3 = resolve(3);\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // The array here would be [ 1, 2, 3 ];\n  });\n  ```\n\n  If any of the `promises` given to `all` are rejected, the first promise\n  that is rejected will be given as an argument to the returned promises's\n  rejection handler. For example:\n\n  Example:\n\n  ```javascript\n  let promise1 = resolve(1);\n  let promise2 = reject(new Error(\"2\"));\n  let promise3 = reject(new Error(\"3\"));\n  let promises = [ promise1, promise2, promise3 ];\n\n  Promise.all(promises).then(function(array){\n    // Code here never runs because there are rejected promises!\n  }, function(error) {\n    // error.message === \"2\"\n  });\n  ```\n\n  @method all\n  @static\n  @param {Array} entries array of promises\n  @param {String} label optional string for labeling the promise.\n  Useful for tooling.\n  @return {Promise} promise that is fulfilled when all `promises` have been\n  fulfilled, or rejected if any of them become rejected.\n  @static\n*/\nfunction all(entries) {\n  return new Enumerator(this, entries).promise;\n}\n\n/**\n  `Promise.race` returns a new promise which is settled in the same way as the\n  first passed promise to settle.\n\n  Example:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 2');\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // result === 'promise 2' because it was resolved before promise1\n    // was resolved.\n  });\n  ```\n\n  `Promise.race` is deterministic in that only the state of the first\n  settled promise matters. For example, even if other promises given to the\n  `promises` array argument are resolved, but the first settled promise has\n  become rejected before the other promises became fulfilled, the returned\n  promise will become rejected:\n\n  ```javascript\n  let promise1 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      resolve('promise 1');\n    }, 200);\n  });\n\n  let promise2 = new Promise(function(resolve, reject){\n    setTimeout(function(){\n      reject(new Error('promise 2'));\n    }, 100);\n  });\n\n  Promise.race([promise1, promise2]).then(function(result){\n    // Code here never runs\n  }, function(reason){\n    // reason.message === 'promise 2' because promise 2 became rejected before\n    // promise 1 became fulfilled\n  });\n  ```\n\n  An example real-world use case is implementing timeouts:\n\n  ```javascript\n  Promise.race([ajax('foo.json'), timeout(5000)])\n  ```\n\n  @method race\n  @static\n  @param {Array} promises array of promises to observe\n  Useful for tooling.\n  @return {Promise} a promise which settles in the same way as the first passed\n  promise to settle.\n*/\nfunction race(entries) {\n  /*jshint validthis:true */\n  var Constructor = this;\n\n  if (!isArray(entries)) {\n    return new Constructor(function (_, reject) {\n      return reject(new TypeError('You must pass an array to race.'));\n    });\n  } else {\n    return new Constructor(function (resolve, reject) {\n      var length = entries.length;\n      for (var i = 0; i < length; i++) {\n        Constructor.resolve(entries[i]).then(resolve, reject);\n      }\n    });\n  }\n}\n\n/**\n  `Promise.reject` returns a promise rejected with the passed `reason`.\n  It is shorthand for the following:\n\n  ```javascript\n  let promise = new Promise(function(resolve, reject){\n    reject(new Error('WHOOPS'));\n  });\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  Instead of writing the above, your code now simply becomes the following:\n\n  ```javascript\n  let promise = Promise.reject(new Error('WHOOPS'));\n\n  promise.then(function(value){\n    // Code here doesn't run because the promise is rejected!\n  }, function(reason){\n    // reason.message === 'WHOOPS'\n  });\n  ```\n\n  @method reject\n  @static\n  @param {Any} reason value that the returned promise will be rejected with.\n  Useful for tooling.\n  @return {Promise} a promise rejected with the given `reason`.\n*/\nfunction reject(reason) {\n  /*jshint validthis:true */\n  var Constructor = this;\n  var promise = new Constructor(noop);\n  _reject(promise, reason);\n  return promise;\n}\n\nfunction needsResolver() {\n  throw new TypeError('You must pass a resolver function as the first argument to the promise constructor');\n}\n\nfunction needsNew() {\n  throw new TypeError(\"Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.\");\n}\n\n/**\n  Promise objects represent the eventual result of an asynchronous operation. The\n  primary way of interacting with a promise is through its `then` method, which\n  registers callbacks to receive either a promise's eventual value or the reason\n  why the promise cannot be fulfilled.\n\n  Terminology\n  -----------\n\n  - `promise` is an object or function with a `then` method whose behavior conforms to this specification.\n  - `thenable` is an object or function that defines a `then` method.\n  - `value` is any legal JavaScript value (including undefined, a thenable, or a promise).\n  - `exception` is a value that is thrown using the throw statement.\n  - `reason` is a value that indicates why a promise was rejected.\n  - `settled` the final resting state of a promise, fulfilled or rejected.\n\n  A promise can be in one of three states: pending, fulfilled, or rejected.\n\n  Promises that are fulfilled have a fulfillment value and are in the fulfilled\n  state.  Promises that are rejected have a rejection reason and are in the\n  rejected state.  A fulfillment value is never a thenable.\n\n  Promises can also be said to *resolve* a value.  If this value is also a\n  promise, then the original promise's settled state will match the value's\n  settled state.  So a promise that *resolves* a promise that rejects will\n  itself reject, and a promise that *resolves* a promise that fulfills will\n  itself fulfill.\n\n\n  Basic Usage:\n  ------------\n\n  ```js\n  let promise = new Promise(function(resolve, reject) {\n    // on success\n    resolve(value);\n\n    // on failure\n    reject(reason);\n  });\n\n  promise.then(function(value) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Advanced Usage:\n  ---------------\n\n  Promises shine when abstracting away asynchronous interactions such as\n  `XMLHttpRequest`s.\n\n  ```js\n  function getJSON(url) {\n    return new Promise(function(resolve, reject){\n      let xhr = new XMLHttpRequest();\n\n      xhr.open('GET', url);\n      xhr.onreadystatechange = handler;\n      xhr.responseType = 'json';\n      xhr.setRequestHeader('Accept', 'application/json');\n      xhr.send();\n\n      function handler() {\n        if (this.readyState === this.DONE) {\n          if (this.status === 200) {\n            resolve(this.response);\n          } else {\n            reject(new Error('getJSON: `' + url + '` failed with status: [' + this.status + ']'));\n          }\n        }\n      };\n    });\n  }\n\n  getJSON('/posts.json').then(function(json) {\n    // on fulfillment\n  }, function(reason) {\n    // on rejection\n  });\n  ```\n\n  Unlike callbacks, promises are great composable primitives.\n\n  ```js\n  Promise.all([\n    getJSON('/posts'),\n    getJSON('/comments')\n  ]).then(function(values){\n    values[0] // => postsJSON\n    values[1] // => commentsJSON\n\n    return values;\n  });\n  ```\n\n  @class Promise\n  @param {function} resolver\n  Useful for tooling.\n  @constructor\n*/\nfunction Promise(resolver) {\n  this[PROMISE_ID] = nextId();\n  this._result = this._state = undefined;\n  this._subscribers = [];\n\n  if (noop !== resolver) {\n    typeof resolver !== 'function' && needsResolver();\n    this instanceof Promise ? initializePromise(this, resolver) : needsNew();\n  }\n}\n\nPromise.all = all;\nPromise.race = race;\nPromise.resolve = resolve;\nPromise.reject = reject;\nPromise._setScheduler = setScheduler;\nPromise._setAsap = setAsap;\nPromise._asap = asap;\n\nPromise.prototype = {\n  constructor: Promise,\n\n  /**\n    The primary way of interacting with a promise is through its `then` method,\n    which registers callbacks to receive either a promise's eventual value or the\n    reason why the promise cannot be fulfilled.\n  \n    ```js\n    findUser().then(function(user){\n      // user is available\n    }, function(reason){\n      // user is unavailable, and you are given the reason why\n    });\n    ```\n  \n    Chaining\n    --------\n  \n    The return value of `then` is itself a promise.  This second, 'downstream'\n    promise is resolved with the return value of the first promise's fulfillment\n    or rejection handler, or rejected if the handler throws an exception.\n  \n    ```js\n    findUser().then(function (user) {\n      return user.name;\n    }, function (reason) {\n      return 'default name';\n    }).then(function (userName) {\n      // If `findUser` fulfilled, `userName` will be the user's name, otherwise it\n      // will be `'default name'`\n    });\n  \n    findUser().then(function (user) {\n      throw new Error('Found user, but still unhappy');\n    }, function (reason) {\n      throw new Error('`findUser` rejected and we're unhappy');\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // if `findUser` fulfilled, `reason` will be 'Found user, but still unhappy'.\n      // If `findUser` rejected, `reason` will be '`findUser` rejected and we're unhappy'.\n    });\n    ```\n    If the downstream promise does not specify a rejection handler, rejection reasons will be propagated further downstream.\n  \n    ```js\n    findUser().then(function (user) {\n      throw new PedagogicalException('Upstream error');\n    }).then(function (value) {\n      // never reached\n    }).then(function (value) {\n      // never reached\n    }, function (reason) {\n      // The `PedgagocialException` is propagated all the way down to here\n    });\n    ```\n  \n    Assimilation\n    ------------\n  \n    Sometimes the value you want to propagate to a downstream promise can only be\n    retrieved asynchronously. This can be achieved by returning a promise in the\n    fulfillment or rejection handler. The downstream promise will then be pending\n    until the returned promise is settled. This is called *assimilation*.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // The user's comments are now available\n    });\n    ```\n  \n    If the assimliated promise rejects, then the downstream promise will also reject.\n  \n    ```js\n    findUser().then(function (user) {\n      return findCommentsByAuthor(user);\n    }).then(function (comments) {\n      // If `findCommentsByAuthor` fulfills, we'll have the value here\n    }, function (reason) {\n      // If `findCommentsByAuthor` rejects, we'll have the reason here\n    });\n    ```\n  \n    Simple Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let result;\n  \n    try {\n      result = findResult();\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n    findResult(function(result, err){\n      if (err) {\n        // failure\n      } else {\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findResult().then(function(result){\n      // success\n    }, function(reason){\n      // failure\n    });\n    ```\n  \n    Advanced Example\n    --------------\n  \n    Synchronous Example\n  \n    ```javascript\n    let author, books;\n  \n    try {\n      author = findAuthor();\n      books  = findBooksByAuthor(author);\n      // success\n    } catch(reason) {\n      // failure\n    }\n    ```\n  \n    Errback Example\n  \n    ```js\n  \n    function foundBooks(books) {\n  \n    }\n  \n    function failure(reason) {\n  \n    }\n  \n    findAuthor(function(author, err){\n      if (err) {\n        failure(err);\n        // failure\n      } else {\n        try {\n          findBoooksByAuthor(author, function(books, err) {\n            if (err) {\n              failure(err);\n            } else {\n              try {\n                foundBooks(books);\n              } catch(reason) {\n                failure(reason);\n              }\n            }\n          });\n        } catch(error) {\n          failure(err);\n        }\n        // success\n      }\n    });\n    ```\n  \n    Promise Example;\n  \n    ```javascript\n    findAuthor().\n      then(findBooksByAuthor).\n      then(function(books){\n        // found books\n    }).catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method then\n    @param {Function} onFulfilled\n    @param {Function} onRejected\n    Useful for tooling.\n    @return {Promise}\n  */\n  then: then,\n\n  /**\n    `catch` is simply sugar for `then(undefined, onRejection)` which makes it the same\n    as the catch block of a try/catch statement.\n  \n    ```js\n    function findAuthor(){\n      throw new Error('couldn't find that author');\n    }\n  \n    // synchronous\n    try {\n      findAuthor();\n    } catch(reason) {\n      // something went wrong\n    }\n  \n    // async with promises\n    findAuthor().catch(function(reason){\n      // something went wrong\n    });\n    ```\n  \n    @method catch\n    @param {Function} onRejection\n    Useful for tooling.\n    @return {Promise}\n  */\n  'catch': function _catch(onRejection) {\n    return this.then(null, onRejection);\n  }\n};\n\nfunction polyfill() {\n    var local = undefined;\n\n    if (typeof global !== 'undefined') {\n        local = global;\n    } else if (typeof self !== 'undefined') {\n        local = self;\n    } else {\n        try {\n            local = Function('return this')();\n        } catch (e) {\n            throw new Error('polyfill failed because global object is unavailable in this environment');\n        }\n    }\n\n    var P = local.Promise;\n\n    if (P) {\n        var promiseToString = null;\n        try {\n            promiseToString = Object.prototype.toString.call(P.resolve());\n        } catch (e) {\n            // silently ignored\n        }\n\n        if (promiseToString === '[object Promise]' && !P.cast) {\n            return;\n        }\n    }\n\n    local.Promise = Promise;\n}\n\npolyfill();\n// Strange compat..\nPromise.polyfill = polyfill;\nPromise.Promise = Promise;\n\nreturn Promise;\n\n})));\n//# sourceMappingURL=es6-promise.map", "/*! JSON v3.3.2 | http://bestiejs.github.io/json3 | Copyright 2012-2014, Kit Cambridge | http://kit.mit-license.org */\n;(function () {\n  // Detect the `define` function exposed by asynchronous module loaders. The\n  // strict `define` check is necessary for compatibility with `r.js`.\n  var isLoader = typeof define === \"function\" && define.amd;\n\n  // A set of types used to distinguish objects from primitives.\n  var objectTypes = {\n    \"function\": true,\n    \"object\": true\n  };\n\n  // Detect the `exports` object exposed by CommonJS implementations.\n  var freeExports = objectTypes[typeof exports] && exports && !exports.nodeType && exports;\n\n  // Use the `global` object exposed by Node (including Browserify via\n  // `insert-module-globals`), <PERSON>rw<PERSON>, and <PERSON><PERSON> as the default context,\n  // and the `window` object in browsers. Rhino exports a `global` function\n  // instead.\n  var root = objectTypes[typeof window] && window || this,\n      freeGlobal = freeExports && objectTypes[typeof module] && module && !module.nodeType && typeof global == \"object\" && global;\n\n  if (freeGlobal && (freeGlobal[\"global\"] === freeGlobal || freeGlobal[\"window\"] === freeGlobal || freeGlobal[\"self\"] === freeGlobal)) {\n    root = freeGlobal;\n  }\n\n  // Public: Initializes JSON 3 using the given `context` object, attaching the\n  // `stringify` and `parse` functions to the specified `exports` object.\n  function runInContext(context, exports) {\n    context || (context = root[\"Object\"]());\n    exports || (exports = root[\"Object\"]());\n\n    // Native constructor aliases.\n    var Number = context[\"Number\"] || root[\"Number\"],\n        String = context[\"String\"] || root[\"String\"],\n        Object = context[\"Object\"] || root[\"Object\"],\n        Date = context[\"Date\"] || root[\"Date\"],\n        SyntaxError = context[\"SyntaxError\"] || root[\"SyntaxError\"],\n        TypeError = context[\"TypeError\"] || root[\"TypeError\"],\n        Math = context[\"Math\"] || root[\"Math\"],\n        nativeJSON = context[\"JSON\"] || root[\"JSON\"];\n\n    // Delegate to the native `stringify` and `parse` implementations.\n    if (typeof nativeJSON == \"object\" && nativeJSON) {\n      exports.stringify = nativeJSON.stringify;\n      exports.parse = nativeJSON.parse;\n    }\n\n    // Convenience aliases.\n    var objectProto = Object.prototype,\n        getClass = objectProto.toString,\n        isProperty, forEach, undef;\n\n    // Test the `Date#getUTC*` methods. Based on work by @Yaffle.\n    var isExtended = new Date(-3509827334573292);\n    try {\n      // The `getUTCFullYear`, `Month`, and `Date` methods return nonsensical\n      // results for certain dates in Opera >= 10.53.\n      isExtended = isExtended.getUTCFullYear() == -109252 && isExtended.getUTCMonth() === 0 && isExtended.getUTCDate() === 1 &&\n        // Safari < 2.0.2 stores the internal millisecond time value correctly,\n        // but clips the values returned by the date methods to the range of\n        // signed 32-bit integers ([-2 ** 31, 2 ** 31 - 1]).\n        isExtended.getUTCHours() == 10 && isExtended.getUTCMinutes() == 37 && isExtended.getUTCSeconds() == 6 && isExtended.getUTCMilliseconds() == 708;\n    } catch (exception) {}\n\n    // Internal: Determines whether the native `JSON.stringify` and `parse`\n    // implementations are spec-compliant. Based on work by Ken Snyder.\n    function has(name) {\n      if (has[name] !== undef) {\n        // Return cached feature test result.\n        return has[name];\n      }\n      var isSupported;\n      if (name == \"bug-string-char-index\") {\n        // IE <= 7 doesn't support accessing string characters using square\n        // bracket notation. IE 8 only supports this for primitives.\n        isSupported = \"a\"[0] != \"a\";\n      } else if (name == \"json\") {\n        // Indicates whether both `JSON.stringify` and `JSON.parse` are\n        // supported.\n        isSupported = has(\"json-stringify\") && has(\"json-parse\");\n      } else {\n        var value, serialized = '{\"a\":[1,true,false,null,\"\\\\u0000\\\\b\\\\n\\\\f\\\\r\\\\t\"]}';\n        // Test `JSON.stringify`.\n        if (name == \"json-stringify\") {\n          var stringify = exports.stringify, stringifySupported = typeof stringify == \"function\" && isExtended;\n          if (stringifySupported) {\n            // A test function object with a custom `toJSON` method.\n            (value = function () {\n              return 1;\n            }).toJSON = value;\n            try {\n              stringifySupported =\n                // Firefox 3.1b1 and b2 serialize string, number, and boolean\n                // primitives as object literals.\n                stringify(0) === \"0\" &&\n                // FF 3.1b1, b2, and JSON 2 serialize wrapped primitives as object\n                // literals.\n                stringify(new Number()) === \"0\" &&\n                stringify(new String()) == '\"\"' &&\n                // FF 3.1b1, 2 throw an error if the value is `null`, `undefined`, or\n                // does not define a canonical JSON representation (this applies to\n                // objects with `toJSON` properties as well, *unless* they are nested\n                // within an object or array).\n                stringify(getClass) === undef &&\n                // IE 8 serializes `undefined` as `\"undefined\"`. Safari <= 5.1.7 and\n                // FF 3.1b3 pass this test.\n                stringify(undef) === undef &&\n                // Safari <= 5.1.7 and FF 3.1b3 throw `Error`s and `TypeError`s,\n                // respectively, if the value is omitted entirely.\n                stringify() === undef &&\n                // FF 3.1b1, 2 throw an error if the given value is not a number,\n                // string, array, object, Boolean, or `null` literal. This applies to\n                // objects with custom `toJSON` methods as well, unless they are nested\n                // inside object or array literals. YUI 3.0.0b1 ignores custom `toJSON`\n                // methods entirely.\n                stringify(value) === \"1\" &&\n                stringify([value]) == \"[1]\" &&\n                // Prototype <= 1.6.1 serializes `[undefined]` as `\"[]\"` instead of\n                // `\"[null]\"`.\n                stringify([undef]) == \"[null]\" &&\n                // YUI 3.0.0b1 fails to serialize `null` literals.\n                stringify(null) == \"null\" &&\n                // FF 3.1b1, 2 halts serialization if an array contains a function:\n                // `[1, true, getClass, 1]` serializes as \"[1,true,],\". FF 3.1b3\n                // elides non-JSON values from objects and arrays, unless they\n                // define custom `toJSON` methods.\n                stringify([undef, getClass, null]) == \"[null,null,null]\" &&\n                // Simple serialization test. FF 3.1b1 uses Unicode escape sequences\n                // where character escape codes are expected (e.g., `\\b` => `\\u0008`).\n                stringify({ \"a\": [value, true, false, null, \"\\x00\\b\\n\\f\\r\\t\"] }) == serialized &&\n                // FF 3.1b1 and b2 ignore the `filter` and `width` arguments.\n                stringify(null, value) === \"1\" &&\n                stringify([1, 2], null, 1) == \"[\\n 1,\\n 2\\n]\" &&\n                // JSON 2, Prototype <= 1.7, and older WebKit builds incorrectly\n                // serialize extended years.\n                stringify(new Date(-8.64e15)) == '\"-271821-04-20T00:00:00.000Z\"' &&\n                // The milliseconds are optional in ES 5, but required in 5.1.\n                stringify(new Date(8.64e15)) == '\"+275760-09-13T00:00:00.000Z\"' &&\n                // Firefox <= 11.0 incorrectly serializes years prior to 0 as negative\n                // four-digit years instead of six-digit years. Credits: @Yaffle.\n                stringify(new Date(-621987552e5)) == '\"-000001-01-01T00:00:00.000Z\"' &&\n                // Safari <= 5.1.5 and Opera >= 10.53 incorrectly serialize millisecond\n                // values less than 1000. Credits: @Yaffle.\n                stringify(new Date(-1)) == '\"1969-12-31T23:59:59.999Z\"';\n            } catch (exception) {\n              stringifySupported = false;\n            }\n          }\n          isSupported = stringifySupported;\n        }\n        // Test `JSON.parse`.\n        if (name == \"json-parse\") {\n          var parse = exports.parse;\n          if (typeof parse == \"function\") {\n            try {\n              // FF 3.1b1, b2 will throw an exception if a bare literal is provided.\n              // Conforming implementations should also coerce the initial argument to\n              // a string prior to parsing.\n              if (parse(\"0\") === 0 && !parse(false)) {\n                // Simple parsing test.\n                value = parse(serialized);\n                var parseSupported = value[\"a\"].length == 5 && value[\"a\"][0] === 1;\n                if (parseSupported) {\n                  try {\n                    // Safari <= 5.1.2 and FF 3.1b1 allow unescaped tabs in strings.\n                    parseSupported = !parse('\"\\t\"');\n                  } catch (exception) {}\n                  if (parseSupported) {\n                    try {\n                      // FF 4.0 and 4.0.1 allow leading `+` signs and leading\n                      // decimal points. FF 4.0, 4.0.1, and IE 9-10 also allow\n                      // certain octal literals.\n                      parseSupported = parse(\"01\") !== 1;\n                    } catch (exception) {}\n                  }\n                  if (parseSupported) {\n                    try {\n                      // FF 4.0, 4.0.1, and Rhino 1.7R3-R4 allow trailing decimal\n                      // points. These environments, along with FF 3.1b1 and 2,\n                      // also allow trailing commas in JSON objects and arrays.\n                      parseSupported = parse(\"1.\") !== 1;\n                    } catch (exception) {}\n                  }\n                }\n              }\n            } catch (exception) {\n              parseSupported = false;\n            }\n          }\n          isSupported = parseSupported;\n        }\n      }\n      return has[name] = !!isSupported;\n    }\n\n    if (!has(\"json\")) {\n      // Common `[[Class]]` name aliases.\n      var functionClass = \"[object Function]\",\n          dateClass = \"[object Date]\",\n          numberClass = \"[object Number]\",\n          stringClass = \"[object String]\",\n          arrayClass = \"[object Array]\",\n          booleanClass = \"[object Boolean]\";\n\n      // Detect incomplete support for accessing string characters by index.\n      var charIndexBuggy = has(\"bug-string-char-index\");\n\n      // Define additional utility methods if the `Date` methods are buggy.\n      if (!isExtended) {\n        var floor = Math.floor;\n        // A mapping between the months of the year and the number of days between\n        // January 1st and the first of the respective month.\n        var Months = [0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334];\n        // Internal: Calculates the number of days between the Unix epoch and the\n        // first day of the given month.\n        var getDay = function (year, month) {\n          return Months[month] + 365 * (year - 1970) + floor((year - 1969 + (month = +(month > 1))) / 4) - floor((year - 1901 + month) / 100) + floor((year - 1601 + month) / 400);\n        };\n      }\n\n      // Internal: Determines if a property is a direct property of the given\n      // object. Delegates to the native `Object#hasOwnProperty` method.\n      if (!(isProperty = objectProto.hasOwnProperty)) {\n        isProperty = function (property) {\n          var members = {}, constructor;\n          if ((members.__proto__ = null, members.__proto__ = {\n            // The *proto* property cannot be set multiple times in recent\n            // versions of Firefox and SeaMonkey.\n            \"toString\": 1\n          }, members).toString != getClass) {\n            // Safari <= 2.0.3 doesn't implement `Object#hasOwnProperty`, but\n            // supports the mutable *proto* property.\n            isProperty = function (property) {\n              // Capture and break the object's prototype chain (see section 8.6.2\n              // of the ES 5.1 spec). The parenthesized expression prevents an\n              // unsafe transformation by the Closure Compiler.\n              var original = this.__proto__, result = property in (this.__proto__ = null, this);\n              // Restore the original prototype chain.\n              this.__proto__ = original;\n              return result;\n            };\n          } else {\n            // Capture a reference to the top-level `Object` constructor.\n            constructor = members.constructor;\n            // Use the `constructor` property to simulate `Object#hasOwnProperty` in\n            // other environments.\n            isProperty = function (property) {\n              var parent = (this.constructor || constructor).prototype;\n              return property in this && !(property in parent && this[property] === parent[property]);\n            };\n          }\n          members = null;\n          return isProperty.call(this, property);\n        };\n      }\n\n      // Internal: Normalizes the `for...in` iteration algorithm across\n      // environments. Each enumerated key is yielded to a `callback` function.\n      forEach = function (object, callback) {\n        var size = 0, Properties, members, property;\n\n        // Tests for bugs in the current environment's `for...in` algorithm. The\n        // `valueOf` property inherits the non-enumerable flag from\n        // `Object.prototype` in older versions of IE, Netscape, and Mozilla.\n        (Properties = function () {\n          this.valueOf = 0;\n        }).prototype.valueOf = 0;\n\n        // Iterate over a new instance of the `Properties` class.\n        members = new Properties();\n        for (property in members) {\n          // Ignore all properties inherited from `Object.prototype`.\n          if (isProperty.call(members, property)) {\n            size++;\n          }\n        }\n        Properties = members = null;\n\n        // Normalize the iteration algorithm.\n        if (!size) {\n          // A list of non-enumerable properties inherited from `Object.prototype`.\n          members = [\"valueOf\", \"toString\", \"toLocaleString\", \"propertyIsEnumerable\", \"isPrototypeOf\", \"hasOwnProperty\", \"constructor\"];\n          // IE <= 8, Mozilla 1.0, and Netscape 6.2 ignore shadowed non-enumerable\n          // properties.\n          forEach = function (object, callback) {\n            var isFunction = getClass.call(object) == functionClass, property, length;\n            var hasProperty = !isFunction && typeof object.constructor != \"function\" && objectTypes[typeof object.hasOwnProperty] && object.hasOwnProperty || isProperty;\n            for (property in object) {\n              // Gecko <= 1.0 enumerates the `prototype` property of functions under\n              // certain conditions; IE does not.\n              if (!(isFunction && property == \"prototype\") && hasProperty.call(object, property)) {\n                callback(property);\n              }\n            }\n            // Manually invoke the callback for each non-enumerable property.\n            for (length = members.length; property = members[--length]; hasProperty.call(object, property) && callback(property));\n          };\n        } else if (size == 2) {\n          // Safari <= 2.0.4 enumerates shadowed properties twice.\n          forEach = function (object, callback) {\n            // Create a set of iterated properties.\n            var members = {}, isFunction = getClass.call(object) == functionClass, property;\n            for (property in object) {\n              // Store each property name to prevent double enumeration. The\n              // `prototype` property of functions is not enumerated due to cross-\n              // environment inconsistencies.\n              if (!(isFunction && property == \"prototype\") && !isProperty.call(members, property) && (members[property] = 1) && isProperty.call(object, property)) {\n                callback(property);\n              }\n            }\n          };\n        } else {\n          // No bugs detected; use the standard `for...in` algorithm.\n          forEach = function (object, callback) {\n            var isFunction = getClass.call(object) == functionClass, property, isConstructor;\n            for (property in object) {\n              if (!(isFunction && property == \"prototype\") && isProperty.call(object, property) && !(isConstructor = property === \"constructor\")) {\n                callback(property);\n              }\n            }\n            // Manually invoke the callback for the `constructor` property due to\n            // cross-environment inconsistencies.\n            if (isConstructor || isProperty.call(object, (property = \"constructor\"))) {\n              callback(property);\n            }\n          };\n        }\n        return forEach(object, callback);\n      };\n\n      // Public: Serializes a JavaScript `value` as a JSON string. The optional\n      // `filter` argument may specify either a function that alters how object and\n      // array members are serialized, or an array of strings and numbers that\n      // indicates which properties should be serialized. The optional `width`\n      // argument may be either a string or number that specifies the indentation\n      // level of the output.\n      if (!has(\"json-stringify\")) {\n        // Internal: A map of control characters and their escaped equivalents.\n        var Escapes = {\n          92: \"\\\\\\\\\",\n          34: '\\\\\"',\n          8: \"\\\\b\",\n          12: \"\\\\f\",\n          10: \"\\\\n\",\n          13: \"\\\\r\",\n          9: \"\\\\t\"\n        };\n\n        // Internal: Converts `value` into a zero-padded string such that its\n        // length is at least equal to `width`. The `width` must be <= 6.\n        var leadingZeroes = \"000000\";\n        var toPaddedString = function (width, value) {\n          // The `|| 0` expression is necessary to work around a bug in\n          // Opera <= 7.54u2 where `0 == -0`, but `String(-0) !== \"0\"`.\n          return (leadingZeroes + (value || 0)).slice(-width);\n        };\n\n        // Internal: Double-quotes a string `value`, replacing all ASCII control\n        // characters (characters with code unit values between 0 and 31) with\n        // their escaped equivalents. This is an implementation of the\n        // `Quote(value)` operation defined in ES 5.1 section 15.12.3.\n        var unicodePrefix = \"\\\\u00\";\n        var quote = function (value) {\n          var result = '\"', index = 0, length = value.length, useCharIndex = !charIndexBuggy || length > 10;\n          var symbols = useCharIndex && (charIndexBuggy ? value.split(\"\") : value);\n          for (; index < length; index++) {\n            var charCode = value.charCodeAt(index);\n            // If the character is a control character, append its Unicode or\n            // shorthand escape sequence; otherwise, append the character as-is.\n            switch (charCode) {\n              case 8: case 9: case 10: case 12: case 13: case 34: case 92:\n                result += Escapes[charCode];\n                break;\n              default:\n                if (charCode < 32) {\n                  result += unicodePrefix + toPaddedString(2, charCode.toString(16));\n                  break;\n                }\n                result += useCharIndex ? symbols[index] : value.charAt(index);\n            }\n          }\n          return result + '\"';\n        };\n\n        // Internal: Recursively serializes an object. Implements the\n        // `Str(key, holder)`, `JO(value)`, and `JA(value)` operations.\n        var serialize = function (property, object, callback, properties, whitespace, indentation, stack) {\n          var value, className, year, month, date, time, hours, minutes, seconds, milliseconds, results, element, index, length, prefix, result;\n          try {\n            // Necessary for host object support.\n            value = object[property];\n          } catch (exception) {}\n          if (typeof value == \"object\" && value) {\n            className = getClass.call(value);\n            if (className == dateClass && !isProperty.call(value, \"toJSON\")) {\n              if (value > -1 / 0 && value < 1 / 0) {\n                // Dates are serialized according to the `Date#toJSON` method\n                // specified in ES 5.1 section *********. See section *********\n                // for the ISO 8601 date time string format.\n                if (getDay) {\n                  // Manually compute the year, month, date, hours, minutes,\n                  // seconds, and milliseconds if the `getUTC*` methods are\n                  // buggy. Adapted from @Yaffle's `date-shim` project.\n                  date = floor(value / 864e5);\n                  for (year = floor(date / 365.2425) + 1970 - 1; getDay(year + 1, 0) <= date; year++);\n                  for (month = floor((date - getDay(year, 0)) / 30.42); getDay(year, month + 1) <= date; month++);\n                  date = 1 + date - getDay(year, month);\n                  // The `time` value specifies the time within the day (see ES\n                  // 5.1 section 15.9.1.2). The formula `(A % B + B) % B` is used\n                  // to compute `A modulo B`, as the `%` operator does not\n                  // correspond to the `modulo` operation for negative numbers.\n                  time = (value % 864e5 + 864e5) % 864e5;\n                  // The hours, minutes, seconds, and milliseconds are obtained by\n                  // decomposing the time within the day. See section 15.9.1.10.\n                  hours = floor(time / 36e5) % 24;\n                  minutes = floor(time / 6e4) % 60;\n                  seconds = floor(time / 1e3) % 60;\n                  milliseconds = time % 1e3;\n                } else {\n                  year = value.getUTCFullYear();\n                  month = value.getUTCMonth();\n                  date = value.getUTCDate();\n                  hours = value.getUTCHours();\n                  minutes = value.getUTCMinutes();\n                  seconds = value.getUTCSeconds();\n                  milliseconds = value.getUTCMilliseconds();\n                }\n                // Serialize extended years correctly.\n                value = (year <= 0 || year >= 1e4 ? (year < 0 ? \"-\" : \"+\") + toPaddedString(6, year < 0 ? -year : year) : toPaddedString(4, year)) +\n                  \"-\" + toPaddedString(2, month + 1) + \"-\" + toPaddedString(2, date) +\n                  // Months, dates, hours, minutes, and seconds should have two\n                  // digits; milliseconds should have three.\n                  \"T\" + toPaddedString(2, hours) + \":\" + toPaddedString(2, minutes) + \":\" + toPaddedString(2, seconds) +\n                  // Milliseconds are optional in ES 5.0, but required in 5.1.\n                  \".\" + toPaddedString(3, milliseconds) + \"Z\";\n              } else {\n                value = null;\n              }\n            } else if (typeof value.toJSON == \"function\" && ((className != numberClass && className != stringClass && className != arrayClass) || isProperty.call(value, \"toJSON\"))) {\n              // Prototype <= 1.6.1 adds non-standard `toJSON` methods to the\n              // `Number`, `String`, `Date`, and `Array` prototypes. JSON 3\n              // ignores all `toJSON` methods on these objects unless they are\n              // defined directly on an instance.\n              value = value.toJSON(property);\n            }\n          }\n          if (callback) {\n            // If a replacement function was provided, call it to obtain the value\n            // for serialization.\n            value = callback.call(object, property, value);\n          }\n          if (value === null) {\n            return \"null\";\n          }\n          className = getClass.call(value);\n          if (className == booleanClass) {\n            // Booleans are represented literally.\n            return \"\" + value;\n          } else if (className == numberClass) {\n            // JSON numbers must be finite. `Infinity` and `NaN` are serialized as\n            // `\"null\"`.\n            return value > -1 / 0 && value < 1 / 0 ? \"\" + value : \"null\";\n          } else if (className == stringClass) {\n            // Strings are double-quoted and escaped.\n            return quote(\"\" + value);\n          }\n          // Recursively serialize objects and arrays.\n          if (typeof value == \"object\") {\n            // Check for cyclic structures. This is a linear search; performance\n            // is inversely proportional to the number of unique nested objects.\n            for (length = stack.length; length--;) {\n              if (stack[length] === value) {\n                // Cyclic structures cannot be serialized by `JSON.stringify`.\n                throw TypeError();\n              }\n            }\n            // Add the object to the stack of traversed objects.\n            stack.push(value);\n            results = [];\n            // Save the current indentation level and indent one additional level.\n            prefix = indentation;\n            indentation += whitespace;\n            if (className == arrayClass) {\n              // Recursively serialize array elements.\n              for (index = 0, length = value.length; index < length; index++) {\n                element = serialize(index, value, callback, properties, whitespace, indentation, stack);\n                results.push(element === undef ? \"null\" : element);\n              }\n              result = results.length ? (whitespace ? \"[\\n\" + indentation + results.join(\",\\n\" + indentation) + \"\\n\" + prefix + \"]\" : (\"[\" + results.join(\",\") + \"]\")) : \"[]\";\n            } else {\n              // Recursively serialize object members. Members are selected from\n              // either a user-specified list of property names, or the object\n              // itself.\n              forEach(properties || value, function (property) {\n                var element = serialize(property, value, callback, properties, whitespace, indentation, stack);\n                if (element !== undef) {\n                  // According to ES 5.1 section 15.12.3: \"If `gap` {whitespace}\n                  // is not the empty string, let `member` {quote(property) + \":\"}\n                  // be the concatenation of `member` and the `space` character.\"\n                  // The \"`space` character\" refers to the literal space\n                  // character, not the `space` {width} argument provided to\n                  // `JSON.stringify`.\n                  results.push(quote(property) + \":\" + (whitespace ? \" \" : \"\") + element);\n                }\n              });\n              result = results.length ? (whitespace ? \"{\\n\" + indentation + results.join(\",\\n\" + indentation) + \"\\n\" + prefix + \"}\" : (\"{\" + results.join(\",\") + \"}\")) : \"{}\";\n            }\n            // Remove the object from the traversed object stack.\n            stack.pop();\n            return result;\n          }\n        };\n\n        // Public: `JSON.stringify`. See ES 5.1 section 15.12.3.\n        exports.stringify = function (source, filter, width) {\n          var whitespace, callback, properties, className;\n          if (objectTypes[typeof filter] && filter) {\n            if ((className = getClass.call(filter)) == functionClass) {\n              callback = filter;\n            } else if (className == arrayClass) {\n              // Convert the property names array into a makeshift set.\n              properties = {};\n              for (var index = 0, length = filter.length, value; index < length; value = filter[index++], ((className = getClass.call(value)), className == stringClass || className == numberClass) && (properties[value] = 1));\n            }\n          }\n          if (width) {\n            if ((className = getClass.call(width)) == numberClass) {\n              // Convert the `width` to an integer and create a string containing\n              // `width` number of space characters.\n              if ((width -= width % 1) > 0) {\n                for (whitespace = \"\", width > 10 && (width = 10); whitespace.length < width; whitespace += \" \");\n              }\n            } else if (className == stringClass) {\n              whitespace = width.length <= 10 ? width : width.slice(0, 10);\n            }\n          }\n          // Opera <= 7.54u2 discards the values associated with empty string keys\n          // (`\"\"`) only if they are used directly within an object member list\n          // (e.g., `!(\"\" in { \"\": 1})`).\n          return serialize(\"\", (value = {}, value[\"\"] = source, value), callback, properties, whitespace, \"\", []);\n        };\n      }\n\n      // Public: Parses a JSON source string.\n      if (!has(\"json-parse\")) {\n        var fromCharCode = String.fromCharCode;\n\n        // Internal: A map of escaped control characters and their unescaped\n        // equivalents.\n        var Unescapes = {\n          92: \"\\\\\",\n          34: '\"',\n          47: \"/\",\n          98: \"\\b\",\n          116: \"\\t\",\n          110: \"\\n\",\n          102: \"\\f\",\n          114: \"\\r\"\n        };\n\n        // Internal: Stores the parser state.\n        var Index, Source;\n\n        // Internal: Resets the parser state and throws a `SyntaxError`.\n        var abort = function () {\n          Index = Source = null;\n          throw SyntaxError();\n        };\n\n        // Internal: Returns the next token, or `\"$\"` if the parser has reached\n        // the end of the source string. A token may be a string, number, `null`\n        // literal, or Boolean literal.\n        var lex = function () {\n          var source = Source, length = source.length, value, begin, position, isSigned, charCode;\n          while (Index < length) {\n            charCode = source.charCodeAt(Index);\n            switch (charCode) {\n              case 9: case 10: case 13: case 32:\n                // Skip whitespace tokens, including tabs, carriage returns, line\n                // feeds, and space characters.\n                Index++;\n                break;\n              case 123: case 125: case 91: case 93: case 58: case 44:\n                // Parse a punctuator token (`{`, `}`, `[`, `]`, `:`, or `,`) at\n                // the current position.\n                value = charIndexBuggy ? source.charAt(Index) : source[Index];\n                Index++;\n                return value;\n              case 34:\n                // `\"` delimits a JSON string; advance to the next character and\n                // begin parsing the string. String tokens are prefixed with the\n                // sentinel `@` character to distinguish them from punctuators and\n                // end-of-string tokens.\n                for (value = \"@\", Index++; Index < length;) {\n                  charCode = source.charCodeAt(Index);\n                  if (charCode < 32) {\n                    // Unescaped ASCII control characters (those with a code unit\n                    // less than the space character) are not permitted.\n                    abort();\n                  } else if (charCode == 92) {\n                    // A reverse solidus (`\\`) marks the beginning of an escaped\n                    // control character (including `\"`, `\\`, and `/`) or Unicode\n                    // escape sequence.\n                    charCode = source.charCodeAt(++Index);\n                    switch (charCode) {\n                      case 92: case 34: case 47: case 98: case 116: case 110: case 102: case 114:\n                        // Revive escaped control characters.\n                        value += Unescapes[charCode];\n                        Index++;\n                        break;\n                      case 117:\n                        // `\\u` marks the beginning of a Unicode escape sequence.\n                        // Advance to the first character and validate the\n                        // four-digit code point.\n                        begin = ++Index;\n                        for (position = Index + 4; Index < position; Index++) {\n                          charCode = source.charCodeAt(Index);\n                          // A valid sequence comprises four hexdigits (case-\n                          // insensitive) that form a single hexadecimal value.\n                          if (!(charCode >= 48 && charCode <= 57 || charCode >= 97 && charCode <= 102 || charCode >= 65 && charCode <= 70)) {\n                            // Invalid Unicode escape sequence.\n                            abort();\n                          }\n                        }\n                        // Revive the escaped character.\n                        value += fromCharCode(\"0x\" + source.slice(begin, Index));\n                        break;\n                      default:\n                        // Invalid escape sequence.\n                        abort();\n                    }\n                  } else {\n                    if (charCode == 34) {\n                      // An unescaped double-quote character marks the end of the\n                      // string.\n                      break;\n                    }\n                    charCode = source.charCodeAt(Index);\n                    begin = Index;\n                    // Optimize for the common case where a string is valid.\n                    while (charCode >= 32 && charCode != 92 && charCode != 34) {\n                      charCode = source.charCodeAt(++Index);\n                    }\n                    // Append the string as-is.\n                    value += source.slice(begin, Index);\n                  }\n                }\n                if (source.charCodeAt(Index) == 34) {\n                  // Advance to the next character and return the revived string.\n                  Index++;\n                  return value;\n                }\n                // Unterminated string.\n                abort();\n              default:\n                // Parse numbers and literals.\n                begin = Index;\n                // Advance past the negative sign, if one is specified.\n                if (charCode == 45) {\n                  isSigned = true;\n                  charCode = source.charCodeAt(++Index);\n                }\n                // Parse an integer or floating-point value.\n                if (charCode >= 48 && charCode <= 57) {\n                  // Leading zeroes are interpreted as octal literals.\n                  if (charCode == 48 && ((charCode = source.charCodeAt(Index + 1)), charCode >= 48 && charCode <= 57)) {\n                    // Illegal octal literal.\n                    abort();\n                  }\n                  isSigned = false;\n                  // Parse the integer component.\n                  for (; Index < length && ((charCode = source.charCodeAt(Index)), charCode >= 48 && charCode <= 57); Index++);\n                  // Floats cannot contain a leading decimal point; however, this\n                  // case is already accounted for by the parser.\n                  if (source.charCodeAt(Index) == 46) {\n                    position = ++Index;\n                    // Parse the decimal component.\n                    for (; position < length && ((charCode = source.charCodeAt(position)), charCode >= 48 && charCode <= 57); position++);\n                    if (position == Index) {\n                      // Illegal trailing decimal.\n                      abort();\n                    }\n                    Index = position;\n                  }\n                  // Parse exponents. The `e` denoting the exponent is\n                  // case-insensitive.\n                  charCode = source.charCodeAt(Index);\n                  if (charCode == 101 || charCode == 69) {\n                    charCode = source.charCodeAt(++Index);\n                    // Skip past the sign following the exponent, if one is\n                    // specified.\n                    if (charCode == 43 || charCode == 45) {\n                      Index++;\n                    }\n                    // Parse the exponential component.\n                    for (position = Index; position < length && ((charCode = source.charCodeAt(position)), charCode >= 48 && charCode <= 57); position++);\n                    if (position == Index) {\n                      // Illegal empty exponent.\n                      abort();\n                    }\n                    Index = position;\n                  }\n                  // Coerce the parsed value to a JavaScript number.\n                  return +source.slice(begin, Index);\n                }\n                // A negative sign may only precede numbers.\n                if (isSigned) {\n                  abort();\n                }\n                // `true`, `false`, and `null` literals.\n                if (source.slice(Index, Index + 4) == \"true\") {\n                  Index += 4;\n                  return true;\n                } else if (source.slice(Index, Index + 5) == \"false\") {\n                  Index += 5;\n                  return false;\n                } else if (source.slice(Index, Index + 4) == \"null\") {\n                  Index += 4;\n                  return null;\n                }\n                // Unrecognized token.\n                abort();\n            }\n          }\n          // Return the sentinel `$` character if the parser has reached the end\n          // of the source string.\n          return \"$\";\n        };\n\n        // Internal: Parses a JSON `value` token.\n        var get = function (value) {\n          var results, hasMembers;\n          if (value == \"$\") {\n            // Unexpected end of input.\n            abort();\n          }\n          if (typeof value == \"string\") {\n            if ((charIndexBuggy ? value.charAt(0) : value[0]) == \"@\") {\n              // Remove the sentinel `@` character.\n              return value.slice(1);\n            }\n            // Parse object and array literals.\n            if (value == \"[\") {\n              // Parses a JSON array, returning a new JavaScript array.\n              results = [];\n              for (;; hasMembers || (hasMembers = true)) {\n                value = lex();\n                // A closing square bracket marks the end of the array literal.\n                if (value == \"]\") {\n                  break;\n                }\n                // If the array literal contains elements, the current token\n                // should be a comma separating the previous element from the\n                // next.\n                if (hasMembers) {\n                  if (value == \",\") {\n                    value = lex();\n                    if (value == \"]\") {\n                      // Unexpected trailing `,` in array literal.\n                      abort();\n                    }\n                  } else {\n                    // A `,` must separate each array element.\n                    abort();\n                  }\n                }\n                // Elisions and leading commas are not permitted.\n                if (value == \",\") {\n                  abort();\n                }\n                results.push(get(value));\n              }\n              return results;\n            } else if (value == \"{\") {\n              // Parses a JSON object, returning a new JavaScript object.\n              results = {};\n              for (;; hasMembers || (hasMembers = true)) {\n                value = lex();\n                // A closing curly brace marks the end of the object literal.\n                if (value == \"}\") {\n                  break;\n                }\n                // If the object literal contains members, the current token\n                // should be a comma separator.\n                if (hasMembers) {\n                  if (value == \",\") {\n                    value = lex();\n                    if (value == \"}\") {\n                      // Unexpected trailing `,` in object literal.\n                      abort();\n                    }\n                  } else {\n                    // A `,` must separate each object member.\n                    abort();\n                  }\n                }\n                // Leading commas are not permitted, object property names must be\n                // double-quoted strings, and a `:` must separate each property\n                // name and value.\n                if (value == \",\" || typeof value != \"string\" || (charIndexBuggy ? value.charAt(0) : value[0]) != \"@\" || lex() != \":\") {\n                  abort();\n                }\n                results[value.slice(1)] = get(lex());\n              }\n              return results;\n            }\n            // Unexpected token encountered.\n            abort();\n          }\n          return value;\n        };\n\n        // Internal: Updates a traversed object member.\n        var update = function (source, property, callback) {\n          var element = walk(source, property, callback);\n          if (element === undef) {\n            delete source[property];\n          } else {\n            source[property] = element;\n          }\n        };\n\n        // Internal: Recursively traverses a parsed JSON object, invoking the\n        // `callback` function for each value. This is an implementation of the\n        // `Walk(holder, name)` operation defined in ES 5.1 section 15.12.2.\n        var walk = function (source, property, callback) {\n          var value = source[property], length;\n          if (typeof value == \"object\" && value) {\n            // `forEach` can't be used to traverse an array in Opera <= 8.54\n            // because its `Object#hasOwnProperty` implementation returns `false`\n            // for array indices (e.g., `![1, 2, 3].hasOwnProperty(\"0\")`).\n            if (getClass.call(value) == arrayClass) {\n              for (length = value.length; length--;) {\n                update(value, length, callback);\n              }\n            } else {\n              forEach(value, function (property) {\n                update(value, property, callback);\n              });\n            }\n          }\n          return callback.call(source, property, value);\n        };\n\n        // Public: `JSON.parse`. See ES 5.1 section 15.12.2.\n        exports.parse = function (source, callback) {\n          var result, value;\n          Index = 0;\n          Source = \"\" + source;\n          result = get(lex());\n          // If a JSON string contains multiple tokens, it is invalid.\n          if (lex() != \"$\") {\n            abort();\n          }\n          // Reset the parser state.\n          Index = Source = null;\n          return callback && getClass.call(callback) == functionClass ? walk((value = {}, value[\"\"] = result, value), \"\", callback) : result;\n        };\n      }\n    }\n\n    exports[\"runInContext\"] = runInContext;\n    return exports;\n  }\n\n  if (freeExports && !isLoader) {\n    // Export for CommonJS environments.\n    runInContext(root, freeExports);\n  } else {\n    // Export for web browsers and JavaScript engines.\n    var nativeJSON = root.JSON,\n        previousJSON = root[\"JSON3\"],\n        isRestored = false;\n\n    var JSON3 = runInContext(root, (root[\"JSON3\"] = {\n      // Public: Restores the original value of the global `JSON` object and\n      // returns a reference to the `JSON3` object.\n      \"noConflict\": function () {\n        if (!isRestored) {\n          isRestored = true;\n          root.JSON = nativeJSON;\n          root[\"JSON3\"] = previousJSON;\n          nativeJSON = previousJSON = null;\n        }\n        return JSON3;\n      }\n    }));\n\n    root.JSON = {\n      \"parse\": JSON3.parse,\n      \"stringify\": JSON3.stringify\n    };\n  }\n\n  // Export for asynchronous module loaders.\n  if (isLoader) {\n    define(function () {\n      return JSON3;\n    });\n  }\n}).call(this);\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar has = Object.prototype.hasOwnProperty;\n\n/**\n * A data structure which is a combination of an array and a set. Adding a new\n * member is O(1), testing for membership is O(1), and finding the index of an\n * element is O(1). Removing elements from the set is not supported. Only\n * strings are supported for membership.\n */\nfunction ArraySet() {\n  this._array = [];\n  this._set = Object.create(null);\n}\n\n/**\n * Static method for creating ArraySet instances from an existing array.\n */\nArraySet.fromArray = function ArraySet_fromArray(aArray, aAllowDuplicates) {\n  var set = new ArraySet();\n  for (var i = 0, len = aArray.length; i < len; i++) {\n    set.add(aArray[i], aAllowDuplicates);\n  }\n  return set;\n};\n\n/**\n * Return how many unique items are in this ArraySet. If duplicates have been\n * added, than those do not count towards the size.\n *\n * @returns Number\n */\nArraySet.prototype.size = function ArraySet_size() {\n  return Object.getOwnPropertyNames(this._set).length;\n};\n\n/**\n * Add the given string to this set.\n *\n * @param String aStr\n */\nArraySet.prototype.add = function ArraySet_add(aStr, aAllowDuplicates) {\n  var sStr = util.toSetString(aStr);\n  var isDuplicate = has.call(this._set, sStr);\n  var idx = this._array.length;\n  if (!isDuplicate || aAllowDuplicates) {\n    this._array.push(aStr);\n  }\n  if (!isDuplicate) {\n    this._set[sStr] = idx;\n  }\n};\n\n/**\n * Is the given string a member of this set?\n *\n * @param String aStr\n */\nArraySet.prototype.has = function ArraySet_has(aStr) {\n  var sStr = util.toSetString(aStr);\n  return has.call(this._set, sStr);\n};\n\n/**\n * What is the index of the given string in the array?\n *\n * @param String aStr\n */\nArraySet.prototype.indexOf = function ArraySet_indexOf(aStr) {\n  var sStr = util.toSetString(aStr);\n  if (has.call(this._set, sStr)) {\n    return this._set[sStr];\n  }\n  throw new Error('\"' + aStr + '\" is not in the set.');\n};\n\n/**\n * What is the element at the given index?\n *\n * @param Number aIdx\n */\nArraySet.prototype.at = function ArraySet_at(aIdx) {\n  if (aIdx >= 0 && aIdx < this._array.length) {\n    return this._array[aIdx];\n  }\n  throw new Error('No element indexed by ' + aIdx);\n};\n\n/**\n * Returns the array representation of this set (which has the proper indices\n * indicated by indexOf). Note that this is a copy of the internal array used\n * for storing the members so that no one can mess with internal state.\n */\nArraySet.prototype.toArray = function ArraySet_toArray() {\n  return this._array.slice();\n};\n\nexports.ArraySet = ArraySet;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n *\n * Based on the Base 64 VLQ implementation in Closure Compiler:\n * https://code.google.com/p/closure-compiler/source/browse/trunk/src/com/google/debugging/sourcemap/Base64VLQ.java\n *\n * Copyright 2011 The Closure Compiler Authors. All rights reserved.\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *  * Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above\n *    copyright notice, this list of conditions and the following\n *    disclaimer in the documentation and/or other materials provided\n *    with the distribution.\n *  * Neither the name of Google Inc. nor the names of its\n *    contributors may be used to endorse or promote products derived\n *    from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n * \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar base64 = require('./base64');\n\n// A single base 64 digit can contain 6 bits of data. For the base 64 variable\n// length quantities we use in the source map spec, the first bit is the sign,\n// the next four bits are the actual value, and the 6th bit is the\n// continuation bit. The continuation bit tells us whether there are more\n// digits in this value following this digit.\n//\n//   Continuation\n//   |    Sign\n//   |    |\n//   V    V\n//   101011\n\nvar VLQ_BASE_SHIFT = 5;\n\n// binary: 100000\nvar VLQ_BASE = 1 << VLQ_BASE_SHIFT;\n\n// binary: 011111\nvar VLQ_BASE_MASK = VLQ_BASE - 1;\n\n// binary: 100000\nvar VLQ_CONTINUATION_BIT = VLQ_BASE;\n\n/**\n * Converts from a two-complement value to a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   1 becomes 2 (10 binary), -1 becomes 3 (11 binary)\n *   2 becomes 4 (100 binary), -2 becomes 5 (101 binary)\n */\nfunction toVLQSigned(aValue) {\n  return aValue < 0\n    ? ((-aValue) << 1) + 1\n    : (aValue << 1) + 0;\n}\n\n/**\n * Converts to a two-complement value from a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   2 (10 binary) becomes 1, 3 (11 binary) becomes -1\n *   4 (100 binary) becomes 2, 5 (101 binary) becomes -2\n */\nfunction fromVLQSigned(aValue) {\n  var isNegative = (aValue & 1) === 1;\n  var shifted = aValue >> 1;\n  return isNegative\n    ? -shifted\n    : shifted;\n}\n\n/**\n * Returns the base 64 VLQ encoded value.\n */\nexports.encode = function base64VLQ_encode(aValue) {\n  var encoded = \"\";\n  var digit;\n\n  var vlq = toVLQSigned(aValue);\n\n  do {\n    digit = vlq & VLQ_BASE_MASK;\n    vlq >>>= VLQ_BASE_SHIFT;\n    if (vlq > 0) {\n      // There are still more digits in this value, so we must make sure the\n      // continuation bit is marked.\n      digit |= VLQ_CONTINUATION_BIT;\n    }\n    encoded += base64.encode(digit);\n  } while (vlq > 0);\n\n  return encoded;\n};\n\n/**\n * Decodes the next base 64 VLQ value from the given string and returns the\n * value and the rest of the string via the out parameter.\n */\nexports.decode = function base64VLQ_decode(aStr, aIndex, aOutParam) {\n  var strLen = aStr.length;\n  var result = 0;\n  var shift = 0;\n  var continuation, digit;\n\n  do {\n    if (aIndex >= strLen) {\n      throw new Error(\"Expected more digits in base 64 VLQ value.\");\n    }\n\n    digit = base64.decode(aStr.charCodeAt(aIndex++));\n    if (digit === -1) {\n      throw new Error(\"Invalid base64 digit: \" + aStr.charAt(aIndex - 1));\n    }\n\n    continuation = !!(digit & VLQ_CONTINUATION_BIT);\n    digit &= VLQ_BASE_MASK;\n    result = result + (digit << shift);\n    shift += VLQ_BASE_SHIFT;\n  } while (continuation);\n\n  aOutParam.value = fromVLQSigned(result);\n  aOutParam.rest = aIndex;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar intToCharMap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/'.split('');\n\n/**\n * Encode an integer in the range of 0 to 63 to a single base 64 digit.\n */\nexports.encode = function (number) {\n  if (0 <= number && number < intToCharMap.length) {\n    return intToCharMap[number];\n  }\n  throw new TypeError(\"Must be between 0 and 63: \" + number);\n};\n\n/**\n * Decode a single base 64 character code digit to an integer. Returns -1 on\n * failure.\n */\nexports.decode = function (charCode) {\n  var bigA = 65;     // 'A'\n  var bigZ = 90;     // 'Z'\n\n  var littleA = 97;  // 'a'\n  var littleZ = 122; // 'z'\n\n  var zero = 48;     // '0'\n  var nine = 57;     // '9'\n\n  var plus = 43;     // '+'\n  var slash = 47;    // '/'\n\n  var littleOffset = 26;\n  var numberOffset = 52;\n\n  // 0 - 25: ABCDEFGHIJKLMNOPQRSTUVWXYZ\n  if (bigA <= charCode && charCode <= bigZ) {\n    return (charCode - bigA);\n  }\n\n  // 26 - 51: abcdefghijklmnopqrstuvwxyz\n  if (littleA <= charCode && charCode <= littleZ) {\n    return (charCode - littleA + littleOffset);\n  }\n\n  // 52 - 61: **********\n  if (zero <= charCode && charCode <= nine) {\n    return (charCode - zero + numberOffset);\n  }\n\n  // 62: +\n  if (charCode == plus) {\n    return 62;\n  }\n\n  // 63: /\n  if (charCode == slash) {\n    return 63;\n  }\n\n  // Invalid base64 digit.\n  return -1;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nexports.GREATEST_LOWER_BOUND = 1;\nexports.LEAST_UPPER_BOUND = 2;\n\n/**\n * Recursive implementation of binary search.\n *\n * @param aLow Indices here and lower do not contain the needle.\n * @param aHigh Indices here and higher do not contain the needle.\n * @param aNeedle The element being searched for.\n * @param aHaystack The non-empty array being searched.\n * @param aCompare Function which takes two elements and returns -1, 0, or 1.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n */\nfunction recursiveSearch(aLow, aHigh, a<PERSON>eed<PERSON>, aHaystack, aCompare, a<PERSON><PERSON>) {\n  // This function terminates when one of the following is true:\n  //\n  //   1. We find the exact element we are looking for.\n  //\n  //   2. We did not find the exact element, but we can return the index of\n  //      the next-closest element.\n  //\n  //   3. We did not find the exact element, and there is no next-closest\n  //      element than the one we are searching for, so we return -1.\n  var mid = Math.floor((aHigh - aLow) / 2) + aLow;\n  var cmp = aCompare(aNeedle, aHaystack[mid], true);\n  if (cmp === 0) {\n    // Found the element we are looking for.\n    return mid;\n  }\n  else if (cmp > 0) {\n    // Our needle is greater than aHaystack[mid].\n    if (aHigh - mid > 1) {\n      // The element is in the upper half.\n      return recursiveSearch(mid, aHigh, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // The exact needle element was not found in this haystack. Determine if\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return aHigh < aHaystack.length ? aHigh : -1;\n    } else {\n      return mid;\n    }\n  }\n  else {\n    // Our needle is less than aHaystack[mid].\n    if (mid - aLow > 1) {\n      // The element is in the lower half.\n      return recursiveSearch(aLow, mid, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return mid;\n    } else {\n      return aLow < 0 ? -1 : aLow;\n    }\n  }\n}\n\n/**\n * This is an implementation of binary search which will always try and return\n * the index of the closest element if there is no exact hit. This is because\n * mappings between original and generated line/col pairs are single points,\n * and there is an implicit region between each of them, so a miss just means\n * that you aren't on the very start of a region.\n *\n * @param aNeedle The element you are looking for.\n * @param aHaystack The array that is being searched.\n * @param aCompare A function which takes the needle and an element in the\n *     array and returns -1, 0, or 1 depending on whether the needle is less\n *     than, equal to, or greater than the element, respectively.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'binarySearch.GREATEST_LOWER_BOUND'.\n */\nexports.search = function search(aNeedle, aHaystack, aCompare, aBias) {\n  if (aHaystack.length === 0) {\n    return -1;\n  }\n\n  var index = recursiveSearch(-1, aHaystack.length, aNeedle, aHaystack,\n                              aCompare, aBias || exports.GREATEST_LOWER_BOUND);\n  if (index < 0) {\n    return -1;\n  }\n\n  // We have found either the exact element, or the next-closest element than\n  // the one we are searching for. However, there may be more than one such\n  // element. Make sure we always return the smallest of these.\n  while (index - 1 >= 0) {\n    if (aCompare(aHaystack[index], aHaystack[index - 1], true) !== 0) {\n      break;\n    }\n    --index;\n  }\n\n  return index;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n// It turns out that some (most?) JavaScript engines don't self-host\n// `Array.prototype.sort`. This makes sense because C++ will likely remain\n// faster than JS when doing raw CPU-intensive sorting. However, when using a\n// custom comparator function, calling back and forth between the VM's C++ and\n// JIT'd JS is rather slow *and* loses JIT type information, resulting in\n// worse generated code for the comparator function than would be optimal. In\n// fact, when sorting with a comparator, these costs outweigh the benefits of\n// sorting in C++. By using our own JS-implemented Quick Sort (below), we get\n// a ~3500ms mean speed-up in `bench/bench.html`.\n\n/**\n * Swap the elements indexed by `x` and `y` in the array `ary`.\n *\n * @param {Array} ary\n *        The array.\n * @param {Number} x\n *        The index of the first item.\n * @param {Number} y\n *        The index of the second item.\n */\nfunction swap(ary, x, y) {\n  var temp = ary[x];\n  ary[x] = ary[y];\n  ary[y] = temp;\n}\n\n/**\n * Returns a random integer within the range `low .. high` inclusive.\n *\n * @param {Number} low\n *        The lower bound on the range.\n * @param {Number} high\n *        The upper bound on the range.\n */\nfunction randomIntInRange(low, high) {\n  return Math.round(low + (Math.random() * (high - low)));\n}\n\n/**\n * The Quick Sort algorithm.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n * @param {Number} p\n *        Start index of the array\n * @param {Number} r\n *        End index of the array\n */\nfunction doQuickSort(ary, comparator, p, r) {\n  // If our lower bound is less than our upper bound, we (1) partition the\n  // array into two pieces and (2) recurse on each half. If it is not, this is\n  // the empty array and our base case.\n\n  if (p < r) {\n    // (1) Partitioning.\n    //\n    // The partitioning chooses a pivot between `p` and `r` and moves all\n    // elements that are less than or equal to the pivot to the before it, and\n    // all the elements that are greater than it after it. The effect is that\n    // once partition is done, the pivot is in the exact place it will be when\n    // the array is put in sorted order, and it will not need to be moved\n    // again. This runs in O(n) time.\n\n    // Always choose a random pivot so that an input array which is reverse\n    // sorted does not cause O(n^2) running time.\n    var pivotIndex = randomIntInRange(p, r);\n    var i = p - 1;\n\n    swap(ary, pivotIndex, r);\n    var pivot = ary[r];\n\n    // Immediately after `j` is incremented in this loop, the following hold\n    // true:\n    //\n    //   * Every element in `ary[p .. i]` is less than or equal to the pivot.\n    //\n    //   * Every element in `ary[i+1 .. j-1]` is greater than the pivot.\n    for (var j = p; j < r; j++) {\n      if (comparator(ary[j], pivot) <= 0) {\n        i += 1;\n        swap(ary, i, j);\n      }\n    }\n\n    swap(ary, i + 1, j);\n    var q = i + 1;\n\n    // (2) Recurse on each half.\n\n    doQuickSort(ary, comparator, p, q - 1);\n    doQuickSort(ary, comparator, q + 1, r);\n  }\n}\n\n/**\n * Sort the given array in-place with the given comparator function.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n */\nexports.quickSort = function (ary, comparator) {\n  doQuickSort(ary, comparator, 0, ary.length - 1);\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar binarySearch = require('./binary-search');\nvar ArraySet = require('./array-set').ArraySet;\nvar base64VLQ = require('./base64-vlq');\nvar quickSort = require('./quick-sort').quickSort;\n\nfunction SourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  return sourceMap.sections != null\n    ? new IndexedSourceMapConsumer(sourceMap)\n    : new BasicSourceMapConsumer(sourceMap);\n}\n\nSourceMapConsumer.fromSourceMap = function(aSourceMap) {\n  return BasicSourceMapConsumer.fromSourceMap(aSourceMap);\n}\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nSourceMapConsumer.prototype._version = 3;\n\n// `__generatedMappings` and `__originalMappings` are arrays that hold the\n// parsed mapping coordinates from the source map's \"mappings\" attribute. They\n// are lazily instantiated, accessed via the `_generatedMappings` and\n// `_originalMappings` getters respectively, and we only parse the mappings\n// and create these arrays once queried for a source location. We jump through\n// these hoops because there can be many thousands of mappings, and parsing\n// them is expensive, so we only want to do it if we must.\n//\n// Each object in the arrays is of the form:\n//\n//     {\n//       generatedLine: The line number in the generated code,\n//       generatedColumn: The column number in the generated code,\n//       source: The path to the original source file that generated this\n//               chunk of code,\n//       originalLine: The line number in the original source that\n//                     corresponds to this chunk of generated code,\n//       originalColumn: The column number in the original source that\n//                       corresponds to this chunk of generated code,\n//       name: The name of the original symbol which generated this chunk of\n//             code.\n//     }\n//\n// All properties except for `generatedLine` and `generatedColumn` can be\n// `null`.\n//\n// `_generatedMappings` is ordered by the generated positions.\n//\n// `_originalMappings` is ordered by the original positions.\n\nSourceMapConsumer.prototype.__generatedMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_generatedMappings', {\n  get: function () {\n    if (!this.__generatedMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__generatedMappings;\n  }\n});\n\nSourceMapConsumer.prototype.__originalMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_originalMappings', {\n  get: function () {\n    if (!this.__originalMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__originalMappings;\n  }\n});\n\nSourceMapConsumer.prototype._charIsMappingSeparator =\n  function SourceMapConsumer_charIsMappingSeparator(aStr, index) {\n    var c = aStr.charAt(index);\n    return c === \";\" || c === \",\";\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    throw new Error(\"Subclasses must implement _parseMappings\");\n  };\n\nSourceMapConsumer.GENERATED_ORDER = 1;\nSourceMapConsumer.ORIGINAL_ORDER = 2;\n\nSourceMapConsumer.GREATEST_LOWER_BOUND = 1;\nSourceMapConsumer.LEAST_UPPER_BOUND = 2;\n\n/**\n * Iterate over each mapping between an original source/line/column and a\n * generated line/column in this source map.\n *\n * @param Function aCallback\n *        The function that is called with each mapping.\n * @param Object aContext\n *        Optional. If specified, this object will be the value of `this` every\n *        time that `aCallback` is called.\n * @param aOrder\n *        Either `SourceMapConsumer.GENERATED_ORDER` or\n *        `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to\n *        iterate over the mappings sorted by the generated file's line/column\n *        order or the original's source/line/column order, respectively. Defaults to\n *        `SourceMapConsumer.GENERATED_ORDER`.\n */\nSourceMapConsumer.prototype.eachMapping =\n  function SourceMapConsumer_eachMapping(aCallback, aContext, aOrder) {\n    var context = aContext || null;\n    var order = aOrder || SourceMapConsumer.GENERATED_ORDER;\n\n    var mappings;\n    switch (order) {\n    case SourceMapConsumer.GENERATED_ORDER:\n      mappings = this._generatedMappings;\n      break;\n    case SourceMapConsumer.ORIGINAL_ORDER:\n      mappings = this._originalMappings;\n      break;\n    default:\n      throw new Error(\"Unknown order of iteration.\");\n    }\n\n    var sourceRoot = this.sourceRoot;\n    mappings.map(function (mapping) {\n      var source = mapping.source === null ? null : this._sources.at(mapping.source);\n      if (source != null && sourceRoot != null) {\n        source = util.join(sourceRoot, source);\n      }\n      return {\n        source: source,\n        generatedLine: mapping.generatedLine,\n        generatedColumn: mapping.generatedColumn,\n        originalLine: mapping.originalLine,\n        originalColumn: mapping.originalColumn,\n        name: mapping.name === null ? null : this._names.at(mapping.name)\n      };\n    }, this).forEach(aCallback, context);\n  };\n\n/**\n * Returns all generated line and column information for the original source,\n * line, and column provided. If no column is provided, returns all mappings\n * corresponding to a either the line we are searching for or the next\n * closest line that has any mappings. Otherwise, returns all mappings\n * corresponding to the given line and either the column we are searching for\n * or the next closest column that has any offsets.\n *\n * The only argument is an object with the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: Optional. the column number in the original source.\n *\n * and an array of objects is returned, each with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nSourceMapConsumer.prototype.allGeneratedPositionsFor =\n  function SourceMapConsumer_allGeneratedPositionsFor(aArgs) {\n    var line = util.getArg(aArgs, 'line');\n\n    // When there is no exact match, BasicSourceMapConsumer.prototype._findMapping\n    // returns the index of the closest mapping less than the needle. By\n    // setting needle.originalColumn to 0, we thus find the last mapping for\n    // the given line, provided such a mapping exists.\n    var needle = {\n      source: util.getArg(aArgs, 'source'),\n      originalLine: line,\n      originalColumn: util.getArg(aArgs, 'column', 0)\n    };\n\n    if (this.sourceRoot != null) {\n      needle.source = util.relative(this.sourceRoot, needle.source);\n    }\n    if (!this._sources.has(needle.source)) {\n      return [];\n    }\n    needle.source = this._sources.indexOf(needle.source);\n\n    var mappings = [];\n\n    var index = this._findMapping(needle,\n                                  this._originalMappings,\n                                  \"originalLine\",\n                                  \"originalColumn\",\n                                  util.compareByOriginalPositions,\n                                  binarySearch.LEAST_UPPER_BOUND);\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (aArgs.column === undefined) {\n        var originalLine = mapping.originalLine;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we found. Since\n        // mappings are sorted, this is guaranteed to find all mappings for\n        // the line we found.\n        while (mapping && mapping.originalLine === originalLine) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      } else {\n        var originalColumn = mapping.originalColumn;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we were searching for.\n        // Since mappings are sorted, this is guaranteed to find all mappings for\n        // the line we are searching for.\n        while (mapping &&\n               mapping.originalLine === line &&\n               mapping.originalColumn == originalColumn) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      }\n    }\n\n    return mappings;\n  };\n\nexports.SourceMapConsumer = SourceMapConsumer;\n\n/**\n * A BasicSourceMapConsumer instance represents a parsed source map which we can\n * query for information about the original file positions by giving it a file\n * position in the generated source.\n *\n * The only parameter is the raw source map (either as a JSON string, or\n * already parsed to an object). According to the spec, source maps have the\n * following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - sources: An array of URLs to the original source files.\n *   - names: An array of identifiers which can be referrenced by individual mappings.\n *   - sourceRoot: Optional. The URL root from which all sources are relative.\n *   - sourcesContent: Optional. An array of contents of the original source files.\n *   - mappings: A string of base64 VLQs which contain the actual mappings.\n *   - file: Optional. The generated file this source map is associated with.\n *\n * Here is an example source map, taken from the source map spec[0]:\n *\n *     {\n *       version : 3,\n *       file: \"out.js\",\n *       sourceRoot : \"\",\n *       sources: [\"foo.js\", \"bar.js\"],\n *       names: [\"src\", \"maps\", \"are\", \"fun\"],\n *       mappings: \"AA,AB;;ABCDE;\"\n *     }\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit?pli=1#\n */\nfunction BasicSourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sources = util.getArg(sourceMap, 'sources');\n  // Sass 3.3 leaves out the 'names' array, so we deviate from the spec (which\n  // requires the array) to play nice here.\n  var names = util.getArg(sourceMap, 'names', []);\n  var sourceRoot = util.getArg(sourceMap, 'sourceRoot', null);\n  var sourcesContent = util.getArg(sourceMap, 'sourcesContent', null);\n  var mappings = util.getArg(sourceMap, 'mappings');\n  var file = util.getArg(sourceMap, 'file', null);\n\n  // Once again, Sass deviates from the spec and supplies the version as a\n  // string rather than a number, so we use loose equality checking here.\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  sources = sources\n    .map(String)\n    // Some source maps produce relative source paths like \"./foo.js\" instead of\n    // \"foo.js\".  Normalize these first so that future comparisons will succeed.\n    // See bugzil.la/1090768.\n    .map(util.normalize)\n    // Always ensure that absolute sources are internally stored relative to\n    // the source root, if the source root is absolute. Not doing this would\n    // be particularly problematic when the source root is a prefix of the\n    // source (valid, but why??). See github issue #199 and bugzil.la/1188982.\n    .map(function (source) {\n      return sourceRoot && util.isAbsolute(sourceRoot) && util.isAbsolute(source)\n        ? util.relative(sourceRoot, source)\n        : source;\n    });\n\n  // Pass `true` below to allow duplicate names and sources. While source maps\n  // are intended to be compressed and deduplicated, the TypeScript compiler\n  // sometimes generates source maps with duplicates in them. See Github issue\n  // #72 and bugzil.la/889492.\n  this._names = ArraySet.fromArray(names.map(String), true);\n  this._sources = ArraySet.fromArray(sources, true);\n\n  this.sourceRoot = sourceRoot;\n  this.sourcesContent = sourcesContent;\n  this._mappings = mappings;\n  this.file = file;\n}\n\nBasicSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nBasicSourceMapConsumer.prototype.consumer = SourceMapConsumer;\n\n/**\n * Create a BasicSourceMapConsumer from a SourceMapGenerator.\n *\n * @param SourceMapGenerator aSourceMap\n *        The source map that will be consumed.\n * @returns BasicSourceMapConsumer\n */\nBasicSourceMapConsumer.fromSourceMap =\n  function SourceMapConsumer_fromSourceMap(aSourceMap) {\n    var smc = Object.create(BasicSourceMapConsumer.prototype);\n\n    var names = smc._names = ArraySet.fromArray(aSourceMap._names.toArray(), true);\n    var sources = smc._sources = ArraySet.fromArray(aSourceMap._sources.toArray(), true);\n    smc.sourceRoot = aSourceMap._sourceRoot;\n    smc.sourcesContent = aSourceMap._generateSourcesContent(smc._sources.toArray(),\n                                                            smc.sourceRoot);\n    smc.file = aSourceMap._file;\n\n    // Because we are modifying the entries (by converting string sources and\n    // names to indices into the sources and names ArraySets), we have to make\n    // a copy of the entry or else bad things happen. Shared mutable state\n    // strikes again! See github issue #191.\n\n    var generatedMappings = aSourceMap._mappings.toArray().slice();\n    var destGeneratedMappings = smc.__generatedMappings = [];\n    var destOriginalMappings = smc.__originalMappings = [];\n\n    for (var i = 0, length = generatedMappings.length; i < length; i++) {\n      var srcMapping = generatedMappings[i];\n      var destMapping = new Mapping;\n      destMapping.generatedLine = srcMapping.generatedLine;\n      destMapping.generatedColumn = srcMapping.generatedColumn;\n\n      if (srcMapping.source) {\n        destMapping.source = sources.indexOf(srcMapping.source);\n        destMapping.originalLine = srcMapping.originalLine;\n        destMapping.originalColumn = srcMapping.originalColumn;\n\n        if (srcMapping.name) {\n          destMapping.name = names.indexOf(srcMapping.name);\n        }\n\n        destOriginalMappings.push(destMapping);\n      }\n\n      destGeneratedMappings.push(destMapping);\n    }\n\n    quickSort(smc.__originalMappings, util.compareByOriginalPositions);\n\n    return smc;\n  };\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nBasicSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(BasicSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    return this._sources.toArray().map(function (s) {\n      return this.sourceRoot != null ? util.join(this.sourceRoot, s) : s;\n    }, this);\n  }\n});\n\n/**\n * Provide the JIT with a nice shape / hidden class.\n */\nfunction Mapping() {\n  this.generatedLine = 0;\n  this.generatedColumn = 0;\n  this.source = null;\n  this.originalLine = null;\n  this.originalColumn = null;\n  this.name = null;\n}\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nBasicSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    var generatedLine = 1;\n    var previousGeneratedColumn = 0;\n    var previousOriginalLine = 0;\n    var previousOriginalColumn = 0;\n    var previousSource = 0;\n    var previousName = 0;\n    var length = aStr.length;\n    var index = 0;\n    var cachedSegments = {};\n    var temp = {};\n    var originalMappings = [];\n    var generatedMappings = [];\n    var mapping, str, segment, end, value;\n\n    while (index < length) {\n      if (aStr.charAt(index) === ';') {\n        generatedLine++;\n        index++;\n        previousGeneratedColumn = 0;\n      }\n      else if (aStr.charAt(index) === ',') {\n        index++;\n      }\n      else {\n        mapping = new Mapping();\n        mapping.generatedLine = generatedLine;\n\n        // Because each offset is encoded relative to the previous one,\n        // many segments often have the same encoding. We can exploit this\n        // fact by caching the parsed variable length fields of each segment,\n        // allowing us to avoid a second parse if we encounter the same\n        // segment again.\n        for (end = index; end < length; end++) {\n          if (this._charIsMappingSeparator(aStr, end)) {\n            break;\n          }\n        }\n        str = aStr.slice(index, end);\n\n        segment = cachedSegments[str];\n        if (segment) {\n          index += str.length;\n        } else {\n          segment = [];\n          while (index < end) {\n            base64VLQ.decode(aStr, index, temp);\n            value = temp.value;\n            index = temp.rest;\n            segment.push(value);\n          }\n\n          if (segment.length === 2) {\n            throw new Error('Found a source, but no line and column');\n          }\n\n          if (segment.length === 3) {\n            throw new Error('Found a source and line, but no column');\n          }\n\n          cachedSegments[str] = segment;\n        }\n\n        // Generated column.\n        mapping.generatedColumn = previousGeneratedColumn + segment[0];\n        previousGeneratedColumn = mapping.generatedColumn;\n\n        if (segment.length > 1) {\n          // Original source.\n          mapping.source = previousSource + segment[1];\n          previousSource += segment[1];\n\n          // Original line.\n          mapping.originalLine = previousOriginalLine + segment[2];\n          previousOriginalLine = mapping.originalLine;\n          // Lines are stored 0-based\n          mapping.originalLine += 1;\n\n          // Original column.\n          mapping.originalColumn = previousOriginalColumn + segment[3];\n          previousOriginalColumn = mapping.originalColumn;\n\n          if (segment.length > 4) {\n            // Original name.\n            mapping.name = previousName + segment[4];\n            previousName += segment[4];\n          }\n        }\n\n        generatedMappings.push(mapping);\n        if (typeof mapping.originalLine === 'number') {\n          originalMappings.push(mapping);\n        }\n      }\n    }\n\n    quickSort(generatedMappings, util.compareByGeneratedPositionsDeflated);\n    this.__generatedMappings = generatedMappings;\n\n    quickSort(originalMappings, util.compareByOriginalPositions);\n    this.__originalMappings = originalMappings;\n  };\n\n/**\n * Find the mapping that best matches the hypothetical \"needle\" mapping that\n * we are searching for in the given \"haystack\" of mappings.\n */\nBasicSourceMapConsumer.prototype._findMapping =\n  function SourceMapConsumer_findMapping(aNeedle, aMappings, aLineName,\n                                         aColumnName, aComparator, aBias) {\n    // To return the position we are searching for, we must first find the\n    // mapping for the given position and then return the opposite position it\n    // points to. Because the mappings are sorted, we can use binary search to\n    // find the best mapping.\n\n    if (aNeedle[aLineName] <= 0) {\n      throw new TypeError('Line must be greater than or equal to 1, got '\n                          + aNeedle[aLineName]);\n    }\n    if (aNeedle[aColumnName] < 0) {\n      throw new TypeError('Column must be greater than or equal to 0, got '\n                          + aNeedle[aColumnName]);\n    }\n\n    return binarySearch.search(aNeedle, aMappings, aComparator, aBias);\n  };\n\n/**\n * Compute the last column for each generated mapping. The last column is\n * inclusive.\n */\nBasicSourceMapConsumer.prototype.computeColumnSpans =\n  function SourceMapConsumer_computeColumnSpans() {\n    for (var index = 0; index < this._generatedMappings.length; ++index) {\n      var mapping = this._generatedMappings[index];\n\n      // Mappings do not contain a field for the last generated columnt. We\n      // can come up with an optimistic estimate, however, by assuming that\n      // mappings are contiguous (i.e. given two consecutive mappings, the\n      // first mapping ends where the second one starts).\n      if (index + 1 < this._generatedMappings.length) {\n        var nextMapping = this._generatedMappings[index + 1];\n\n        if (mapping.generatedLine === nextMapping.generatedLine) {\n          mapping.lastGeneratedColumn = nextMapping.generatedColumn - 1;\n          continue;\n        }\n      }\n\n      // The last mapping for each line spans the entire line.\n      mapping.lastGeneratedColumn = Infinity;\n    }\n  };\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.\n *   - column: The column number in the generated source.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.\n *   - column: The column number in the original source, or null.\n *   - name: The original identifier, or null.\n */\nBasicSourceMapConsumer.prototype.originalPositionFor =\n  function SourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._generatedMappings,\n      \"generatedLine\",\n      \"generatedColumn\",\n      util.compareByGeneratedPositionsDeflated,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._generatedMappings[index];\n\n      if (mapping.generatedLine === needle.generatedLine) {\n        var source = util.getArg(mapping, 'source', null);\n        if (source !== null) {\n          source = this._sources.at(source);\n          if (this.sourceRoot != null) {\n            source = util.join(this.sourceRoot, source);\n          }\n        }\n        var name = util.getArg(mapping, 'name', null);\n        if (name !== null) {\n          name = this._names.at(name);\n        }\n        return {\n          source: source,\n          line: util.getArg(mapping, 'originalLine', null),\n          column: util.getArg(mapping, 'originalColumn', null),\n          name: name\n        };\n      }\n    }\n\n    return {\n      source: null,\n      line: null,\n      column: null,\n      name: null\n    };\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nBasicSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function BasicSourceMapConsumer_hasContentsOfAllSources() {\n    if (!this.sourcesContent) {\n      return false;\n    }\n    return this.sourcesContent.length >= this._sources.size() &&\n      !this.sourcesContent.some(function (sc) { return sc == null; });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nBasicSourceMapConsumer.prototype.sourceContentFor =\n  function SourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    if (!this.sourcesContent) {\n      return null;\n    }\n\n    if (this.sourceRoot != null) {\n      aSource = util.relative(this.sourceRoot, aSource);\n    }\n\n    if (this._sources.has(aSource)) {\n      return this.sourcesContent[this._sources.indexOf(aSource)];\n    }\n\n    var url;\n    if (this.sourceRoot != null\n        && (url = util.urlParse(this.sourceRoot))) {\n      // XXX: file:// URIs and absolute paths lead to unexpected behavior for\n      // many users. We can help them out when they expect file:// URIs to\n      // behave like it would if they were running a local HTTP server. See\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=885597.\n      var fileUriAbsPath = aSource.replace(/^file:\\/\\//, \"\");\n      if (url.scheme == \"file\"\n          && this._sources.has(fileUriAbsPath)) {\n        return this.sourcesContent[this._sources.indexOf(fileUriAbsPath)]\n      }\n\n      if ((!url.path || url.path == \"/\")\n          && this._sources.has(\"/\" + aSource)) {\n        return this.sourcesContent[this._sources.indexOf(\"/\" + aSource)];\n      }\n    }\n\n    // This function is used recursively from\n    // IndexedSourceMapConsumer.prototype.sourceContentFor. In that case, we\n    // don't want to throw if we can't find the source - we just want to\n    // return null, so we provide a flag to exit gracefully.\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: The column number in the original source.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nBasicSourceMapConsumer.prototype.generatedPositionFor =\n  function SourceMapConsumer_generatedPositionFor(aArgs) {\n    var source = util.getArg(aArgs, 'source');\n    if (this.sourceRoot != null) {\n      source = util.relative(this.sourceRoot, source);\n    }\n    if (!this._sources.has(source)) {\n      return {\n        line: null,\n        column: null,\n        lastColumn: null\n      };\n    }\n    source = this._sources.indexOf(source);\n\n    var needle = {\n      source: source,\n      originalLine: util.getArg(aArgs, 'line'),\n      originalColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._originalMappings,\n      \"originalLine\",\n      \"originalColumn\",\n      util.compareByOriginalPositions,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (mapping.source === needle.source) {\n        return {\n          line: util.getArg(mapping, 'generatedLine', null),\n          column: util.getArg(mapping, 'generatedColumn', null),\n          lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n        };\n      }\n    }\n\n    return {\n      line: null,\n      column: null,\n      lastColumn: null\n    };\n  };\n\nexports.BasicSourceMapConsumer = BasicSourceMapConsumer;\n\n/**\n * An IndexedSourceMapConsumer instance represents a parsed source map which\n * we can query for information. It differs from BasicSourceMapConsumer in\n * that it takes \"indexed\" source maps (i.e. ones with a \"sections\" field) as\n * input.\n *\n * The only parameter is a raw source map (either as a JSON string, or already\n * parsed to an object). According to the spec for indexed source maps, they\n * have the following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - file: Optional. The generated file this source map is associated with.\n *   - sections: A list of section definitions.\n *\n * Each value under the \"sections\" field has two fields:\n *   - offset: The offset into the original specified at which this section\n *       begins to apply, defined as an object with a \"line\" and \"column\"\n *       field.\n *   - map: A source map definition. This source map could also be indexed,\n *       but doesn't have to be.\n *\n * Instead of the \"map\" field, it's also possible to have a \"url\" field\n * specifying a URL to retrieve a source map from, but that's currently\n * unsupported.\n *\n * Here's an example source map, taken from the source map spec[0], but\n * modified to omit a section which uses the \"url\" field.\n *\n *  {\n *    version : 3,\n *    file: \"app.js\",\n *    sections: [{\n *      offset: {line:100, column:10},\n *      map: {\n *        version : 3,\n *        file: \"section.js\",\n *        sources: [\"foo.js\", \"bar.js\"],\n *        names: [\"src\", \"maps\", \"are\", \"fun\"],\n *        mappings: \"AAAA,E;;ABCDE;\"\n *      }\n *    }],\n *  }\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit#heading=h.535es3xeprgt\n */\nfunction IndexedSourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sections = util.getArg(sourceMap, 'sections');\n\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n\n  var lastOffset = {\n    line: -1,\n    column: 0\n  };\n  this._sections = sections.map(function (s) {\n    if (s.url) {\n      // The url field will require support for asynchronicity.\n      // See https://github.com/mozilla/source-map/issues/16\n      throw new Error('Support for url field in sections not implemented.');\n    }\n    var offset = util.getArg(s, 'offset');\n    var offsetLine = util.getArg(offset, 'line');\n    var offsetColumn = util.getArg(offset, 'column');\n\n    if (offsetLine < lastOffset.line ||\n        (offsetLine === lastOffset.line && offsetColumn < lastOffset.column)) {\n      throw new Error('Section offsets must be ordered and non-overlapping.');\n    }\n    lastOffset = offset;\n\n    return {\n      generatedOffset: {\n        // The offset fields are 0-based, but we use 1-based indices when\n        // encoding/decoding from VLQ.\n        generatedLine: offsetLine + 1,\n        generatedColumn: offsetColumn + 1\n      },\n      consumer: new SourceMapConsumer(util.getArg(s, 'map'))\n    }\n  });\n}\n\nIndexedSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nIndexedSourceMapConsumer.prototype.constructor = SourceMapConsumer;\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nIndexedSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(IndexedSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    var sources = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      for (var j = 0; j < this._sections[i].consumer.sources.length; j++) {\n        sources.push(this._sections[i].consumer.sources[j]);\n      }\n    }\n    return sources;\n  }\n});\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.\n *   - column: The column number in the generated source.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.\n *   - column: The column number in the original source, or null.\n *   - name: The original identifier, or null.\n */\nIndexedSourceMapConsumer.prototype.originalPositionFor =\n  function IndexedSourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    // Find the section containing the generated position we're trying to map\n    // to an original position.\n    var sectionIndex = binarySearch.search(needle, this._sections,\n      function(needle, section) {\n        var cmp = needle.generatedLine - section.generatedOffset.generatedLine;\n        if (cmp) {\n          return cmp;\n        }\n\n        return (needle.generatedColumn -\n                section.generatedOffset.generatedColumn);\n      });\n    var section = this._sections[sectionIndex];\n\n    if (!section) {\n      return {\n        source: null,\n        line: null,\n        column: null,\n        name: null\n      };\n    }\n\n    return section.consumer.originalPositionFor({\n      line: needle.generatedLine -\n        (section.generatedOffset.generatedLine - 1),\n      column: needle.generatedColumn -\n        (section.generatedOffset.generatedLine === needle.generatedLine\n         ? section.generatedOffset.generatedColumn - 1\n         : 0),\n      bias: aArgs.bias\n    });\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nIndexedSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function IndexedSourceMapConsumer_hasContentsOfAllSources() {\n    return this._sections.every(function (s) {\n      return s.consumer.hasContentsOfAllSources();\n    });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nIndexedSourceMapConsumer.prototype.sourceContentFor =\n  function IndexedSourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      var content = section.consumer.sourceContentFor(aSource, true);\n      if (content) {\n        return content;\n      }\n    }\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: The column number in the original source.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nIndexedSourceMapConsumer.prototype.generatedPositionFor =\n  function IndexedSourceMapConsumer_generatedPositionFor(aArgs) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      // Only consider this section if the requested source is in the list of\n      // sources of the consumer.\n      if (section.consumer.sources.indexOf(util.getArg(aArgs, 'source')) === -1) {\n        continue;\n      }\n      var generatedPosition = section.consumer.generatedPositionFor(aArgs);\n      if (generatedPosition) {\n        var ret = {\n          line: generatedPosition.line +\n            (section.generatedOffset.generatedLine - 1),\n          column: generatedPosition.column +\n            (section.generatedOffset.generatedLine === generatedPosition.line\n             ? section.generatedOffset.generatedColumn - 1\n             : 0)\n        };\n        return ret;\n      }\n    }\n\n    return {\n      line: null,\n      column: null\n    };\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nIndexedSourceMapConsumer.prototype._parseMappings =\n  function IndexedSourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    this.__generatedMappings = [];\n    this.__originalMappings = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n      var sectionMappings = section.consumer._generatedMappings;\n      for (var j = 0; j < sectionMappings.length; j++) {\n        var mapping = sectionMappings[j];\n\n        var source = section.consumer._sources.at(mapping.source);\n        if (section.consumer.sourceRoot !== null) {\n          source = util.join(section.consumer.sourceRoot, source);\n        }\n        this._sources.add(source);\n        source = this._sources.indexOf(source);\n\n        var name = section.consumer._names.at(mapping.name);\n        this._names.add(name);\n        name = this._names.indexOf(name);\n\n        // The mappings coming from the consumer for the section have\n        // generated positions relative to the start of the section, so we\n        // need to offset them to be relative to the start of the concatenated\n        // generated file.\n        var adjustedMapping = {\n          source: source,\n          generatedLine: mapping.generatedLine +\n            (section.generatedOffset.generatedLine - 1),\n          generatedColumn: mapping.generatedColumn +\n            (section.generatedOffset.generatedLine === mapping.generatedLine\n            ? section.generatedOffset.generatedColumn - 1\n            : 0),\n          originalLine: mapping.originalLine,\n          originalColumn: mapping.originalColumn,\n          name: name\n        };\n\n        this.__generatedMappings.push(adjustedMapping);\n        if (typeof adjustedMapping.originalLine === 'number') {\n          this.__originalMappings.push(adjustedMapping);\n        }\n      }\n    }\n\n    quickSort(this.__generatedMappings, util.compareByGeneratedPositionsDeflated);\n    quickSort(this.__originalMappings, util.compareByOriginalPositions);\n  };\n\nexports.IndexedSourceMapConsumer = IndexedSourceMapConsumer;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n/**\n * This is a helper function for getting values from parameter/options\n * objects.\n *\n * @param args The object we are extracting values from\n * @param name The name of the property we are getting.\n * @param defaultValue An optional value to return if the property is missing\n * from the object. If this is not specified and the property is missing, an\n * error will be thrown.\n */\nfunction getArg(aArgs, aName, aDefaultValue) {\n  if (aName in aArgs) {\n    return aArgs[aName];\n  } else if (arguments.length === 3) {\n    return aDefaultValue;\n  } else {\n    throw new Error('\"' + aName + '\" is a required argument.');\n  }\n}\nexports.getArg = getArg;\n\nvar urlRegexp = /^(?:([\\w+\\-.]+):)?\\/\\/(?:(\\w+:\\w+)@)?([\\w.]*)(?::(\\d+))?(\\S*)$/;\nvar dataUrlRegexp = /^data:.+\\,.+$/;\n\nfunction urlParse(aUrl) {\n  var match = aUrl.match(urlRegexp);\n  if (!match) {\n    return null;\n  }\n  return {\n    scheme: match[1],\n    auth: match[2],\n    host: match[3],\n    port: match[4],\n    path: match[5]\n  };\n}\nexports.urlParse = urlParse;\n\nfunction urlGenerate(aParsedUrl) {\n  var url = '';\n  if (aParsedUrl.scheme) {\n    url += aParsedUrl.scheme + ':';\n  }\n  url += '//';\n  if (aParsedUrl.auth) {\n    url += aParsedUrl.auth + '@';\n  }\n  if (aParsedUrl.host) {\n    url += aParsedUrl.host;\n  }\n  if (aParsedUrl.port) {\n    url += \":\" + aParsedUrl.port\n  }\n  if (aParsedUrl.path) {\n    url += aParsedUrl.path;\n  }\n  return url;\n}\nexports.urlGenerate = urlGenerate;\n\n/**\n * Normalizes a path, or the path portion of a URL:\n *\n * - Replaces consecutive slashes with one slash.\n * - Removes unnecessary '.' parts.\n * - Removes unnecessary '<dir>/..' parts.\n *\n * Based on code in the Node.js 'path' core module.\n *\n * @param aPath The path or url to normalize.\n */\nfunction normalize(aPath) {\n  var path = aPath;\n  var url = urlParse(aPath);\n  if (url) {\n    if (!url.path) {\n      return aPath;\n    }\n    path = url.path;\n  }\n  var isAbsolute = exports.isAbsolute(path);\n\n  var parts = path.split(/\\/+/);\n  for (var part, up = 0, i = parts.length - 1; i >= 0; i--) {\n    part = parts[i];\n    if (part === '.') {\n      parts.splice(i, 1);\n    } else if (part === '..') {\n      up++;\n    } else if (up > 0) {\n      if (part === '') {\n        // The first part is blank if the path is absolute. Trying to go\n        // above the root is a no-op. Therefore we can remove all '..' parts\n        // directly after the root.\n        parts.splice(i + 1, up);\n        up = 0;\n      } else {\n        parts.splice(i, 2);\n        up--;\n      }\n    }\n  }\n  path = parts.join('/');\n\n  if (path === '') {\n    path = isAbsolute ? '/' : '.';\n  }\n\n  if (url) {\n    url.path = path;\n    return urlGenerate(url);\n  }\n  return path;\n}\nexports.normalize = normalize;\n\n/**\n * Joins two paths/URLs.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be joined with the root.\n *\n * - If aPath is a URL or a data URI, aPath is returned, unless aPath is a\n *   scheme-relative URL: Then the scheme of aRoot, if any, is prepended\n *   first.\n * - Otherwise aPath is a path. If aRoot is a URL, then its path portion\n *   is updated with the result and aRoot is returned. Otherwise the result\n *   is returned.\n *   - If aPath is absolute, the result is aPath.\n *   - Otherwise the two paths are joined with a slash.\n * - Joining for example 'http://' and 'www.example.com' is also supported.\n */\nfunction join(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n  if (aPath === \"\") {\n    aPath = \".\";\n  }\n  var aPathUrl = urlParse(aPath);\n  var aRootUrl = urlParse(aRoot);\n  if (aRootUrl) {\n    aRoot = aRootUrl.path || '/';\n  }\n\n  // `join(foo, '//www.example.org')`\n  if (aPathUrl && !aPathUrl.scheme) {\n    if (aRootUrl) {\n      aPathUrl.scheme = aRootUrl.scheme;\n    }\n    return urlGenerate(aPathUrl);\n  }\n\n  if (aPathUrl || aPath.match(dataUrlRegexp)) {\n    return aPath;\n  }\n\n  // `join('http://', 'www.example.com')`\n  if (aRootUrl && !aRootUrl.host && !aRootUrl.path) {\n    aRootUrl.host = aPath;\n    return urlGenerate(aRootUrl);\n  }\n\n  var joined = aPath.charAt(0) === '/'\n    ? aPath\n    : normalize(aRoot.replace(/\\/+$/, '') + '/' + aPath);\n\n  if (aRootUrl) {\n    aRootUrl.path = joined;\n    return urlGenerate(aRootUrl);\n  }\n  return joined;\n}\nexports.join = join;\n\nexports.isAbsolute = function (aPath) {\n  return aPath.charAt(0) === '/' || !!aPath.match(urlRegexp);\n};\n\n/**\n * Make a path relative to a URL or another path.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be made relative to aRoot.\n */\nfunction relative(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n\n  aRoot = aRoot.replace(/\\/$/, '');\n\n  // It is possible for the path to be above the root. In this case, simply\n  // checking whether the root is a prefix of the path won't work. Instead, we\n  // need to remove components from the root one by one, until either we find\n  // a prefix that fits, or we run out of components to remove.\n  var level = 0;\n  while (aPath.indexOf(aRoot + '/') !== 0) {\n    var index = aRoot.lastIndexOf(\"/\");\n    if (index < 0) {\n      return aPath;\n    }\n\n    // If the only part of the root that is left is the scheme (i.e. http://,\n    // file:///, etc.), one or more slashes (/), or simply nothing at all, we\n    // have exhausted all components, so the path is not relative to the root.\n    aRoot = aRoot.slice(0, index);\n    if (aRoot.match(/^([^\\/]+:\\/)?\\/*$/)) {\n      return aPath;\n    }\n\n    ++level;\n  }\n\n  // Make sure we add a \"../\" for each component we removed from the root.\n  return Array(level + 1).join(\"../\") + aPath.substr(aRoot.length + 1);\n}\nexports.relative = relative;\n\nvar supportsNullProto = (function () {\n  var obj = Object.create(null);\n  return !('__proto__' in obj);\n}());\n\nfunction identity (s) {\n  return s;\n}\n\n/**\n * Because behavior goes wacky when you set `__proto__` on objects, we\n * have to prefix all the strings in our set with an arbitrary character.\n *\n * See https://github.com/mozilla/source-map/pull/31 and\n * https://github.com/mozilla/source-map/issues/30\n *\n * @param String aStr\n */\nfunction toSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return '$' + aStr;\n  }\n\n  return aStr;\n}\nexports.toSetString = supportsNullProto ? identity : toSetString;\n\nfunction fromSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return aStr.slice(1);\n  }\n\n  return aStr;\n}\nexports.fromSetString = supportsNullProto ? identity : fromSetString;\n\nfunction isProtoString(s) {\n  if (!s) {\n    return false;\n  }\n\n  var length = s.length;\n\n  if (length < 9 /* \"__proto__\".length */) {\n    return false;\n  }\n\n  if (s.charCodeAt(length - 1) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 2) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 3) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 4) !== 116 /* 't' */ ||\n      s.charCodeAt(length - 5) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 6) !== 114 /* 'r' */ ||\n      s.charCodeAt(length - 7) !== 112 /* 'p' */ ||\n      s.charCodeAt(length - 8) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 9) !== 95  /* '_' */) {\n    return false;\n  }\n\n  for (var i = length - 10; i >= 0; i--) {\n    if (s.charCodeAt(i) !== 36 /* '$' */) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Comparator between two mappings where the original positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same original source/line/column, but different generated\n * line and column the same. Useful when searching for a mapping with a\n * stubbed out mapping.\n */\nfunction compareByOriginalPositions(mappingA, mappingB, onlyCompareOriginal) {\n  var cmp = mappingA.source - mappingB.source;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0 || onlyCompareOriginal) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return mappingA.name - mappingB.name;\n}\nexports.compareByOriginalPositions = compareByOriginalPositions;\n\n/**\n * Comparator between two mappings with deflated source and name indices where\n * the generated positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same generated line and column, but different\n * source/name/original line and column the same. Useful when searching for a\n * mapping with a stubbed out mapping.\n */\nfunction compareByGeneratedPositionsDeflated(mappingA, mappingB, onlyCompareGenerated) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0 || onlyCompareGenerated) {\n    return cmp;\n  }\n\n  cmp = mappingA.source - mappingB.source;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return mappingA.name - mappingB.name;\n}\nexports.compareByGeneratedPositionsDeflated = compareByGeneratedPositionsDeflated;\n\nfunction strcmp(aStr1, aStr2) {\n  if (aStr1 === aStr2) {\n    return 0;\n  }\n\n  if (aStr1 > aStr2) {\n    return 1;\n  }\n\n  return -1;\n}\n\n/**\n * Comparator between two mappings with inflated source and name strings where\n * the generated positions are compared.\n */\nfunction compareByGeneratedPositionsInflated(mappingA, mappingB) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsInflated = compareByGeneratedPositionsInflated;\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stack-generator', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.StackGenerator = factory(root.StackFrame);\n    }\n}(this, function(StackFrame) {\n    return {\n        backtrace: function StackGenerator$$backtrace(opts) {\n            var stack = [];\n            var maxStackSize = 10;\n\n            if (typeof opts === 'object' && typeof opts.maxStackSize === 'number') {\n                maxStackSize = opts.maxStackSize;\n            }\n\n            var curr = arguments.callee;\n            while (curr && stack.length < maxStackSize && curr['arguments']) {\n                // Allow V8 optimizations\n                var args = new Array(curr['arguments'].length);\n                for (var i = 0; i < args.length; ++i) {\n                    args[i] = curr['arguments'][i];\n                }\n                if (/function(?:\\s+([\\w$]+))+\\s*\\(/.test(curr.toString())) {\n                    stack.push(new StackFrame({functionName: RegExp.$1 || undefined, args: args}));\n                } else {\n                    stack.push(new StackFrame({args: args}));\n                }\n\n                try {\n                    curr = curr.caller;\n                } catch (e) {\n                    break;\n                }\n            }\n            return stack;\n        }\n    };\n}));\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stacktrace-gps', ['source-map', 'stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('source-map/lib/source-map-consumer'), require('stackframe'));\n    } else {\n        root.StackTraceGPS = factory(root.SourceMap || root.sourceMap, root.StackFrame);\n    }\n}(this, function(SourceMap, StackFrame) {\n    'use strict';\n\n    /**\n     * Make a X-Domain request to url and callback.\n     *\n     * @param {String} url\n     * @returns {Promise} with response text if fulfilled\n     */\n    function _xdr(url) {\n        return new Promise(function(resolve, reject) {\n            var req = new XMLHttpRequest();\n            req.open('get', url);\n            req.onerror = reject;\n            req.onreadystatechange = function onreadystatechange() {\n                if (req.readyState === 4) {\n                    if ((req.status >= 200 && req.status < 300) ||\n                        (url.substr(0, 7) === 'file://' && req.responseText)) {\n                        resolve(req.responseText);\n                    } else {\n                        reject(new Error('HTTP status: ' + req.status + ' retrieving ' + url));\n                    }\n                }\n            };\n            req.send();\n        });\n\n    }\n\n    /**\n     * Convert a Base64-encoded string into its original representation.\n     * Used for inline sourcemaps.\n     *\n     * @param {String} b64str Base-64 encoded string\n     * @returns {String} original representation of the base64-encoded string.\n     */\n    function _atob(b64str) {\n        if (typeof window !== 'undefined' && window.atob) {\n            return window.atob(b64str);\n        } else {\n            throw new Error('You must supply a polyfill for window.atob in this environment');\n        }\n    }\n\n    function _parseJson(string) {\n        if (typeof JSON !== 'undefined' && JSON.parse) {\n            return JSON.parse(string);\n        } else {\n            throw new Error('You must supply a polyfill for JSON.parse in this environment');\n        }\n    }\n\n    function _findFunctionName(source, lineNumber/*, columnNumber*/) {\n        var syntaxes = [\n            // {name} = function ({args}) TODO args capture\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*function\\b/,\n            // function {name}({args}) m[1]=name m[2]=args\n            /function\\s+([^('\"`]*?)\\s*\\(([^)]*)\\)/,\n            // {name} = eval()\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*(?:eval|new Function)\\b/,\n            // fn_name() {\n            /\\b(?!(?:if|for|switch|while|with|catch)\\b)(?:(?:static)\\s+)?(\\S+)\\s*\\(.*?\\)\\s*\\{/,\n            // {name} = () => {\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*\\(.*?\\)\\s*=>/\n        ];\n        var lines = source.split('\\n');\n\n        // Walk backwards in the source lines until we find the line which matches one of the patterns above\n        var code = '';\n        var maxLines = Math.min(lineNumber, 20);\n        for (var i = 0; i < maxLines; ++i) {\n            // lineNo is 1-based, source[] is 0-based\n            var line = lines[lineNumber - i - 1];\n            var commentPos = line.indexOf('//');\n            if (commentPos >= 0) {\n                line = line.substr(0, commentPos);\n            }\n\n            if (line) {\n                code = line + code;\n                var len = syntaxes.length;\n                for (var index = 0; index < len; index++) {\n                    var m = syntaxes[index].exec(code);\n                    if (m && m[1]) {\n                        return m[1];\n                    }\n                }\n            }\n        }\n        return undefined;\n    }\n\n    function _ensureSupportedEnvironment() {\n        if (typeof Object.defineProperty !== 'function' || typeof Object.create !== 'function') {\n            throw new Error('Unable to consume source maps in older browsers');\n        }\n    }\n\n    function _ensureStackFrameIsLegit(stackframe) {\n        if (typeof stackframe !== 'object') {\n            throw new TypeError('Given StackFrame is not an object');\n        } else if (typeof stackframe.fileName !== 'string') {\n            throw new TypeError('Given file name is not a String');\n        } else if (typeof stackframe.lineNumber !== 'number' ||\n            stackframe.lineNumber % 1 !== 0 ||\n            stackframe.lineNumber < 1) {\n            throw new TypeError('Given line number must be a positive integer');\n        } else if (typeof stackframe.columnNumber !== 'number' ||\n            stackframe.columnNumber % 1 !== 0 ||\n            stackframe.columnNumber < 0) {\n            throw new TypeError('Given column number must be a non-negative integer');\n        }\n        return true;\n    }\n\n    function _findSourceMappingURL(source) {\n        var sourceMappingUrlRegExp = /\\/\\/[#@] ?sourceMappingURL=([^\\s'\"]+)\\s*$/mg;\n        var lastSourceMappingUrl;\n        var matchSourceMappingUrl;\n        // eslint-disable-next-line no-cond-assign\n        while (matchSourceMappingUrl = sourceMappingUrlRegExp.exec(source)) {\n            lastSourceMappingUrl = matchSourceMappingUrl[1];\n        }\n        if (lastSourceMappingUrl) {\n            return lastSourceMappingUrl;\n        } else {\n            throw new Error('sourceMappingURL not found');\n        }\n    }\n\n    function _extractLocationInfoFromSourceMapSource(stackframe, sourceMapConsumer, sourceCache) {\n        return new Promise(function(resolve, reject) {\n            var loc = sourceMapConsumer.originalPositionFor({\n                line: stackframe.lineNumber,\n                column: stackframe.columnNumber\n            });\n\n            if (loc.source) {\n                // cache mapped sources\n                var mappedSource = sourceMapConsumer.sourceContentFor(loc.source);\n                if (mappedSource) {\n                    sourceCache[loc.source] = mappedSource;\n                }\n\n                resolve(\n                    // given stackframe and source location, update stackframe\n                    new StackFrame({\n                        functionName: loc.name || stackframe.functionName,\n                        args: stackframe.args,\n                        fileName: loc.source,\n                        lineNumber: loc.line,\n                        columnNumber: loc.column\n                    }));\n            } else {\n                reject(new Error('Could not get original source for given stackframe and source map'));\n            }\n        });\n    }\n\n    /**\n     * @constructor\n     * @param {Object} opts\n     *      opts.sourceCache = {url: \"Source String\"} => preload source cache\n     *      opts.sourceMapConsumerCache = {/path/file.js.map: SourceMapConsumer}\n     *      opts.offline = True to prevent network requests.\n     *              Best effort without sources or source maps.\n     *      opts.ajax = Promise returning function to make X-Domain requests\n     */\n    return function StackTraceGPS(opts) {\n        if (!(this instanceof StackTraceGPS)) {\n            return new StackTraceGPS(opts);\n        }\n        opts = opts || {};\n\n        this.sourceCache = opts.sourceCache || {};\n        this.sourceMapConsumerCache = opts.sourceMapConsumerCache || {};\n\n        this.ajax = opts.ajax || _xdr;\n\n        this._atob = opts.atob || _atob;\n\n        this._get = function _get(location) {\n            return new Promise(function(resolve, reject) {\n                var isDataUrl = location.substr(0, 5) === 'data:';\n                if (this.sourceCache[location]) {\n                    resolve(this.sourceCache[location]);\n                } else if (opts.offline && !isDataUrl) {\n                    reject(new Error('Cannot make network requests in offline mode'));\n                } else {\n                    if (isDataUrl) {\n                        // data URLs can have parameters.\n                        // see http://tools.ietf.org/html/rfc2397\n                        var supportedEncodingRegexp =\n                            /^data:application\\/json;([\\w=:\"-]+;)*base64,/;\n                        var match = location.match(supportedEncodingRegexp);\n                        if (match) {\n                            var sourceMapStart = match[0].length;\n                            var encodedSource = location.substr(sourceMapStart);\n                            var source = this._atob(encodedSource);\n                            this.sourceCache[location] = source;\n                            resolve(source);\n                        } else {\n                            reject(new Error('The encoding of the inline sourcemap is not supported'));\n                        }\n                    } else {\n                        var xhrPromise = this.ajax(location, {method: 'get'});\n                        // Cache the Promise to prevent duplicate in-flight requests\n                        this.sourceCache[location] = xhrPromise;\n                        xhrPromise.then(resolve, reject);\n                    }\n                }\n            }.bind(this));\n        };\n\n        /**\n         * Creating SourceMapConsumers is expensive, so this wraps the creation of a\n         * SourceMapConsumer in a per-instance cache.\n         *\n         * @param {String} sourceMappingURL = URL to fetch source map from\n         * @param {String} defaultSourceRoot = Default source root for source map if undefined\n         * @returns {Promise} that resolves a SourceMapConsumer\n         */\n        this._getSourceMapConsumer = function _getSourceMapConsumer(sourceMappingURL, defaultSourceRoot) {\n            return new Promise(function(resolve) {\n                if (this.sourceMapConsumerCache[sourceMappingURL]) {\n                    resolve(this.sourceMapConsumerCache[sourceMappingURL]);\n                } else {\n                    var sourceMapConsumerPromise = new Promise(function(resolve, reject) {\n                        return this._get(sourceMappingURL).then(function(sourceMapSource) {\n                            if (typeof sourceMapSource === 'string') {\n                                sourceMapSource = _parseJson(sourceMapSource.replace(/^\\)\\]\\}'/, ''));\n                            }\n                            if (typeof sourceMapSource.sourceRoot === 'undefined') {\n                                sourceMapSource.sourceRoot = defaultSourceRoot;\n                            }\n\n                            resolve(new SourceMap.SourceMapConsumer(sourceMapSource));\n                        }, reject);\n                    }.bind(this));\n                    this.sourceMapConsumerCache[sourceMappingURL] = sourceMapConsumerPromise;\n                    resolve(sourceMapConsumerPromise);\n                }\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, enhance function name and use source maps for a\n         * better StackFrame.\n         *\n         * @param {StackFrame} stackframe object\n         * @returns {Promise} that resolves with with source-mapped StackFrame\n         */\n        this.pinpoint = function StackTraceGPS$$pinpoint(stackframe) {\n            return new Promise(function(resolve, reject) {\n                this.getMappedLocation(stackframe).then(function(mappedStackFrame) {\n                    function resolveMappedStackFrame() {\n                        resolve(mappedStackFrame);\n                    }\n\n                    this.findFunctionName(mappedStackFrame)\n                        .then(resolve, resolveMappedStackFrame)\n                        // eslint-disable-next-line no-unexpected-multiline\n                        ['catch'](resolveMappedStackFrame);\n                }.bind(this), reject);\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, guess function name from location information.\n         *\n         * @param {StackFrame} stackframe\n         * @returns {Promise} that resolves with enhanced StackFrame.\n         */\n        this.findFunctionName = function StackTraceGPS$$findFunctionName(stackframe) {\n            return new Promise(function(resolve, reject) {\n                _ensureStackFrameIsLegit(stackframe);\n                this._get(stackframe.fileName).then(function getSourceCallback(source) {\n                    var lineNumber = stackframe.lineNumber;\n                    var columnNumber = stackframe.columnNumber;\n                    var guessedFunctionName = _findFunctionName(source, lineNumber, columnNumber);\n                    // Only replace functionName if we found something\n                    if (guessedFunctionName) {\n                        resolve(new StackFrame({\n                            functionName: guessedFunctionName,\n                            args: stackframe.args,\n                            fileName: stackframe.fileName,\n                            lineNumber: lineNumber,\n                            columnNumber: columnNumber\n                        }));\n                    } else {\n                        resolve(stackframe);\n                    }\n                }, reject)['catch'](reject);\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, seek source-mapped location and return new enhanced StackFrame.\n         *\n         * @param {StackFrame} stackframe\n         * @returns {Promise} that resolves with enhanced StackFrame.\n         */\n        this.getMappedLocation = function StackTraceGPS$$getMappedLocation(stackframe) {\n            return new Promise(function(resolve, reject) {\n                _ensureSupportedEnvironment();\n                _ensureStackFrameIsLegit(stackframe);\n\n                var sourceCache = this.sourceCache;\n                var fileName = stackframe.fileName;\n                this._get(fileName).then(function(source) {\n                    var sourceMappingURL = _findSourceMappingURL(source);\n                    var isDataUrl = sourceMappingURL.substr(0, 5) === 'data:';\n                    var defaultSourceRoot = fileName.substring(0, fileName.lastIndexOf('/') + 1);\n\n                    if (sourceMappingURL[0] !== '/' && !isDataUrl && !(/^https?:\\/\\/|^\\/\\//i).test(sourceMappingURL)) {\n                        sourceMappingURL = defaultSourceRoot + sourceMappingURL;\n                    }\n\n                    return this._getSourceMapConsumer(sourceMappingURL, defaultSourceRoot)\n                        .then(function(sourceMapConsumer) {\n                            return _extractLocationInfoFromSourceMapSource(stackframe, sourceMapConsumer, sourceCache)\n                                .then(resolve)['catch'](function() {\n                                    resolve(stackframe);\n                                });\n                        });\n                }.bind(this), reject)['catch'](reject);\n            }.bind(this));\n        };\n    };\n}));\n", "// Polyfill for old browsers\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/isArray\nif (!Array.isArray) {\n    Array.isArray = function(arg) {\n        return Object.prototype.toString.call(arg) === '[object Array]';\n    };\n}\n\nif (typeof Promise === 'undefined') {\n    ES6Promise.polyfill();\n}\n\n// ES5 Polyfills\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/bind\nif (!Function.prototype.bind) {\n    Function.prototype.bind = function(oThis) {\n        if (typeof this !== 'function') {\n            throw new TypeError('Function.prototype.bind - what is trying to be bound is not callable');\n        }\n\n        var aArgs = Array.prototype.slice.call(arguments, 1);\n        var fToBind = this;\n        var NoOp = function() {\n        };\n        var fBound = function() {\n            return fToBind.apply(this instanceof NoOp && oThis ? this : oThis,\n                aArgs.concat(Array.prototype.slice.call(arguments)));\n        };\n\n        NoOp.prototype = this.prototype;\n        fBound.prototype = new NoOp();\n\n        return fBound;\n    };\n}\n\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/map\nif (!Array.prototype.map) {\n    Array.prototype.map = function(callback, thisArg) {\n        if (this === void 0 || this === null) {\n            throw new TypeError('this is null or not defined');\n        }\n        var O = Object(this);\n        var len = O.length >>> 0;\n        var T;\n        if (typeof callback !== 'function') {\n            throw new TypeError(callback + ' is not a function');\n        }\n        if (arguments.length > 1) {\n            T = thisArg;\n        }\n\n        var A = new Array(len);\n        var k = 0;\n\n        while (k < len) {\n            var kValue;\n            var mappedValue;\n            if (k in O) {\n                kValue = O[k];\n                mappedValue = callback.call(T, kValue, k, O);\n                A[k] = mappedValue;\n            }\n            k++;\n        }\n\n        return A;\n    };\n}\n\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/filter\nif (!Array.prototype.filter) {\n    Array.prototype.filter = function(callback/*, thisArg*/) {\n        if (this === void 0 || this === null) {\n            throw new TypeError('this is null or not defined');\n        }\n\n        var t = Object(this);\n        var len = t.length >>> 0;\n        if (typeof callback !== 'function') {\n            throw new TypeError(callback + ' is not a function');\n        }\n\n        var res = [];\n        var thisArg = arguments.length >= 2 ? arguments[1] : void 0;\n        for (var i = 0; i < len; i++) {\n            if (i in t) {\n                var val = t[i];\n                if (callback.call(thisArg, val, i, t)) {\n                    res.push(val);\n                }\n            }\n        }\n\n        return res;\n    };\n}\n\n// See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/forEach\nif (!Array.prototype.forEach) {\n    Array.prototype.forEach = function(callback, thisArg) {\n        var T;\n        var k;\n        if (this === null || this === undefined) {\n            throw new TypeError(' this is null or not defined');\n        }\n\n        var O = Object(this);\n        var len = O.length >>> 0;\n        if (typeof callback !== 'function') {\n            throw new TypeError(callback + ' is not a function');\n        }\n\n        if (arguments.length > 1) {\n            T = thisArg;\n        }\n        k = 0;\n        while (k < len) {\n            var kValue;\n            if (k in O) {\n                kValue = O[k];\n                callback.call(T, kValue, k, O);\n            }\n            k++;\n        }\n    };\n}\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stacktrace', ['error-stack-parser', 'stack-generator', 'stacktrace-gps'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('error-stack-parser'), require('stack-generator'), require('stacktrace-gps'));\n    } else {\n        root.StackTrace = factory(root.ErrorStackParser, root.StackGenerator, root.StackTraceGPS);\n    }\n}(this, function StackTrace(ErrorStackParser, StackGenerator, StackTraceGPS) {\n    var _options = {\n        filter: function(stackframe) {\n            // Filter out stackframes for this library by default\n            return (stackframe.functionName || '').indexOf('StackTrace$$') === -1 &&\n                (stackframe.functionName || '').indexOf('ErrorStackParser$$') === -1 &&\n                (stackframe.functionName || '').indexOf('StackTraceGPS$$') === -1 &&\n                (stackframe.functionName || '').indexOf('StackGenerator$$') === -1;\n        },\n        sourceCache: {}\n    };\n\n    var _generateError = function StackTrace$$GenerateError() {\n        try {\n            // Error must be thrown to get stack in IE\n            throw new Error();\n        } catch (err) {\n            return err;\n        }\n    };\n\n    /**\n     * Merge 2 given Objects. If a conflict occurs the second object wins.\n     * Does not do deep merges.\n     *\n     * @param {Object} first base object\n     * @param {Object} second overrides\n     * @returns {Object} merged first and second\n     * @private\n     */\n    function _merge(first, second) {\n        var target = {};\n\n        [first, second].forEach(function(obj) {\n            for (var prop in obj) {\n                if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n                    target[prop] = obj[prop];\n                }\n            }\n            return target;\n        });\n\n        return target;\n    }\n\n    function _isShapedLikeParsableError(err) {\n        return err.stack || err['opera#sourceloc'];\n    }\n\n    function _filtered(stackframes, filter) {\n        if (typeof filter === 'function') {\n            return stackframes.filter(filter);\n        }\n        return stackframes;\n    }\n\n    return {\n        /**\n         * Get a backtrace from invocation point.\n         *\n         * @param {Object} opts\n         * @returns {Array} of StackFrame\n         */\n        get: function StackTrace$$get(opts) {\n            var err = _generateError();\n            return _isShapedLikeParsableError(err) ? this.fromError(err, opts) : this.generateArtificially(opts);\n        },\n\n        /**\n         * Get a backtrace from invocation point.\n         * IMPORTANT: Does not handle source maps or guess function names!\n         *\n         * @param {Object} opts\n         * @returns {Array} of StackFrame\n         */\n        getSync: function StackTrace$$getSync(opts) {\n            opts = _merge(_options, opts);\n            var err = _generateError();\n            var stack = _isShapedLikeParsableError(err) ? ErrorStackParser.parse(err) : StackGenerator.backtrace(opts);\n            return _filtered(stack, opts.filter);\n        },\n\n        /**\n         * Given an error object, parse it.\n         *\n         * @param {Error} error object\n         * @param {Object} opts\n         * @returns {Promise} for Array[StackFrame}\n         */\n        fromError: function StackTrace$$fromError(error, opts) {\n            opts = _merge(_options, opts);\n            var gps = new StackTraceGPS(opts);\n            return new Promise(function(resolve) {\n                var stackframes = _filtered(ErrorStackParser.parse(error), opts.filter);\n                resolve(Promise.all(stackframes.map(function(sf) {\n                    return new Promise(function(resolve) {\n                        function resolveOriginal() {\n                            resolve(sf);\n                        }\n\n                        gps.pinpoint(sf).then(resolve, resolveOriginal)['catch'](resolveOriginal);\n                    });\n                })));\n            }.bind(this));\n        },\n\n        /**\n         * Use StackGenerator to generate a backtrace.\n         *\n         * @param {Object} opts\n         * @returns {Promise} of Array[StackFrame]\n         */\n        generateArtificially: function StackTrace$$generateArtificially(opts) {\n            opts = _merge(_options, opts);\n            var stackFrames = StackGenerator.backtrace(opts);\n            if (typeof opts.filter === 'function') {\n                stackFrames = stackFrames.filter(opts.filter);\n            }\n            return Promise.resolve(stackFrames);\n        },\n\n        /**\n         * Given a function, wrap it such that invocations trigger a callback that\n         * is called with a stack trace.\n         *\n         * @param {Function} fn to be instrumented\n         * @param {Function} callback function to call with a stack trace on invocation\n         * @param {Function} errback optional function to call with error if unable to get stack trace.\n         * @param {Object} thisArg optional context object (e.g. window)\n         */\n        instrument: function StackTrace$$instrument(fn, callback, errback, thisArg) {\n            if (typeof fn !== 'function') {\n                throw new Error('Cannot instrument non-function object');\n            } else if (typeof fn.__stacktraceOriginalFn === 'function') {\n                // Already instrumented, return given Function\n                return fn;\n            }\n\n            var instrumented = function StackTrace$$instrumented() {\n                try {\n                    this.get().then(callback, errback)['catch'](errback);\n                    return fn.apply(thisArg || this, arguments);\n                } catch (e) {\n                    if (_isShapedLikeParsableError(e)) {\n                        this.fromError(e).then(callback, errback)['catch'](errback);\n                    }\n                    throw e;\n                }\n            }.bind(this);\n            instrumented.__stacktraceOriginalFn = fn;\n\n            return instrumented;\n        },\n\n        /**\n         * Given a function that has been instrumented,\n         * revert the function to it's original (non-instrumented) state.\n         *\n         * @param {Function} fn to de-instrument\n         */\n        deinstrument: function StackTrace$$deinstrument(fn) {\n            if (typeof fn !== 'function') {\n                throw new Error('Cannot de-instrument non-function object');\n            } else if (typeof fn.__stacktraceOriginalFn === 'function') {\n                return fn.__stacktraceOriginalFn;\n            } else {\n                // Function not instrumented, return original\n                return fn;\n            }\n        },\n\n        /**\n         * Given an error message and Array of StackFrames, serialize and POST to given URL.\n         *\n         * @param {Array} stackframes\n         * @param {String} url\n         * @param {String} errorMsg\n         * @param {Object} requestOptions\n         */\n        report: function StackTrace$$report(stackframes, url, errorMsg, requestOptions) {\n            return new Promise(function(resolve, reject) {\n                var req = new XMLHttpRequest();\n                req.onerror = reject;\n                req.onreadystatechange = function onreadystatechange() {\n                    if (req.readyState === 4) {\n                        if (req.status >= 200 && req.status < 400) {\n                            resolve(req.responseText);\n                        } else {\n                            reject(new Error('POST to ' + url + ' failed with status: ' + req.status));\n                        }\n                    }\n                };\n                req.open('post', url);\n\n                // Set request headers\n                req.setRequestHeader('Content-Type', 'application/json');\n                if (requestOptions && typeof requestOptions.headers === 'object') {\n                    var headers = requestOptions.headers;\n                    for (var header in headers) {\n                        if (Object.prototype.hasOwnProperty.call(headers, header)) {\n                            req.setRequestHeader(header, headers[header]);\n                        }\n                    }\n                }\n\n                var reportPayload = {stack: stackframes};\n                if (errorMsg !== undefined && errorMsg !== null) {\n                    reportPayload.message = errorMsg;\n                }\n\n                req.send(JSON.stringify(reportPayload));\n            });\n        }\n    };\n}));\n"]}