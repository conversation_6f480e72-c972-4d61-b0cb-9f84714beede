{"name": "scrollreveal", "version": "4.0.9", "description": "Animate elements as they scroll into view", "homepage": "https://scrollrevealjs.org", "main": "dist/scrollreveal.js", "module": "dist/scrollreveal.es.js", "jsnext:main": "dist/scrollreveal.es.js", "files": ["dist"], "scripts": {"prebuild": "rm -rf dist/*", "build": "npm run bundle && npm run bundle:min", "bundle": "./node_modules/rollup/bin/rollup -c ./build/rollup.conf.js", "bundle:min": "./node_modules/rollup/bin/rollup -c ./build/rollup.conf.min.js", "lint": "./node_modules/eslint/bin/eslint.js src test", "pretest": "rm -rf .ignore/coverage/**/ && npm run lint", "test": "./node_modules/karma/bin/karma start ./test/karma.conf.js", "testing": "cross-env COVERAGE=true npm test -- --no-single-run", "coverage": "cross-env COVERAGE=true npm test", "sandbox:bundle": "./node_modules/rollup/bin/rollup -w -c ./.ignore/sandbox/rollup.conf.sandbox.js", "sandbox:server": "node ./.ignore/sandbox/server.sandbox.js", "coverage:server": "node ./.ignore/coverage/server.coverage.js", "postinstall": "node -e \"console.log('\\u001b[35m\\u001b[1mLove ScrollReveal? 🔑  Buy a license!\\u001b[22m\\u001b[35m\\n >> \\u001b[33mhttps://scrollrevealjs.org/pricing/\\u001b[0m\\n')\""}, "keywords": ["scroll", "animation", "reveal", "css", "transform", "transition"], "repository": {"type": "git", "url": "https://github.com/jlmakes/scrollreveal.git"}, "bugs": {"url": "https://github.com/jlmakes/scrollreveal/issues"}, "dependencies": {"miniraf": "1.0.0", "rematrix": "0.3.0", "tealight": "0.3.6"}, "devDependencies": {"chai": "^4.1.2", "cross-env": "^5.1.3", "eslint": "^4.16.0", "karma": "^2.0.0", "karma-chrome-launcher": "^2.0.0", "karma-coverage": "^1.1.1", "karma-coveralls": "^1.1.2", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-rollup-preprocessor": "^5.1.1", "karma-sauce-launcher": "^1.1.0", "karma-sinon-chai": "^1.3.3", "live-server": "jlmakes/live-server", "mocha": "^4.0.1", "rollup": "^0.55.0", "rollup-plugin-buble": "^0.x", "rollup-plugin-istanbul": "^1.1.0", "rollup-plugin-json": "^2.1.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-strip": "^1.1.1", "rollup-plugin-uglify": "^2.0.1", "rollup-watch": "^4.3.1", "sinon": "^4.2.0", "sinon-chai": "^2.8.0"}, "author": "<PERSON>", "license": "GPL-3.0"}