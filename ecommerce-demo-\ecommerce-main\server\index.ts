import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes.js";
import { setupVite, serveStatic, log } from "./vite.js";
import dotenv from 'dotenv';
import { pingDatabase } from "./db.js";
import { dbStorage } from "./database-storage.js";
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Set up static file serving
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Try multiple public directories
const publicDirs = [
  path.join(process.cwd(), 'public'),
  path.join(__dirname, 'public'),
  path.join(process.cwd(), 'server/public')
];

let publicDir = '';
for (const dir of publicDirs) {
  if (fs.existsSync(dir)) {
    publicDir = dir;
    console.log(`Found public directory at ${dir}`);
    break;
  }
}

if (!publicDir) {
  publicDir = path.join(process.cwd(), 'public');
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
    console.log(`Created public directory at ${publicDir}`);
  }
}

// Serve static files from the public directory
app.use(express.static(publicDir));
console.log(`Serving static files from ${publicDir}`);

// Add a simple test endpoint
app.get('/test', (req, res) => {
  res.send('Server is running correctly! You can access this endpoint.');
});

// Serve index.html for the root path if it exists
const indexPath = path.join(publicDir, 'index.html');
if (fs.existsSync(indexPath)) {
  app.get('/', (req, res) => {
    res.sendFile(indexPath);
    console.log('Serving index.html for root path');
  });
}

// Add CORS headers to allow images to load properly from external domains
app.use((req, res, next) => {
  // Allow requests from any origin for images and other static content
  res.header('Access-Control-Allow-Origin', '*');

  // Allow specific headers
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  // Allow specific methods
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

// Export the app for use in api/index.ts
export { app };

// Export the initializeApp function for use in api/index.ts
export async function initializeApp() {
  try {
    // Check database connection
    await pingDatabase();
    console.log("Database connection successful");

    // Initialize the database storage (seed if needed)
    await dbStorage.initialize();
    console.log("Database initialized successfully");
  } catch (error) {
    console.error("Database initialization error:", error);
    process.exit(1);
  }

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Serve the app on the port defined by Vercel or default to 5000
  const port = Number(process.env.PORT) || 5000;
  // Bind to 0.0.0.0 and handle port in use errors by trying the next port
  function listenOnPort(p: number, maxAttempts = 5, attempts = 0) {
    if (attempts >= maxAttempts) {
      log(`Could not find an available port after ${maxAttempts} attempts`);
      process.exit(1);
    }

    server.on("error", (err: any) => {
      if (err.code === "EADDRINUSE") {
        log(`Port ${p} in use, trying ${p + 1}`);
        // Remove existing error listeners to avoid duplicates
        server.removeAllListeners("error");
        listenOnPort(p + 1, maxAttempts, attempts + 1);
      } else {
        console.error("Server error:", err);
        process.exit(1);
      }
    });

    // Bind to all interfaces (0.0.0.0) to ensure it's accessible from localhost
    server.listen(p, "0.0.0.0", () => {
      log(`serving on port ${p}`);
    });
  }

  listenOnPort(port);
}

// Initialize the app if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  initializeApp().catch(error => {
    console.error('Failed to initialize app:', error);
    process.exit(1);
  });
}
