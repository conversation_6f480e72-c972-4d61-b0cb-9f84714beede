/*
  @license
	Rollup.js v4.24.4
	Mon, 04 Nov 2024 08:46:36 GMT - commit cdf34ab5411aac6ac3f6cd21b10d2e58427e88ec

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map
