{"version": 3, "sources": ["node_modules/browser-pack/_prelude.js", "node_modules/error-stack-parser/error-stack-parser.js", "stacktrace.js", "node_modules/error-stack-parser/node_modules/stackframe/stackframe.js", "node_modules/source-map/lib/array-set.js", "node_modules/source-map/lib/base64-vlq.js", "node_modules/source-map/lib/base64.js", "node_modules/source-map/lib/binary-search.js", "node_modules/source-map/lib/quick-sort.js", "node_modules/source-map/lib/source-map-consumer.js", "node_modules/source-map/lib/util.js", "node_modules/stack-generator/stack-generator.js", "node_modules/stacktrace-gps/stacktrace-gps.js"], "names": ["f", "exports", "module", "define", "amd", "g", "window", "global", "self", "this", "StackTrace", "r", "e", "n", "t", "o", "i", "c", "require", "u", "a", "Error", "code", "p", "call", "length", "1", "root", "factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StackFrame", "FIREFOX_SAFARI_STACK_REGEXP", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "parse", "error", "stacktrace", "parseOpera", "stack", "match", "parseV8OrIE", "parseFFOr<PERSON><PERSON><PERSON>", "extractLocation", "urlLike", "indexOf", "regExp", "parts", "exec", "replace", "undefined", "filtered", "split", "filter", "line", "map", "sanitizedLine", "location", "tokens", "slice", "locationParts", "pop", "functionName", "join", "fileName", "lineNumber", "columnNumber", "source", "functionNameRegex", "matches", "message", "parseOpera9", "parseOpera11", "parseOpera10", "lineRE", "lines", "result", "len", "push", "argsRaw", "functionCall", "shift", "args", "stackframe", "2", "_isNumber", "isNaN", "parseFloat", "isFinite", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "substring", "_getter", "obj", "props", "booleanProps", "numericProps", "stringProps", "arrayProps", "concat", "prototype", "getArgs", "set<PERSON>rgs", "v", "Object", "toString", "TypeError", "getEval<PERSON><PERSON>in", "eval<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getFileName", "getLineNumber", "getColumnNumber", "getFunctionName", "getIsEval", "fromString", "argsStartIndex", "argsEndIndex", "lastIndexOf", "locationString", "Boolean", "j", "Number", "k", "String", "3", "ArraySet", "_array", "_set", "create", "util", "has", "hasOwnProperty", "fromArray", "aArray", "aAllowDuplicates", "set", "add", "size", "getOwnPropertyNames", "aStr", "sStr", "toSetString", "isDuplicate", "idx", "at", "aIdx", "toArray", "./util", "4", "toVLQSigned", "aValue", "fromVLQSigned", "isNegative", "shifted", "base64", "VLQ_BASE_SHIFT", "VLQ_BASE", "VLQ_BASE_MASK", "VLQ_CONTINUATION_BIT", "encode", "digit", "encoded", "vlq", "decode", "aIndex", "aOutParam", "continuation", "strLen", "charCodeAt", "value", "rest", "./base64", "5", "intToCharMap", "number", "charCode", "bigA", "bigZ", "littleA", "littleZ", "zero", "nine", "plus", "slash", "littleOffset", "numberOffset", "6", "recursiveSearch", "aLow", "aHigh", "aNeedle", "aHaystack", "aCompare", "aBias", "mid", "Math", "floor", "cmp", "LEAST_UPPER_BOUND", "GREATEST_LOWER_BOUND", "search", "index", "7", "swap", "ary", "x", "y", "temp", "randomIntInRange", "low", "high", "round", "random", "doQuickSort", "comparator", "pivotIndex", "pivot", "q", "quickSort", "8", "SourceMapConsumer", "aSourceMap", "sourceMap", "JSON", "sections", "IndexedSourceMapConsumer", "BasicSourceMapConsumer", "version", "getArg", "sources", "names", "sourceRoot", "sourcesContent", "mappings", "file", "_version", "normalize", "isAbsolute", "relative", "_names", "_sources", "_mappings", "Mapping", "generatedLine", "generatedColumn", "originalLine", "originalColumn", "name", "lastOffset", "column", "_sections", "s", "url", "offset", "offsetLine", "offsetColumn", "generatedOffset", "consumer", "binarySearch", "base64VLQ", "fromSourceMap", "__generatedMappings", "defineProperty", "get", "_parseMappings", "__originalMappings", "_charIsMappingSeparator", "aSourceRoot", "GENERATED_ORDER", "ORIGINAL_ORDER", "eachMapping", "aCallback", "aContext", "aOrder", "context", "order", "_generatedMappings", "_originalMappings", "mapping", "for<PERSON>ach", "allGeneratedPositionsFor", "aArgs", "needle", "_findMapping", "compareByOriginalPositions", "lastColumn", "smc", "_sourceRoot", "_generateSourcesContent", "_file", "generatedMappings", "destGeneratedMappings", "destOriginalMappings", "srcMapping", "destMapping", "segment", "end", "previousGeneratedColumn", "previousOriginalLine", "previousOriginalColumn", "previousSource", "previousName", "cachedSegments", "originalMappings", "compareByGeneratedPositionsDeflated", "aMappings", "aLineName", "aColumnName", "aComparator", "computeColumnSpans", "nextMapping", "lastGeneratedColumn", "Infinity", "originalPositionFor", "hasContentsOfAllSources", "some", "sc", "sourceContentFor", "aSource", "nullOnMissing", "urlParse", "fileUriAbsPath", "scheme", "path", "generatedPositionFor", "constructor", "sectionIndex", "section", "bias", "every", "content", "generatedPosition", "ret", "sectionMappings", "adjustedMapping", "./array-set", "./base64-vlq", "./binary-search", "./quick-sort", "9", "aName", "aDefaultValue", "arguments", "aUrl", "urlRegexp", "auth", "host", "port", "urlGenerate", "aParsedUrl", "a<PERSON><PERSON>", "part", "up", "splice", "aRoot", "aPathUrl", "aRootUrl", "dataUrlRegexp", "joined", "level", "Array", "substr", "identity", "isProtoString", "fromSetString", "mappingA", "mappingB", "onlyCompareOriginal", "onlyCompareGenerated", "strcmp", "aStr1", "aStr2", "compareByGeneratedPositionsInflated", "supportsNullProto", "10", "apply", "dup", "11", "StackGenerator", "backtrace", "opts", "maxStackSize", "curr", "callee", "test", "RegExp", "$1", "caller", "12", "13", "StackTraceGPS", "SourceMap", "_xdr", "Promise", "resolve", "reject", "req", "XMLHttpRequest", "open", "onerror", "onreadystatechange", "readyState", "status", "responseText", "send", "_atob", "b64str", "atob", "_parseJson", "string", "_findFunctionName", "syntaxes", "maxLines", "min", "commentPos", "m", "_ensureSupportedEnvironment", "_ensureStackFrameIsLegit", "_findSourceMappingURL", "lastSourceMappingUrl", "matchSourceMappingUrl", "sourceMappingUrlRegExp", "_extractLocationInfoFromSourceMapSource", "sourceMapConsumer", "sourceCache", "loc", "mappedSource", "sourceMapConsumerCache", "ajax", "_get", "isDataUrl", "offline", "supportedEncodingRegexp", "sourceMapStart", "encodedSource", "xhrPromise", "method", "then", "bind", "_getSourceMapConsumer", "sourceMappingURL", "defaultSourceRoot", "sourceMapConsumerPromise", "sourceMapSource", "pinpoint", "getMappedLocation", "mappedStackFrame", "resolveMappedStackFrame", "findFunctionName", "guessedFunctionName", "source-map/lib/source-map-consumer", "14", "_merge", "first", "second", "target", "prop", "_isShapedLikeParsableError", "err", "_filtered", "stackframes", "_options", "_generateError", "fromError", "generateArtificially", "getSync", "gps", "all", "sf", "resolveOriginal", "stackFrames", "instrument", "fn", "callback", "errback", "thisArg", "__stacktraceOriginalFn", "instrumented", "deinstrument", "report", "errorMsg", "requestOptions", "setRequestHeader", "headers", "header", "reportPayload", "stringify", "error-stack-parser", "stack-generator", "stacktrace-gps"], "mappings": "CAAA,SAAAA,GAAA,GAAA,gBAAAC,UAAA,mBAAAC,QAAAA,OAAAD,QAAAD,QAAA,IAAA,kBAAAG,SAAAA,OAAAC,IAAAD,UAAAH,OAAA,CAAA,GAAAK,EAAAA,GAAA,mBAAAC,QAAAA,OAAA,mBAAAC,QAAAA,OAAA,mBAAAC,MAAAA,KAAAC,KAAAJ,EAAAK,WAAAV,MAAA,WAAA,GAAAG,EAAA,OAAA,YAAA,QAAAQ,GAAAC,EAAAC,EAAAC,GAAA,QAAAC,GAAAC,EAAAhB,GAAA,IAAAa,EAAAG,GAAA,CAAA,IAAAJ,EAAAI,GAAA,CAAA,GAAAC,GAAA,kBAAAC,UAAAA,OAAA,KAAAlB,GAAAiB,EAAA,MAAAA,GAAAD,GAAA,EAAA,IAAAG,EAAA,MAAAA,GAAAH,GAAA,EAAA,IAAAI,GAAA,GAAAC,OAAA,uBAAAL,EAAA,IAAA,MAAAI,GAAAE,KAAA,mBAAAF,EAAA,GAAAG,GAAAV,EAAAG,IAAAf,WAAAW,GAAAI,GAAA,GAAAQ,KAAAD,EAAAtB,QAAA,SAAAU,GAAA,GAAAE,GAAAD,EAAAI,GAAA,GAAAL,EAAA,OAAAI,GAAAF,GAAAF,IAAAY,EAAAA,EAAAtB,QAAAU,EAAAC,EAAAC,EAAAC,GAAA,MAAAD,GAAAG,GAAAf,QAAA,IAAA,GAAAkB,GAAA,kBAAAD,UAAAA,QAAAF,EAAA,EAAAA,EAAAF,EAAAW,OAAAT,IAAAD,EAAAD,EAAAE,GAAA,OAAAD,GAAA,MAAAJ,OAAAe,GAAA,SAAAR,EAAAhB,EAAAD,ICAA,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,sBAAA,cAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,EAAAV,EAAA,eAEAS,EAAAE,iBAAAD,EAAAD,EAAAG,aAEArB,KAAA,SAAAqB,GACA,YAEA,IAAAC,GAAA,eACAC,EAAA,iCACAC,EAAA,6BAEA,QAOAC,MAAA,SAAAC,GACA,GAAA,mBAAAA,GAAAC,YAAA,mBAAAD,GAAA,mBACA,MAAA1B,MAAA4B,WAAAF,EACA,IAAAA,EAAAG,OAAAH,EAAAG,MAAAC,MAAAP,GACA,MAAAvB,MAAA+B,YAAAL,EACA,IAAAA,EAAAG,MACA,MAAA7B,MAAAgC,gBAAAN,EAEA,MAAA,IAAAd,OAAA,oCAKAqB,gBAAA,SAAAC,GAEA,GAAAA,EAAAC,QAAA,UACA,OAAAD,EAGA,IAAAE,GAAA,+BACAC,EAAAD,EAAAE,KAAAJ,EAAAK,QAAA,QAAA,IACA,QAAAF,EAAA,GAAAA,EAAA,IAAAG,OAAAH,EAAA,IAAAG,SAGAT,YAAA,SAAAL,GACA,GAAAe,GAAAf,EAAAG,MAAAa,MAAA,MAAAC,OAAA,SAAAC,GACA,QAAAA,EAAAd,MAAAP,IACAvB,KAEA,OAAAyC,GAAAI,IAAA,SAAAD,GACAA,EAAAT,QAAA,eAEAS,EAAAA,EAAAL,QAAA,aAAA,QAAAA,QAAA,+BAAA,IAEA,IAAAO,GAAAF,EAAAL,QAAA,OAAA,IAAAA,QAAA,eAAA,KAIAQ,EAAAD,EAAAhB,MAAA,2BAGAgB,GAAAC,EAAAD,EAAAP,QAAAQ,EAAA,GAAA,IAAAD,CAEA,IAAAE,GAAAF,EAAAJ,MAAA,OAAAO,MAAA,GAEAC,EAAAlD,KAAAiC,gBAAAc,EAAAA,EAAA,GAAAC,EAAAG,OACAC,EAAAJ,EAAAK,KAAA,MAAAb,OACAc,GAAA,OAAA,eAAAnB,QAAAe,EAAA,OAAAV,OAAAU,EAAA,EAEA,OAAA,IAAA7B,IACA+B,aAAAA,EACAE,SAAAA,EACAC,WAAAL,EAAA,GACAM,aAAAN,EAAA,GACAO,OAAAb,KAEA5C,OAGAgC,gBAAA,SAAAN,GACA,GAAAe,GAAAf,EAAAG,MAAAa,MAAA,MAAAC,OAAA,SAAAC,GACA,OAAAA,EAAAd,MAAAN,IACAxB,KAEA,OAAAyC,GAAAI,IAAA,SAAAD,GAMA,GAJAA,EAAAT,QAAA,gBACAS,EAAAA,EAAAL,QAAA,mDAAA,QAGAK,EAAAT,QAAA,WAAAS,EAAAT,QAAA,UAEA,MAAA,IAAAd,IACA+B,aAAAR,GAGA,IAAAc,GAAA,6BACAC,EAAAf,EAAAd,MAAA4B,GACAN,EAAAO,GAAAA,EAAA,GAAAA,EAAA,GAAAnB,OACAU,EAAAlD,KAAAiC,gBAAAW,EAAAL,QAAAmB,EAAA,IAEA,OAAA,IAAArC,IACA+B,aAAAA,EACAE,SAAAJ,EAAA,GACAK,WAAAL,EAAA,GACAM,aAAAN,EAAA,GACAO,OAAAb,KAGA5C,OAGA4B,WAAA,SAAAzB,GACA,OAAAA,EAAAwB,YAAAxB,EAAAyD,QAAAzB,QAAA,UACAhC,EAAAyD,QAAAlB,MAAA,MAAA1B,OAAAb,EAAAwB,WAAAe,MAAA,MAAA1B,OACAhB,KAAA6D,YAAA1D,GACAA,EAAA0B,MAGA7B,KAAA8D,aAAA3D,GAFAH,KAAA+D,aAAA5D,IAMA0D,YAAA,SAAA1D,GAKA,IAAA,GAJA6D,GAAA,oCACAC,EAAA9D,EAAAyD,QAAAlB,MAAA,MACAwB,KAEA3D,EAAA,EAAA4D,EAAAF,EAAAjD,OAAAT,EAAA4D,EAAA5D,GAAA,EAAA,CACA,GAAAuB,GAAAkC,EAAA1B,KAAA2B,EAAA1D,GACAuB,IACAoC,EAAAE,KAAA,GAAA/C,IACAiC,SAAAxB,EAAA,GACAyB,WAAAzB,EAAA,GACA2B,OAAAQ,EAAA1D,MAKA,MAAA2D,IAGAH,aAAA,SAAA5D,GAKA,IAAA,GAJA6D,GAAA,6DACAC,EAAA9D,EAAAwB,WAAAe,MAAA,MACAwB,KAEA3D,EAAA,EAAA4D,EAAAF,EAAAjD,OAAAT,EAAA4D,EAAA5D,GAAA,EAAA,CACA,GAAAuB,GAAAkC,EAAA1B,KAAA2B,EAAA1D,GACAuB,IACAoC,EAAAE,KACA,GAAA/C,IACA+B,aAAAtB,EAAA,IAAAU,OACAc,SAAAxB,EAAA,GACAyB,WAAAzB,EAAA,GACA2B,OAAAQ,EAAA1D,MAMA,MAAA2D,IAIAJ,aAAA,SAAApC,GACA,GAAAe,GAAAf,EAAAG,MAAAa,MAAA,MAAAC,OAAA,SAAAC,GACA,QAAAA,EAAAd,MAAAR,KAAAsB,EAAAd,MAAA,sBACA9B,KAEA,OAAAyC,GAAAI,IAAA,SAAAD,GACA,GAMAyB,GANArB,EAAAJ,EAAAF,MAAA,KACAQ,EAAAlD,KAAAiC,gBAAAe,EAAAG,OACAmB,EAAAtB,EAAAuB,SAAA,GACAnB,EAAAkB,EACA/B,QAAA,iCAAA,MACAA,QAAA,aAAA,KAAAC,MAEA8B,GAAAxC,MAAA,iBACAuC,EAAAC,EAAA/B,QAAA,qBAAA,MAEA,IAAAiC,GAAAhC,SAAA6B,GAAA,8BAAAA,EACA7B,OAAA6B,EAAA3B,MAAA,IAEA,OAAA,IAAArB,IACA+B,aAAAA,EACAoB,KAAAA,EACAlB,SAAAJ,EAAA,GACAK,WAAAL,EAAA,GACAM,aAAAN,EAAA,GACAO,OAAAb,KAEA5C,YCMGyE,WAAa,IAAIC,GAAG,SAASjE,EAAQhB,EAAOD,IC5M/C,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,gBAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,IAEAD,EAAAG,WAAAF,KAEAnB,KAAA,WACA,YACA,SAAA2E,GAAAvE,GACA,OAAAwE,MAAAC,WAAAzE,KAAA0E,SAAA1E,GAGA,QAAA2E,GAAAC,GACA,MAAAA,GAAAC,OAAA,GAAAC,cAAAF,EAAAG,UAAA,GAGA,QAAAC,GAAAtE,GACA,MAAA,YACA,MAAAd,MAAAc,IAWA,QAAAO,GAAAgE,GACA,GAAAA,EACA,IAAA,GAAA9E,GAAA,EAAAA,EAAA+E,EAAAtE,OAAAT,IACAiC,SAAA6C,EAAAC,EAAA/E,KACAP,KAAA,MAAA+E,EAAAO,EAAA/E,KAAA8E,EAAAC,EAAA/E,KAXA,GAAAgF,IAAA,gBAAA,SAAA,WAAA,cACAC,GAAA,eAAA,cACAC,GAAA,WAAA,eAAA,UACAC,GAAA,QAEAJ,EAAAC,EAAAI,OAAAH,EAAAC,EAAAC,EAWArE,GAAAuE,WACAC,QAAA,WACA,MAAA7F,MAAAwE,MAEAsB,QAAA,SAAAC,GACA,GAAA,mBAAAC,OAAAJ,UAAAK,SAAAlF,KAAAgF,GACA,KAAA,IAAAG,WAAA,wBAEAlG,MAAAwE,KAAAuB,GAGAI,cAAA,WACA,MAAAnG,MAAAoG,YAEAC,cAAA,SAAAN,GACA,GAAAA,YAAA1E,GACArB,KAAAoG,WAAAL,MACA,CAAA,KAAAA,YAAAC,SAGA,KAAA,IAAAE,WAAA,8CAFAlG,MAAAoG,WAAA,GAAA/E,GAAA0E,KAMAE,SAAA,WACA,GAAA3C,GAAAtD,KAAAsG,eAAA,GACA/C,EAAAvD,KAAAuG,iBAAA,GACA/C,EAAAxD,KAAAwG,mBAAA,GACApD,EAAApD,KAAAyG,mBAAA,EACA,OAAAzG,MAAA0G,YACApD,EACA,WAAAA,EAAA,IAAAC,EAAA,IAAAC,EAAA,IAEA,UAAAD,EAAA,IAAAC,EAEAJ,EACAA,EAAA,KAAAE,EAAA,IAAAC,EAAA,IAAAC,EAAA,IAEAF,EAAA,IAAAC,EAAA,IAAAC,IAIAnC,EAAAsF,WAAA,SAAA3B,GACA,GAAA4B,GAAA5B,EAAA7C,QAAA,KACA0E,EAAA7B,EAAA8B,YAAA,KAEA1D,EAAA4B,EAAAG,UAAA,EAAAyB,GACApC,EAAAQ,EAAAG,UAAAyB,EAAA,EAAAC,GAAAnE,MAAA,KACAqE,EAAA/B,EAAAG,UAAA0B,EAAA,EAEA,IAAA,IAAAE,EAAA5E,QAAA,KACA,GAAAE,GAAA,gCAAAC,KAAAyE,EAAA,IACAzD,EAAAjB,EAAA,GACAkB,EAAAlB,EAAA,GACAmB,EAAAnB,EAAA,EAGA,OAAA,IAAAhB,IACA+B,aAAAA,EACAoB,KAAAA,GAAAhC,OACAc,SAAAA,EACAC,WAAAA,GAAAf,OACAgB,aAAAA,GAAAhB,SAIA,KAAA,GAAAjC,GAAA,EAAAA,EAAAgF,EAAAvE,OAAAT,IACAc,EAAAuE,UAAA,MAAAb,EAAAQ,EAAAhF,KAAA6E,EAAAG,EAAAhF,IACAc,EAAAuE,UAAA,MAAAb,EAAAQ,EAAAhF,KAAA,SAAAO,GACA,MAAA,UAAAiF,GACA/F,KAAAc,GAAAkG,QAAAjB,KAEAR,EAAAhF,GAGA,KAAA,GAAA0G,GAAA,EAAAA,EAAAzB,EAAAxE,OAAAiG,IACA5F,EAAAuE,UAAA,MAAAb,EAAAS,EAAAyB,KAAA7B,EAAAI,EAAAyB,IACA5F,EAAAuE,UAAA,MAAAb,EAAAS,EAAAyB,KAAA,SAAAnG,GACA,MAAA,UAAAiF,GACA,IAAApB,EAAAoB,GACA,KAAA,IAAAG,WAAApF,EAAA,oBAEAd,MAAAc,GAAAoG,OAAAnB,KAEAP,EAAAyB,GAGA,KAAA,GAAAE,GAAA,EAAAA,EAAA1B,EAAAzE,OAAAmG,IACA9F,EAAAuE,UAAA,MAAAb,EAAAU,EAAA0B,KAAA/B,EAAAK,EAAA0B,IACA9F,EAAAuE,UAAA,MAAAb,EAAAU,EAAA0B,KAAA,SAAArG,GACA,MAAA,UAAAiF,GACA/F,KAAAc,GAAAsG,OAAArB,KAEAN,EAAA0B,GAGA,OAAA9F,UDgNMgG,GAAG,SAAS5G,EAAQhB,EAAOD,GE5UjC,QAAA8H,KACAtH,KAAAuH,UACAvH,KAAAwH,KAAAxB,OAAAyB,OAAA,MAXA,GAAAC,GAAAjH,EAAA,UACAkH,EAAA3B,OAAAJ,UAAAgC,cAgBAN,GAAAO,UAAA,SAAAC,EAAAC,GAEA,IAAA,GADAC,GAAA,GAAAV,GACA/G,EAAA,EAAA4D,EAAA2D,EAAA9G,OAAAT,EAAA4D,EAAA5D,IACAyH,EAAAC,IAAAH,EAAAvH,GAAAwH,EAEA,OAAAC,IASAV,EAAA1B,UAAAsC,KAAA,WACA,MAAAlC,QAAAmC,oBAAAnI,KAAAwH,MAAAxG,QAQAsG,EAAA1B,UAAAqC,IAAA,SAAAG,EAAAL,GACA,GAAAM,GAAAX,EAAAY,YAAAF,GACAG,EAAAZ,EAAA5G,KAAAf,KAAAwH,KAAAa,GACAG,EAAAxI,KAAAuH,OAAAvG,MACAuH,KAAAR,GACA/H,KAAAuH,OAAAnD,KAAAgE,GAEAG,IACAvI,KAAAwH,KAAAa,GAAAG,IASAlB,EAAA1B,UAAA+B,IAAA,SAAAS,GACA,GAAAC,GAAAX,EAAAY,YAAAF,EACA,OAAAT,GAAA5G,KAAAf,KAAAwH,KAAAa,IAQAf,EAAA1B,UAAAzD,QAAA,SAAAiG,GACA,GAAAC,GAAAX,EAAAY,YAAAF,EACA,IAAAT,EAAA5G,KAAAf,KAAAwH,KAAAa,GACA,MAAArI,MAAAwH,KAAAa,EAEA,MAAA,IAAAzH,OAAA,IAAAwH,EAAA,yBAQAd,EAAA1B,UAAA6C,GAAA,SAAAC,GACA,GAAAA,GAAA,GAAAA,EAAA1I,KAAAuH,OAAAvG,OACA,MAAAhB,MAAAuH,OAAAmB,EAEA,MAAA,IAAA9H,OAAA,yBAAA8H,IAQApB,EAAA1B,UAAA+C,QAAA,WACA,MAAA3I,MAAAuH,OAAAtE,SAGAzD,EAAA8H,SAAAA,IF+VGsB,SAAS,IAAIC,GAAG,SAASpI,EAAQhB,EAAOD,GGlY3C,QAAAsJ,GAAAC,GACA,MAAAA,GAAA,IACAA,GAAA,GAAA,GACAA,GAAA,GAAA,EASA,QAAAC,GAAAD,GACA,GAAAE,GAAA,KAAA,EAAAF,GACAG,EAAAH,GAAA,CACA,OAAAE,IACAC,EACAA,EAhDA,GAAAC,GAAA1I,EAAA,YAcA2I,EAAA,EAGAC,EAAA,GAAAD,EAGAE,EAAAD,EAAA,EAGAE,EAAAF,CA+BA7J,GAAAgK,OAAA,SAAAT,GACA,GACAU,GADAC,EAAA,GAGAC,EAAAb,EAAAC,EAEA,GACAU,GAAAE,EAAAL,EACAK,KAAAP,EACAO,EAAA,IAGAF,GAAAF,GAEAG,GAAAP,EAAAK,OAAAC,SACAE,EAAA,EAEA,OAAAD,IAOAlK,EAAAoK,OAAA,SAAAxB,EAAAyB,EAAAC,GACA,GAGAC,GAAAN,EAHAO,EAAA5B,EAAApH,OACAkD,EAAA,EACAK,EAAA,CAGA,GAAA,CACA,GAAAsF,GAAAG,EACA,KAAA,IAAApJ,OAAA,6CAIA,IADA6I,EAAAN,EAAAS,OAAAxB,EAAA6B,WAAAJ,MACAJ,OACA,KAAA,IAAA7I,OAAA,yBAAAwH,EAAAnD,OAAA4E,EAAA,GAGAE,MAAAN,EAAAF,GACAE,GAAAH,EACApF,GAAAuF,GAAAlF,EACAA,GAAA6E,QACAW,EAEAD,GAAAI,MAAAlB,EAAA9E,GACA4F,EAAAK,KAAAN,KH0cGO,WAAW,IAAIC,GAAG,SAAS5J,EAAQhB,EAAOD,GI7kB7C,GAAA8K,GAAA,mEAAA5H,MAAA,GAKAlD,GAAAgK,OAAA,SAAAe,GACA,GAAA,GAAAA,GAAAA,EAAAD,EAAAtJ,OACA,MAAAsJ,GAAAC,EAEA,MAAA,IAAArE,WAAA,6BAAAqE,IAOA/K,EAAAoK,OAAA,SAAAY,GACA,GAAAC,GAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,IAEAC,EAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,GAEAC,EAAA,GACAC,EAAA,EAGA,OAAAT,IAAAD,GAAAA,GAAAE,EACAF,EAAAC,EAIAE,GAAAH,GAAAA,GAAAI,EACAJ,EAAAG,EAAAM,EAIAJ,GAAAL,GAAAA,GAAAM,EACAN,EAAAK,EAAAK,EAIAV,GAAAO,EACA,GAIAP,GAAAQ,EACA,YJ4lBMG,GAAG,SAAS1K,EAAQhB,EAAOD,GKloBjC,QAAA4L,GAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAUA,GAAAC,GAAAC,KAAAC,OAAAP,EAAAD,GAAA,GAAAA,EACAS,EAAAL,EAAAF,EAAAC,EAAAG,IAAA,EACA,OAAA,KAAAG,EAEAH,EAEAG,EAAA,EAEAR,EAAAK,EAAA,EAEAP,EAAAO,EAAAL,EAAAC,EAAAC,EAAAC,EAAAC,GAKAA,GAAAlM,EAAAuM,kBACAT,EAAAE,EAAAxK,OAAAsK,KAEAK,EAKAA,EAAAN,EAAA,EAEAD,EAAAC,EAAAM,EAAAJ,EAAAC,EAAAC,EAAAC,GAIAA,GAAAlM,EAAAuM,kBACAJ,EAEAN,EAAA,KAAAA,EA1DA7L,EAAAwM,qBAAA,EACAxM,EAAAuM,kBAAA,EAgFAvM,EAAAyM,OAAA,SAAAV,EAAAC,EAAAC,EAAAC,GACA,GAAA,IAAAF,EAAAxK,OACA,QAGA,IAAAkL,GAAAd,KAAAI,EAAAxK,OAAAuK,EAAAC,EACAC,EAAAC,GAAAlM,EAAAwM,qBACA,IAAAE,EAAA,EACA,QAMA,MAAAA,EAAA,GAAA,GACA,IAAAT,EAAAD,EAAAU,GAAAV,EAAAU,EAAA,IAAA,MAGAA,CAGA,OAAAA,SL6pBMC,GAAG,SAAS1L,EAAQhB,EAAOD,GM/uBjC,QAAA4M,GAAAC,EAAAC,EAAAC,GACA,GAAAC,GAAAH,EAAAC,EACAD,GAAAC,GAAAD,EAAAE,GACAF,EAAAE,GAAAC,EAWA,QAAAC,GAAAC,EAAAC,GACA,MAAAf,MAAAgB,MAAAF,EAAAd,KAAAiB,UAAAF,EAAAD,IAeA,QAAAI,GAAAT,EAAAU,EAAAjM,EAAAZ,GAKA,GAAAY,EAAAZ,EAAA,CAYA,GAAA8M,GAAAP,EAAA3L,EAAAZ,GACAK,EAAAO,EAAA,CAEAsL,GAAAC,EAAAW,EAAA9M,EASA,KAAA,GARA+M,GAAAZ,EAAAnM,GAQA+G,EAAAnG,EAAAmG,EAAA/G,EAAA+G,IACA8F,EAAAV,EAAApF,GAAAgG,IAAA,IACA1M,GAAA,EACA6L,EAAAC,EAAA9L,EAAA0G,GAIAmF,GAAAC,EAAA9L,EAAA,EAAA0G,EACA,IAAAiG,GAAA3M,EAAA,CAIAuM,GAAAT,EAAAU,EAAAjM,EAAAoM,EAAA,GACAJ,EAAAT,EAAAU,EAAAG,EAAA,EAAAhN,IAYAV,EAAA2N,UAAA,SAAAd,EAAAU,GACAD,EAAAT,EAAAU,EAAA,EAAAV,EAAArL,OAAA,SN8wBMoM,GAAG,SAAS3M,EAAQhB,EAAOD,GOj3BjC,QAAA6N,GAAAC,GACA,GAAAC,GAAAD,CAKA,OAJA,gBAAAA,KACAC,EAAAC,KAAA/L,MAAA6L,EAAA/K,QAAA,WAAA,MAGA,MAAAgL,EAAAE,SACA,GAAAC,GAAAH,GACA,GAAAI,GAAAJ,GAoQA,QAAAI,GAAAL,GACA,GAAAC,GAAAD,CACA,iBAAAA,KACAC,EAAAC,KAAA/L,MAAA6L,EAAA/K,QAAA,WAAA,KAGA,IAAAqL,GAAAlG,EAAAmG,OAAAN,EAAA,WACAO,EAAApG,EAAAmG,OAAAN,EAAA,WAGAQ,EAAArG,EAAAmG,OAAAN,EAAA,YACAS,EAAAtG,EAAAmG,OAAAN,EAAA,aAAA,MACAU,EAAAvG,EAAAmG,OAAAN,EAAA,iBAAA,MACAW,EAAAxG,EAAAmG,OAAAN,EAAA,YACAY,EAAAzG,EAAAmG,OAAAN,EAAA,OAAA,KAIA,IAAAK,GAAA5N,KAAAoO,SACA,KAAA,IAAAxN,OAAA,wBAAAgN,EAGAE,GAAAA,EACAjL,IAAAuE,QAIAvE,IAAA6E,EAAA2G,WAKAxL,IAAA,SAAAY,GACA,MAAAuK,IAAAtG,EAAA4G,WAAAN,IAAAtG,EAAA4G,WAAA7K,GACAiE,EAAA6G,SAAAP,EAAAvK,GACAA,IAOAzD,KAAAwO,OAAAlH,EAAAO,UAAAkG,EAAAlL,IAAAuE,SAAA,GACApH,KAAAyO,SAAAnH,EAAAO,UAAAiG,GAAA,GAEA9N,KAAAgO,WAAAA,EACAhO,KAAAiO,eAAAA,EACAjO,KAAA0O,UAAAR,EACAlO,KAAAmO,KAAAA,EA8EA,QAAAQ,KACA3O,KAAA4O,cAAA,EACA5O,KAAA6O,gBAAA,EACA7O,KAAAyD,OAAA,KACAzD,KAAA8O,aAAA,KACA9O,KAAA+O,eAAA,KACA/O,KAAAgP,KAAA,KAyZA,QAAAtB,GAAAJ,GACA,GAAAC,GAAAD,CACA,iBAAAA,KACAC,EAAAC,KAAA/L,MAAA6L,EAAA/K,QAAA,WAAA,KAGA,IAAAqL,GAAAlG,EAAAmG,OAAAN,EAAA,WACAE,EAAA/F,EAAAmG,OAAAN,EAAA,WAEA,IAAAK,GAAA5N,KAAAoO,SACA,KAAA,IAAAxN,OAAA,wBAAAgN,EAGA5N,MAAAyO,SAAA,GAAAnH,GACAtH,KAAAwO,OAAA,GAAAlH,EAEA,IAAA2H,IACArM,QACAsM,OAAA,EAEAlP,MAAAmP,UAAA1B,EAAA5K,IAAA,SAAAuM,GACA,GAAAA,EAAAC,IAGA,KAAA,IAAAzO,OAAA,qDAEA,IAAA0O,GAAA5H,EAAAmG,OAAAuB,EAAA,UACAG,EAAA7H,EAAAmG,OAAAyB,EAAA,QACAE,EAAA9H,EAAAmG,OAAAyB,EAAA,SAEA,IAAAC,EAAAN,EAAArM,MACA2M,IAAAN,EAAArM,MAAA4M,EAAAP,EAAAC,OACA,KAAA,IAAAtO,OAAA,uDAIA,OAFAqO,GAAAK,GAGAG,iBAGAb,cAAAW,EAAA,EACAV,gBAAAW,EAAA,GAEAE,SAAA,GAAArC,GAAA3F,EAAAmG,OAAAuB,EAAA,WA11BA,GAAA1H,GAAAjH,EAAA,UACAkP,EAAAlP,EAAA,mBACA6G,EAAA7G,EAAA,eAAA6G,SACAsI,EAAAnP,EAAA,gBACA0M,EAAA1M,EAAA,gBAAA0M,SAaAE,GAAAwC,cAAA,SAAAvC,GACA,MAAAK,GAAAkC,cAAAvC,IAMAD,EAAAzH,UAAAwI,SAAA,EAgCAf,EAAAzH,UAAAkK,oBAAA,KACA9J,OAAA+J,eAAA1C,EAAAzH,UAAA,sBACAoK,IAAA,WAKA,MAJAhQ,MAAA8P,qBACA9P,KAAAiQ,eAAAjQ,KAAA0O,UAAA1O,KAAAgO,YAGAhO,KAAA8P,uBAIAzC,EAAAzH,UAAAsK,mBAAA,KACAlK,OAAA+J,eAAA1C,EAAAzH,UAAA,qBACAoK,IAAA,WAKA,MAJAhQ,MAAAkQ,oBACAlQ,KAAAiQ,eAAAjQ,KAAA0O,UAAA1O,KAAAgO,YAGAhO,KAAAkQ,sBAIA7C,EAAAzH,UAAAuK,wBACA,SAAA/H,EAAA8D,GACA,GAAA1L,GAAA4H,EAAAnD,OAAAiH,EACA,OAAA,MAAA1L,GAAA,MAAAA,GAQA6M,EAAAzH,UAAAqK,eACA,SAAA7H,EAAAgI,GACA,KAAA,IAAAxP,OAAA,6CAGAyM,EAAAgD,gBAAA,EACAhD,EAAAiD,eAAA,EAEAjD,EAAArB,qBAAA,EACAqB,EAAAtB,kBAAA,EAkBAsB,EAAAzH,UAAA2K,YACA,SAAAC,EAAAC,EAAAC,GACA,GAGAxC,GAHAyC,EAAAF,GAAA,KACAG,EAAAF,GAAArD,EAAAgD,eAGA,QAAAO,GACA,IAAAvD,GAAAgD,gBACAnC,EAAAlO,KAAA6Q,kBACA,MACA,KAAAxD,GAAAiD,eACApC,EAAAlO,KAAA8Q,iBACA,MACA,SACA,KAAA,IAAAlQ,OAAA,+BAGA,GAAAoN,GAAAhO,KAAAgO,UACAE,GAAArL,IAAA,SAAAkO,GACA,GAAAtN,GAAA,OAAAsN,EAAAtN,OAAA,KAAAzD,KAAAyO,SAAAhG,GAAAsI,EAAAtN,OAIA,OAHA,OAAAA,GAAA,MAAAuK,IACAvK,EAAAiE,EAAArE,KAAA2K,EAAAvK,KAGAA,OAAAA,EACAmL,cAAAmC,EAAAnC,cACAC,gBAAAkC,EAAAlC,gBACAC,aAAAiC,EAAAjC,aACAC,eAAAgC,EAAAhC,eACAC,KAAA,OAAA+B,EAAA/B,KAAA,KAAAhP,KAAAwO,OAAA/F,GAAAsI,EAAA/B,QAEAhP,MAAAgR,QAAAR,EAAAG,IAsBAtD,EAAAzH,UAAAqL,yBACA,SAAAC,GACA,GAAAtO,GAAA8E,EAAAmG,OAAAqD,EAAA,QAMAC,GACA1N,OAAAiE,EAAAmG,OAAAqD,EAAA,UACApC,aAAAlM,EACAmM,eAAArH,EAAAmG,OAAAqD,EAAA,SAAA,GAMA,IAHA,MAAAlR,KAAAgO,aACAmD,EAAA1N,OAAAiE,EAAA6G,SAAAvO,KAAAgO,WAAAmD,EAAA1N,UAEAzD,KAAAyO,SAAA9G,IAAAwJ,EAAA1N,QACA,QAEA0N,GAAA1N,OAAAzD,KAAAyO,SAAAtM,QAAAgP,EAAA1N,OAEA,IAAAyK,MAEAhC,EAAAlM,KAAAoR,aAAAD,EACAnR,KAAA8Q,kBACA,eACA,iBACApJ,EAAA2J,2BACA1B,EAAA5D,kBACA,IAAAG,GAAA,EAAA,CACA,GAAA6E,GAAA/Q,KAAA8Q,kBAAA5E,EAEA,IAAA1J,SAAA0O,EAAAhC,OAOA,IANA,GAAAJ,GAAAiC,EAAAjC,aAMAiC,GAAAA,EAAAjC,eAAAA,GACAZ,EAAA9J,MACAxB,KAAA8E,EAAAmG,OAAAkD,EAAA,gBAAA,MACA7B,OAAAxH,EAAAmG,OAAAkD,EAAA,kBAAA,MACAO,WAAA5J,EAAAmG,OAAAkD,EAAA,sBAAA,QAGAA,EAAA/Q,KAAA8Q,oBAAA5E,OASA,KANA,GAAA6C,GAAAgC,EAAAhC,eAMAgC,GACAA,EAAAjC,eAAAlM,GACAmO,EAAAhC,gBAAAA,GACAb,EAAA9J,MACAxB,KAAA8E,EAAAmG,OAAAkD,EAAA,gBAAA,MACA7B,OAAAxH,EAAAmG,OAAAkD,EAAA,kBAAA,MACAO,WAAA5J,EAAAmG,OAAAkD,EAAA,sBAAA,QAGAA,EAAA/Q,KAAA8Q,oBAAA5E,GAKA,MAAAgC,IAGA1O,EAAA6N,kBAAAA,EAmFAM,EAAA/H,UAAAI,OAAAyB,OAAA4F,EAAAzH,WACA+H,EAAA/H,UAAA8J,SAAArC,EASAM,EAAAkC,cACA,SAAAvC,GACA,GAAAiE,GAAAvL,OAAAyB,OAAAkG,EAAA/H,WAEAmI,EAAAwD,EAAA/C,OAAAlH,EAAAO,UAAAyF,EAAAkB,OAAA7F,WAAA,GACAmF,EAAAyD,EAAA9C,SAAAnH,EAAAO,UAAAyF,EAAAmB,SAAA9F,WAAA,EACA4I,GAAAvD,WAAAV,EAAAkE,YACAD,EAAAtD,eAAAX,EAAAmE,wBAAAF,EAAA9C,SAAA9F,UACA4I,EAAAvD,YACAuD,EAAApD,KAAAb,EAAAoE,KAWA,KAAA,GAJAC,GAAArE,EAAAoB,UAAA/F,UAAA1F,QACA2O,EAAAL,EAAAzB,uBACA+B,EAAAN,EAAArB,sBAEA3P,EAAA,EAAAS,EAAA2Q,EAAA3Q,OAAAT,EAAAS,EAAAT,IAAA,CACA,GAAAuR,GAAAH,EAAApR,GACAwR,EAAA,GAAApD,EACAoD,GAAAnD,cAAAkD,EAAAlD,cACAmD,EAAAlD,gBAAAiD,EAAAjD,gBAEAiD,EAAArO,SACAsO,EAAAtO,OAAAqK,EAAA3L,QAAA2P,EAAArO,QACAsO,EAAAjD,aAAAgD,EAAAhD,aACAiD,EAAAhD,eAAA+C,EAAA/C,eAEA+C,EAAA9C,OACA+C,EAAA/C,KAAAjB,EAAA5L,QAAA2P,EAAA9C,OAGA6C,EAAAzN,KAAA2N,IAGAH,EAAAxN,KAAA2N,GAKA,MAFA5E,GAAAoE,EAAArB,mBAAAxI,EAAA2J,4BAEAE,GAMA5D,EAAA/H,UAAAwI,SAAA,EAKApI,OAAA+J,eAAApC,EAAA/H,UAAA,WACAoK,IAAA,WACA,MAAAhQ,MAAAyO,SAAA9F,UAAA9F,IAAA,SAAAuM,GACA,MAAA,OAAApP,KAAAgO,WAAAtG,EAAArE,KAAArD,KAAAgO,WAAAoB,GAAAA,GACApP,SAqBA2N,EAAA/H,UAAAqK,eACA,SAAA7H,EAAAgI,GAeA,IAdA,GAYAW,GAAA/L,EAAAgN,EAAAC,EAAA/H,EAZA0E,EAAA,EACAsD,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAtR,EAAAoH,EAAApH,OACAkL,EAAA,EACAqG,KACA/F,KACAgG,KACAb,KAGAzF,EAAAlL,GACA,GAAA,MAAAoH,EAAAnD,OAAAiH,GACA0C,IACA1C,IACAgG,EAAA,MAEA,IAAA,MAAA9J,EAAAnD,OAAAiH,GACAA,QAEA,CASA,IARA6E,EAAA,GAAApC,GACAoC,EAAAnC,cAAAA,EAOAqD,EAAA/F,EAAA+F,EAAAjR,IACAhB,KAAAmQ,wBAAA/H,EAAA6J,GADAA,KAQA,GAHAjN,EAAAoD,EAAAnF,MAAAiJ,EAAA+F,GAEAD,EAAAO,EAAAvN,GAEAkH,GAAAlH,EAAAhE,WACA,CAEA,IADAgR,KACA9F,EAAA+F,GACArC,EAAAhG,OAAAxB,EAAA8D,EAAAM,GACAtC,EAAAsC,EAAAtC,MACAgC,EAAAM,EAAArC,KACA6H,EAAA5N,KAAA8F,EAGA,IAAA,IAAA8H,EAAAhR,OACA,KAAA,IAAAJ,OAAA,yCAGA,IAAA,IAAAoR,EAAAhR,OACA,KAAA,IAAAJ,OAAA,yCAGA2R,GAAAvN,GAAAgN,EAIAjB,EAAAlC,gBAAAqD,EAAAF,EAAA,GACAE,EAAAnB,EAAAlC,gBAEAmD,EAAAhR,OAAA,IAEA+P,EAAAtN,OAAA4O,EAAAL,EAAA,GACAK,GAAAL,EAAA,GAGAjB,EAAAjC,aAAAqD,EAAAH,EAAA,GACAG,EAAApB,EAAAjC,aAEAiC,EAAAjC,cAAA,EAGAiC,EAAAhC,eAAAqD,EAAAJ,EAAA,GACAI,EAAArB,EAAAhC,eAEAiD,EAAAhR,OAAA,IAEA+P,EAAA/B,KAAAsD,EAAAN,EAAA,GACAM,GAAAN,EAAA,KAIAL,EAAAvN,KAAA2M,GACA,gBAAAA,GAAAjC,cACA0D,EAAApO,KAAA2M,GAKA5D,EAAAwE,EAAAjK,EAAA+K,qCACAzS,KAAA8P,oBAAA6B,EAEAxE,EAAAqF,EAAA9K,EAAA2J,4BACArR,KAAAkQ,mBAAAsC,GAOA7E,EAAA/H,UAAAwL,aACA,SAAA7F,EAAAmH,EAAAC,EACAC,EAAAC,EAAAnH,GAMA,GAAAH,EAAAoH,IAAA,EACA,KAAA,IAAAzM,WAAA,gDACAqF,EAAAoH,GAEA,IAAApH,EAAAqH,GAAA,EACA,KAAA,IAAA1M,WAAA,kDACAqF,EAAAqH,GAGA,OAAAjD,GAAA1D,OAAAV,EAAAmH,EAAAG,EAAAnH,IAOAiC,EAAA/H,UAAAkN,mBACA,WACA,IAAA,GAAA5G,GAAA,EAAAA,EAAAlM,KAAA6Q,mBAAA7P,SAAAkL,EAAA,CACA,GAAA6E,GAAA/Q,KAAA6Q,mBAAA3E,EAMA,IAAAA,EAAA,EAAAlM,KAAA6Q,mBAAA7P,OAAA,CACA,GAAA+R,GAAA/S,KAAA6Q,mBAAA3E,EAAA,EAEA,IAAA6E,EAAAnC,gBAAAmE,EAAAnE,cAAA,CACAmC,EAAAiC,oBAAAD,EAAAlE,gBAAA,CACA,WAKAkC,EAAAiC,oBAAAC,EAAAA,IAwBAtF,EAAA/H,UAAAsN,oBACA,SAAAhC,GACA,GAAAC,IACAvC,cAAAlH,EAAAmG,OAAAqD,EAAA,QACArC,gBAAAnH,EAAAmG,OAAAqD,EAAA,WAGAhF,EAAAlM,KAAAoR,aACAD,EACAnR,KAAA6Q,mBACA,gBACA,kBACAnJ,EAAA+K,oCACA/K,EAAAmG,OAAAqD,EAAA,OAAA7D,EAAArB,sBAGA,IAAAE,GAAA,EAAA,CACA,GAAA6E,GAAA/Q,KAAA6Q,mBAAA3E,EAEA,IAAA6E,EAAAnC,gBAAAuC,EAAAvC,cAAA,CACA,GAAAnL,GAAAiE,EAAAmG,OAAAkD,EAAA,SAAA,KACA,QAAAtN,IACAA,EAAAzD,KAAAyO,SAAAhG,GAAAhF,GACA,MAAAzD,KAAAgO,aACAvK,EAAAiE,EAAArE,KAAArD,KAAAgO,WAAAvK,IAGA,IAAAuL,GAAAtH,EAAAmG,OAAAkD,EAAA,OAAA,KAIA,OAHA,QAAA/B,IACAA,EAAAhP,KAAAwO,OAAA/F,GAAAuG,KAGAvL,OAAAA,EACAb,KAAA8E,EAAAmG,OAAAkD,EAAA,eAAA,MACA7B,OAAAxH,EAAAmG,OAAAkD,EAAA,iBAAA,MACA/B,KAAAA,IAKA,OACAvL,OAAA,KACAb,KAAA,KACAsM,OAAA,KACAF,KAAA,OAQArB,EAAA/H,UAAAuN,wBACA,WACA,QAAAnT,KAAAiO,iBAGAjO,KAAAiO,eAAAjN,QAAAhB,KAAAyO,SAAAvG,SACAlI,KAAAiO,eAAAmF,KAAA,SAAAC,GAAA,MAAA,OAAAA,MAQA1F,EAAA/H,UAAA0N,iBACA,SAAAC,EAAAC,GACA,IAAAxT,KAAAiO,eACA,MAAA,KAOA,IAJA,MAAAjO,KAAAgO,aACAuF,EAAA7L,EAAA6G,SAAAvO,KAAAgO,WAAAuF,IAGAvT,KAAAyO,SAAA9G,IAAA4L,GACA,MAAAvT,MAAAiO,eAAAjO,KAAAyO,SAAAtM,QAAAoR,GAGA,IAAAlE,EACA,IAAA,MAAArP,KAAAgO,aACAqB,EAAA3H,EAAA+L,SAAAzT,KAAAgO,aAAA,CAKA,GAAA0F,GAAAH,EAAAhR,QAAA,aAAA,GACA,IAAA,QAAA8M,EAAAsE,QACA3T,KAAAyO,SAAA9G,IAAA+L,GACA,MAAA1T,MAAAiO,eAAAjO,KAAAyO,SAAAtM,QAAAuR,GAGA,MAAArE,EAAAuE,MAAA,KAAAvE,EAAAuE,OACA5T,KAAAyO,SAAA9G,IAAA,IAAA4L,GACA,MAAAvT,MAAAiO,eAAAjO,KAAAyO,SAAAtM,QAAA,IAAAoR,IAQA,GAAAC,EACA,MAAA,KAGA,MAAA,IAAA5S,OAAA,IAAA2S,EAAA,+BAuBA5F,EAAA/H,UAAAiO,qBACA,SAAA3C,GACA,GAAAzN,GAAAiE,EAAAmG,OAAAqD,EAAA,SAIA,IAHA,MAAAlR,KAAAgO,aACAvK,EAAAiE,EAAA6G,SAAAvO,KAAAgO,WAAAvK,KAEAzD,KAAAyO,SAAA9G,IAAAlE,GACA,OACAb,KAAA,KACAsM,OAAA,KACAoC,WAAA,KAGA7N,GAAAzD,KAAAyO,SAAAtM,QAAAsB,EAEA,IAAA0N,IACA1N,OAAAA,EACAqL,aAAApH,EAAAmG,OAAAqD,EAAA,QACAnC,eAAArH,EAAAmG,OAAAqD,EAAA,WAGAhF,EAAAlM,KAAAoR,aACAD,EACAnR,KAAA8Q,kBACA,eACA,iBACApJ,EAAA2J,2BACA3J,EAAAmG,OAAAqD,EAAA,OAAA7D,EAAArB,sBAGA,IAAAE,GAAA,EAAA,CACA,GAAA6E,GAAA/Q,KAAA8Q,kBAAA5E,EAEA,IAAA6E,EAAAtN,SAAA0N,EAAA1N,OACA,OACAb,KAAA8E,EAAAmG,OAAAkD,EAAA,gBAAA,MACA7B,OAAAxH,EAAAmG,OAAAkD,EAAA,kBAAA,MACAO,WAAA5J,EAAAmG,OAAAkD,EAAA,sBAAA,OAKA,OACAnO,KAAA,KACAsM,OAAA,KACAoC,WAAA,OAIA9R,EAAAmO,uBAAAA,EA+FAD,EAAA9H,UAAAI,OAAAyB,OAAA4F,EAAAzH,WACA8H,EAAA9H,UAAAkO,YAAAzG,EAKAK,EAAA9H,UAAAwI,SAAA,EAKApI,OAAA+J,eAAArC,EAAA9H,UAAA,WACAoK,IAAA,WAEA,IAAA,GADAlC,MACAvN,EAAA,EAAAA,EAAAP,KAAAmP,UAAAnO,OAAAT,IACA,IAAA,GAAA0G,GAAA,EAAAA,EAAAjH,KAAAmP,UAAA5O,GAAAmP,SAAA5B,QAAA9M,OAAAiG,IACA6G,EAAA1J,KAAApE,KAAAmP,UAAA5O,GAAAmP,SAAA5B,QAAA7G,GAGA,OAAA6G,MAmBAJ,EAAA9H,UAAAsN,oBACA,SAAAhC,GACA,GAAAC,IACAvC,cAAAlH,EAAAmG,OAAAqD,EAAA,QACArC,gBAAAnH,EAAAmG,OAAAqD,EAAA,WAKA6C,EAAApE,EAAA1D,OAAAkF,EAAAnR,KAAAmP,UACA,SAAAgC,EAAA6C,GACA,GAAAlI,GAAAqF,EAAAvC,cAAAoF,EAAAvE,gBAAAb,aACA,OAAA9C,GACAA,EAGAqF,EAAAtC,gBACAmF,EAAAvE,gBAAAZ,kBAEAmF,EAAAhU,KAAAmP,UAAA4E,EAEA,OAAAC,GASAA,EAAAtE,SAAAwD,qBACAtQ,KAAAuO,EAAAvC,eACAoF,EAAAvE,gBAAAb,cAAA,GACAM,OAAAiC,EAAAtC,iBACAmF,EAAAvE,gBAAAb,gBAAAuC,EAAAvC,cACAoF,EAAAvE,gBAAAZ,gBAAA,EACA,GACAoF,KAAA/C,EAAA+C,QAdAxQ,OAAA,KACAb,KAAA,KACAsM,OAAA,KACAF,KAAA,OAmBAtB,EAAA9H,UAAAuN,wBACA,WACA,MAAAnT,MAAAmP,UAAA+E,MAAA,SAAA9E,GACA,MAAAA,GAAAM,SAAAyD,6BASAzF,EAAA9H,UAAA0N,iBACA,SAAAC,EAAAC,GACA,IAAA,GAAAjT,GAAA,EAAAA,EAAAP,KAAAmP,UAAAnO,OAAAT,IAAA,CACA,GAAAyT,GAAAhU,KAAAmP,UAAA5O,GAEA4T,EAAAH,EAAAtE,SAAA4D,iBAAAC,GAAA,EACA,IAAAY,EACA,MAAAA,GAGA,GAAAX,EACA,MAAA,KAGA,MAAA,IAAA5S,OAAA,IAAA2S,EAAA,+BAkBA7F,EAAA9H,UAAAiO,qBACA,SAAA3C,GACA,IAAA,GAAA3Q,GAAA,EAAAA,EAAAP,KAAAmP,UAAAnO,OAAAT,IAAA,CACA,GAAAyT,GAAAhU,KAAAmP,UAAA5O,EAIA,IAAAyT,EAAAtE,SAAA5B,QAAA3L,QAAAuF,EAAAmG,OAAAqD,EAAA,gBAAA,CAGA,GAAAkD,GAAAJ,EAAAtE,SAAAmE,qBAAA3C,EACA,IAAAkD,EAAA,CACA,GAAAC,IACAzR,KAAAwR,EAAAxR,MACAoR,EAAAvE,gBAAAb,cAAA,GACAM,OAAAkF,EAAAlF,QACA8E,EAAAvE,gBAAAb,gBAAAwF,EAAAxR,KACAoR,EAAAvE,gBAAAZ,gBAAA,EACA,GAEA,OAAAwF,KAIA,OACAzR,KAAA,KACAsM,OAAA,OASAxB,EAAA9H,UAAAqK,eACA,SAAA7H,EAAAgI,GACApQ,KAAA8P,uBACA9P,KAAAkQ,qBACA,KAAA,GAAA3P,GAAA,EAAAA,EAAAP,KAAAmP,UAAAnO,OAAAT,IAGA,IAAA,GAFAyT,GAAAhU,KAAAmP,UAAA5O,GACA+T,EAAAN,EAAAtE,SAAAmB,mBACA5J,EAAA,EAAAA,EAAAqN,EAAAtT,OAAAiG,IAAA,CACA,GAAA8J,GAAAuD,EAAArN,GAEAxD,EAAAuQ,EAAAtE,SAAAjB,SAAAhG,GAAAsI,EAAAtN,OACA,QAAAuQ,EAAAtE,SAAA1B,aACAvK,EAAAiE,EAAArE,KAAA2Q,EAAAtE,SAAA1B,WAAAvK,IAEAzD,KAAAyO,SAAAxG,IAAAxE,GACAA,EAAAzD,KAAAyO,SAAAtM,QAAAsB,EAEA,IAAAuL,GAAAgF,EAAAtE,SAAAlB,OAAA/F,GAAAsI,EAAA/B,KACAhP,MAAAwO,OAAAvG,IAAA+G,GACAA,EAAAhP,KAAAwO,OAAArM,QAAA6M,EAMA,IAAAuF,IACA9Q,OAAAA,EACAmL,cAAAmC,EAAAnC,eACAoF,EAAAvE,gBAAAb,cAAA,GACAC,gBAAAkC,EAAAlC,iBACAmF,EAAAvE,gBAAAb,gBAAAmC,EAAAnC,cACAoF,EAAAvE,gBAAAZ,gBAAA,EACA,GACAC,aAAAiC,EAAAjC,aACAC,eAAAgC,EAAAhC,eACAC,KAAAA,EAGAhP,MAAA8P,oBAAA1L,KAAAmQ,GACA,gBAAAA,GAAAzF,cACA9O,KAAAkQ,mBAAA9L,KAAAmQ,GAKApH,EAAAnN,KAAA8P,oBAAApI,EAAA+K,qCACAtF,EAAAnN,KAAAkQ,mBAAAxI,EAAA2J,6BAGA7R,EAAAkO,yBAAAA,IPi4BG8G,cAAc,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,eAAe,EAAE/L,SAAS,IAAIgM,GAAG,SAASnU,EAAQhB,EAAOD,GQz6DjH,QAAAqO,GAAAqD,EAAA2D,EAAAC,GACA,GAAAD,IAAA3D,GACA,MAAAA,GAAA2D,EACA,IAAA,IAAAE,UAAA/T,OACA,MAAA8T,EAEA,MAAA,IAAAlU,OAAA,IAAAiU,EAAA,6BAQA,QAAApB,GAAAuB,GACA,GAAAlT,GAAAkT,EAAAlT,MAAAmT,EACA,OAAAnT,IAIA6R,OAAA7R,EAAA,GACAoT,KAAApT,EAAA,GACAqT,KAAArT,EAAA,GACAsT,KAAAtT,EAAA,GACA8R,KAAA9R,EAAA,IAPA,KAYA,QAAAuT,GAAAC,GACA,GAAAjG,GAAA,EAiBA,OAhBAiG,GAAA3B,SACAtE,GAAAiG,EAAA3B,OAAA,KAEAtE,GAAA,KACAiG,EAAAJ,OACA7F,GAAAiG,EAAAJ,KAAA,KAEAI,EAAAH,OACA9F,GAAAiG,EAAAH,MAEAG,EAAAF,OACA/F,GAAA,IAAAiG,EAAAF,MAEAE,EAAA1B,OACAvE,GAAAiG,EAAA1B,MAEAvE,EAeA,QAAAhB,GAAAkH,GACA,GAAA3B,GAAA2B,EACAlG,EAAAoE,EAAA8B,EACA,IAAAlG,EAAA,CACA,IAAAA,EAAAuE,KACA,MAAA2B,EAEA3B,GAAAvE,EAAAuE,KAKA,IAAA,GAAA4B,GAHAlH,EAAA9O,EAAA8O,WAAAsF,GAEAvR,EAAAuR,EAAAlR,MAAA,OACA+S,EAAA,EAAAlV,EAAA8B,EAAArB,OAAA,EAAAT,GAAA,EAAAA,IACAiV,EAAAnT,EAAA9B,GACA,MAAAiV,EACAnT,EAAAqT,OAAAnV,EAAA,GACA,OAAAiV,EACAC,IACAA,EAAA,IACA,KAAAD,GAIAnT,EAAAqT,OAAAnV,EAAA,EAAAkV,GACAA,EAAA,IAEApT,EAAAqT,OAAAnV,EAAA,GACAkV,KAUA,OANA7B,GAAAvR,EAAAgB,KAAA,KAEA,KAAAuQ,IACAA,EAAAtF,EAAA,IAAA,KAGAe,GACAA,EAAAuE,KAAAA,EACAyB,EAAAhG,IAEAuE,EAoBA,QAAAvQ,GAAAsS,EAAAJ,GACA,KAAAI,IACAA,EAAA,KAEA,KAAAJ,IACAA,EAAA,IAEA,IAAAK,GAAAnC,EAAA8B,GACAM,EAAApC,EAAAkC,EAMA,IALAE,IACAF,EAAAE,EAAAjC,MAAA,KAIAgC,IAAAA,EAAAjC,OAIA,MAHAkC,KACAD,EAAAjC,OAAAkC,EAAAlC,QAEA0B,EAAAO,EAGA,IAAAA,GAAAL,EAAAzT,MAAAgU,GACA,MAAAP,EAIA,IAAAM,IAAAA,EAAAV,OAAAU,EAAAjC,KAEA,MADAiC,GAAAV,KAAAI,EACAF,EAAAQ,EAGA,IAAAE,GAAA,MAAAR,EAAAtQ,OAAA,GACAsQ,EACAlH,EAAAsH,EAAApT,QAAA,OAAA,IAAA,IAAAgT,EAEA,OAAAM,IACAA,EAAAjC,KAAAmC,EACAV,EAAAQ,IAEAE,EAcA,QAAAxH,GAAAoH,EAAAJ,GACA,KAAAI,IACAA,EAAA,KAGAA,EAAAA,EAAApT,QAAA,MAAA,GAOA,KADA,GAAAyT,GAAA,EACA,IAAAT,EAAApT,QAAAwT,EAAA,MAAA,CACA,GAAAzJ,GAAAyJ,EAAA7O,YAAA,IACA,IAAAoF,EAAA,EACA,MAAAqJ,EAOA,IADAI,EAAAA,EAAA1S,MAAA,EAAAiJ,GACAyJ,EAAA7T,MAAA,qBACA,MAAAyT,KAGAS,EAIA,MAAAC,OAAAD,EAAA,GAAA3S,KAAA,OAAAkS,EAAAW,OAAAP,EAAA3U,OAAA,GASA,QAAAmV,GAAA/G,GACA,MAAAA,GAYA,QAAA9G,GAAAF,GACA,MAAAgO,GAAAhO,GACA,IAAAA,EAGAA,EAIA,QAAAiO,GAAAjO,GACA,MAAAgO,GAAAhO,GACAA,EAAAnF,MAAA,GAGAmF,EAIA,QAAAgO,GAAAhH,GACA,IAAAA,EACA,OAAA,CAGA,IAAApO,GAAAoO,EAAApO,MAEA,IAAAA,EAAA,EACA,OAAA,CAGA,IAAA,KAAAoO,EAAAnF,WAAAjJ,EAAA,IACA,KAAAoO,EAAAnF,WAAAjJ,EAAA,IACA,MAAAoO,EAAAnF,WAAAjJ,EAAA,IACA,MAAAoO,EAAAnF,WAAAjJ,EAAA,IACA,MAAAoO,EAAAnF,WAAAjJ,EAAA,IACA,MAAAoO,EAAAnF,WAAAjJ,EAAA,IACA,MAAAoO,EAAAnF,WAAAjJ,EAAA,IACA,KAAAoO,EAAAnF,WAAAjJ,EAAA,IACA,KAAAoO,EAAAnF,WAAAjJ,EAAA,GACA,OAAA,CAGA,KAAA,GAAAT,GAAAS,EAAA,GAAAT,GAAA,EAAAA,IACA,GAAA,KAAA6O,EAAAnF,WAAA1J,GACA,OAAA,CAIA,QAAA,EAWA,QAAA8Q,GAAAiF,EAAAC,EAAAC,GACA,GAAA1K,GAAAwK,EAAA7S,OAAA8S,EAAA9S,MACA,OAAA,KAAAqI,EACAA,GAGAA,EAAAwK,EAAAxH,aAAAyH,EAAAzH,aACA,IAAAhD,EACAA,GAGAA,EAAAwK,EAAAvH,eAAAwH,EAAAxH,eACA,IAAAjD,GAAA0K,EACA1K,GAGAA,EAAAwK,EAAAzH,gBAAA0H,EAAA1H,gBACA,IAAA/C,EACAA,GAGAA,EAAAwK,EAAA1H,cAAA2H,EAAA3H,cACA,IAAA9C,EACAA,EAGAwK,EAAAtH,KAAAuH,EAAAvH,SAaA,QAAAyD,GAAA6D,EAAAC,EAAAE,GACA,GAAA3K,GAAAwK,EAAA1H,cAAA2H,EAAA3H,aACA,OAAA,KAAA9C,EACAA,GAGAA,EAAAwK,EAAAzH,gBAAA0H,EAAA1H,gBACA,IAAA/C,GAAA2K,EACA3K,GAGAA,EAAAwK,EAAA7S,OAAA8S,EAAA9S,OACA,IAAAqI,EACAA,GAGAA,EAAAwK,EAAAxH,aAAAyH,EAAAzH,aACA,IAAAhD,EACAA,GAGAA,EAAAwK,EAAAvH,eAAAwH,EAAAxH,eACA,IAAAjD,EACAA,EAGAwK,EAAAtH,KAAAuH,EAAAvH,SAIA,QAAA0H,GAAAC,EAAAC,GACA,MAAAD,KAAAC,EACA,EAGAD,EAAAC,EACA,KAUA,QAAAC,GAAAP,EAAAC,GACA,GAAAzK,GAAAwK,EAAA1H,cAAA2H,EAAA3H,aACA,OAAA,KAAA9C,EACAA,GAGAA,EAAAwK,EAAAzH,gBAAA0H,EAAA1H,gBACA,IAAA/C,EACAA,GAGAA,EAAA4K,EAAAJ,EAAA7S,OAAA8S,EAAA9S,QACA,IAAAqI,EACAA,GAGAA,EAAAwK,EAAAxH,aAAAyH,EAAAzH,aACA,IAAAhD,EACAA,GAGAA,EAAAwK,EAAAvH,eAAAwH,EAAAxH,eACA,IAAAjD,EACAA,EAGA4K,EAAAJ,EAAAtH,KAAAuH,EAAAvH,UApYAxP,EAAAqO,OAAAA,CAEA,IAAAoH,GAAA,iEACAa,EAAA,eAeAtW,GAAAiU,SAAAA,EAsBAjU,EAAA6V,YAAAA,EAwDA7V,EAAA6O,UAAAA,EA2DA7O,EAAA6D,KAAAA,EAEA7D,EAAA8O,WAAA,SAAAiH,GACA,MAAA,MAAAA,EAAAtQ,OAAA,MAAAsQ,EAAAzT,MAAAmT,IAyCAzV,EAAA+O,SAAAA,CAEA,IAAAuI,GAAA,WACA,GAAAzR,GAAAW,OAAAyB,OAAA,KACA,SAAA,aAAApC,MAuBA7F,GAAA8I,YAAAwO,EAAAX,EAAA7N,EASA9I,EAAA6W,cAAAS,EAAAX,EAAAE,EAsEA7W,EAAA6R,2BAAAA,EAuCA7R,EAAAiT,oCAAAA,EA8CAjT,EAAAqX,oCAAAA,OR67DME,IAAI,SAAStW,EAAQhB,EAAOD,GAClCuV,UAAU,GAAG,GAAG,GAAGiC,MAAMxX,EAAQuV,aAC9BkC,IAAM,IAAIC,IAAI,SAASzW,EAAQhB,EAAOD,IS/1EzC,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,mBAAA,cAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,EAAAV,EAAA,eAEAS,EAAAiW,eAAAhW,EAAAD,EAAAG,aAEArB,KAAA,SAAAqB,GACA,OACA+V,UAAA,SAAAC,GACA,GAAAxV,MACAyV,EAAA,EAEA,iBAAAD,IAAA,gBAAAA,GAAAC,eACAA,EAAAD,EAAAC,aAIA,KADA,GAAAC,GAAAxC,UAAAyC,OACAD,GAAA1V,EAAAb,OAAAsW,GAAAC,EAAA,WAAA,CAGA,IAAA,GADA/S,GAAA,GAAAyR,OAAAsB,EAAA,UAAAvW,QACAT,EAAA,EAAAA,EAAAiE,EAAAxD,SAAAT,EACAiE,EAAAjE,GAAAgX,EAAA,UAAAhX,EAEA,iCAAAkX,KAAAF,EAAAtR,YACApE,EAAAuC,KAAA,GAAA/C,IAAA+B,aAAAsU,OAAAC,IAAAnV,OAAAgC,KAAAA,KAEA3C,EAAAuC,KAAA,GAAA/C,IAAAmD,KAAAA,IAGA,KACA+S,EAAAA,EAAAK,OACA,MAAAzX,GACA,OAGA,MAAA0B,STq2EG4C,WAAa,KAAKoT,IAAI,SAASpX,EAAQhB,EAAOD,GACjDuV,UAAU,GAAG,GAAG,GAAGiC,MAAMxX,EAAQuV,aAC9BkC,IAAM,IAAIa,IAAI,SAASrX,EAAQhB,EAAOD,IUh5EzC,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,kBAAA,aAAA,cAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,EAAAV,EAAA,sCAAAA,EAAA,eAEAS,EAAA6W,cAAA5W,EAAAD,EAAA8W,WAAA9W,EAAAqM,UAAArM,EAAAG,aAEArB,KAAA,SAAAgY,EAAA3W,GACA,YAQA,SAAA4W,GAAA5I,GACA,MAAA,IAAA6I,SAAA,SAAAC,EAAAC,GACA,GAAAC,GAAA,GAAAC,eACAD,GAAAE,KAAA,MAAAlJ,GACAgJ,EAAAG,QAAAJ,EACAC,EAAAI,mBAAA,WACA,IAAAJ,EAAAK,aACAL,EAAAM,QAAA,KAAAN,EAAAM,OAAA,KACA,YAAAtJ,EAAA6G,OAAA,EAAA,IAAAmC,EAAAO,aACAT,EAAAE,EAAAO,cAEAR,EAAA,GAAAxX,OAAA,gBAAAyX,EAAAM,OAAA,eAAAtJ,MAIAgJ,EAAAQ,SAYA,QAAAC,GAAAC,GACA,GAAA,mBAAAlZ,SAAAA,OAAAmZ,KACA,MAAAnZ,QAAAmZ,KAAAD,EAEA,MAAA,IAAAnY,OAAA,kEAIA,QAAAqY,GAAAC,GACA,GAAA,mBAAA1L,OAAAA,KAAA/L,MACA,MAAA+L,MAAA/L,MAAAyX,EAEA,MAAA,IAAAtY,OAAA,iEAIA,QAAAuY,GAAA1V,EAAAF,GAkBA,IAAA,GAjBA6V,IAEA,2DAEA,uCAEA,wEAEA,mFAEA,8DAEAnV,EAAAR,EAAAf,MAAA,MAGA7B,EAAA,GACAwY,EAAAzN,KAAA0N,IAAA/V,EAAA,IACAhD,EAAA,EAAAA,EAAA8Y,IAAA9Y,EAAA,CAEA,GAAAqC,GAAAqB,EAAAV,EAAAhD,EAAA,GACAgZ,EAAA3W,EAAAT,QAAA,KAKA,IAJAoX,GAAA,IACA3W,EAAAA,EAAAsT,OAAA,EAAAqD,IAGA3W,EAAA,CACA/B,EAAA+B,EAAA/B,CAEA,KAAA,GADAsD,GAAAiV,EAAApY,OACAkL,EAAA,EAAAA,EAAA/H,EAAA+H,IAAA,CACA,GAAAsN,GAAAJ,EAAAlN,GAAA5J,KAAAzB,EACA,IAAA2Y,GAAAA,EAAA,GACA,MAAAA,GAAA,MAQA,QAAAC,KACA,GAAA,kBAAAzT,QAAA+J,gBAAA,kBAAA/J,QAAAyB,OACA,KAAA,IAAA7G,OAAA,mDAIA,QAAA8Y,GAAAjV,GACA,GAAA,gBAAAA,GACA,KAAA,IAAAyB,WAAA,oCACA,IAAA,gBAAAzB,GAAAnB,SACA,KAAA,IAAA4C,WAAA,kCACA,IAAA,gBAAAzB,GAAAlB,YACAkB,EAAAlB,WAAA,IAAA,GACAkB,EAAAlB,WAAA,EACA,KAAA,IAAA2C,WAAA,+CACA,IAAA,gBAAAzB,GAAAjB,cACAiB,EAAAjB,aAAA,IAAA,GACAiB,EAAAjB,aAAA,EACA,KAAA,IAAA0C,WAAA,qDAEA,QAAA,EAGA,QAAAyT,GAAAlW,GAKA,IAJA,GACAmW,GACAC,EAFAC,EAAA,8CAIAD,EAAAC,EAAAxX,KAAAmB,IACAmW,EAAAC,EAAA,EAEA,IAAAD,EACA,MAAAA,EAEA,MAAA,IAAAhZ,OAAA,8BAIA,QAAAmZ,GAAAtV,EAAAuV,EAAAC,GACA,MAAA,IAAA/B,SAAA,SAAAC,EAAAC,GACA,GAAA8B,GAAAF,EAAA9G,qBACAtQ,KAAA6B,EAAAlB,WACA2L,OAAAzK,EAAAjB,cAGA,IAAA0W,EAAAzW,OAAA,CAEA,GAAA0W,GAAAH,EAAA1G,iBAAA4G,EAAAzW,OACA0W,KACAF,EAAAC,EAAAzW,QAAA0W,GAGAhC,EAEA,GAAA9W,IACA+B,aAAA8W,EAAAlL,MAAAvK,EAAArB,aACAoB,KAAAC,EAAAD,KACAlB,SAAA4W,EAAAzW,OACAF,WAAA2W,EAAAtX,KACAY,aAAA0W,EAAAhL,cAGAkJ,GAAA,GAAAxX,OAAA,wEAcA,MAAA,SAAAmX,GAAAV,GACA,MAAArX,gBAAA+X,IAGAV,EAAAA,MAEArX,KAAAia,YAAA5C,EAAA4C,gBACAja,KAAAoa,uBAAA/C,EAAA+C,2BAEApa,KAAAqa,KAAAhD,EAAAgD,MAAApC,EAEAjY,KAAA8Y,MAAAzB,EAAA2B,MAAAF,EAEA9Y,KAAAsa,KAAA,SAAAvX,GACA,MAAA,IAAAmV,SAAA,SAAAC,EAAAC,GACA,GAAAmC,GAAA,UAAAxX,EAAAmT,OAAA,EAAA,EACA,IAAAlW,KAAAia,YAAAlX,GACAoV,EAAAnY,KAAAia,YAAAlX,QACA,IAAAsU,EAAAmD,UAAAD,EACAnC,EAAA,GAAAxX,OAAA,qDAEA,IAAA2Z,EAAA,CAGA,GAAAE,GACA,+CACA3Y,EAAAiB,EAAAjB,MAAA2Y,EACA,IAAA3Y,EAAA,CACA,GAAA4Y,GAAA5Y,EAAA,GAAAd,OACA2Z,EAAA5X,EAAAmT,OAAAwE,GACAjX,EAAAzD,KAAA8Y,MAAA6B,EACA3a,MAAAia,YAAAlX,GAAAU,EACA0U,EAAA1U,OAEA2U,GAAA,GAAAxX,OAAA,8DAEA,CACA,GAAAga,GAAA5a,KAAAqa,KAAAtX,GAAA8X,OAAA,OAEA7a,MAAAia,YAAAlX,GAAA6X,EACAA,EAAAE,KAAA3C,EAAAC,KAGA2C,KAAA/a,QAWAA,KAAAgb,sBAAA,SAAAC,EAAAC,GACA,MAAA,IAAAhD,SAAA,SAAAC,GACA,GAAAnY,KAAAoa,uBAAAa,GACA9C,EAAAnY,KAAAoa,uBAAAa,QACA,CACA,GAAAE,GAAA,GAAAjD,SAAA,SAAAC,EAAAC,GACA,MAAApY,MAAAsa,KAAAW,GAAAH,KAAA,SAAAM,GACA,gBAAAA,KACAA,EAAAnC,EAAAmC,EAAA7Y,QAAA,WAAA,MAEA,mBAAA6Y,GAAApN,aACAoN,EAAApN,WAAAkN,GAGA/C,EAAA,GAAAH,GAAA3K,kBAAA+N,KACAhD,IACA2C,KAAA/a,MACAA,MAAAoa,uBAAAa,GAAAE,EACAhD,EAAAgD,KAEAJ,KAAA/a,QAUAA,KAAAqb,SAAA,SAAA5W,GACA,MAAA,IAAAyT,SAAA,SAAAC,EAAAC,GACApY,KAAAsb,kBAAA7W,GAAAqW,KAAA,SAAAS,GACA,QAAAC,KACArD,EAAAoD,GAGAvb,KAAAyb,iBAAAF,GACAT,KAAA3C,EAAAqD,GAEA,SAAAA,IACAT,KAAA/a,MAAAoY,IACA2C,KAAA/a,QASAA,KAAAyb,iBAAA,SAAAhX,GACA,MAAA,IAAAyT,SAAA,SAAAC,EAAAC,GACAsB,EAAAjV,GACAzE,KAAAsa,KAAA7V,EAAAnB,UAAAwX,KAAA,SAAArX,GACA,GAAAF,GAAAkB,EAAAlB,WACAC,EAAAiB,EAAAjB,aACAkY,EAAAvC,EAAA1V,EAAAF,EAAAC,EAGA2U,GADAuD,EACA,GAAAra,IACA+B,aAAAsY,EACAlX,KAAAC,EAAAD,KACAlB,SAAAmB,EAAAnB,SACAC,WAAAA,EACAC,aAAAA,IAGAiB,IAEA2T,GAAA,SAAAA,IACA2C,KAAA/a,aASAA,KAAAsb,kBAAA,SAAA7W,GACA,MAAA,IAAAyT,SAAA,SAAAC,EAAAC,GACAqB,IACAC,EAAAjV,EAEA,IAAAwV,GAAAja,KAAAia,YACA3W,EAAAmB,EAAAnB,QACAtD,MAAAsa,KAAAhX,GAAAwX,KAAA,SAAArX,GACA,GAAAwX,GAAAtB,EAAAlW,GACA8W,EAAA,UAAAU,EAAA/E,OAAA,EAAA,GACAgF,EAAA5X,EAAA6B,UAAA,EAAA7B,EAAAwD,YAAA,KAAA,EAMA,OAJA,MAAAmU,EAAA,IAAAV,GAAA,sBAAA9C,KAAAwD,KACAA,EAAAC,EAAAD,GAGAjb,KAAAgb,sBAAAC,EAAAC,GACAJ,KAAA,SAAAd,GACA,MAAAD,GAAAtV,EAAAuV,EAAAC,GACAa,KAAA3C,GAAA,SAAA,WACAA,EAAA1T,QAGAsW,KAAA/a,MAAAoY,GAAA,SAAAA,IACA2C,KAAA/a,UA5JA,GAAA+X,GAAAV,QVkjFGsE,qCAAqC,EAAElX,WAAa,KAAKmX,IAAI,SAASnb,EAAQhB,EAAOD,IAxuFxF,SAAA0B,EAAAC,GACA,YAIA,mBAAAzB,IAAAA,EAAAC,IACAD,EAAA,cAAA,qBAAA,kBAAA,kBAAAyB,GACA,gBAAA3B,GACAC,EAAAD,QAAA2B,EAAAV,EAAA,sBAAAA,EAAA,mBAAAA,EAAA,mBAEAS,EAAAjB,WAAAkB,EAAAD,EAAAE,iBAAAF,EAAAiW,eAAAjW,EAAA6W,gBAEA/X,KAAA,SAAAoB,EAAA+V,EAAAY,GA8BA,QAAA8D,GAAAC,EAAAC,GACA,GAAAC,KAWA,QATAF,EAAAC,GAAA/K,QAAA,SAAA3L,GACA,IAAA,GAAA4W,KAAA5W,GACAW,OAAAJ,UAAAgC,eAAA7G,KAAAsE,EAAA4W,KACAD,EAAAC,GAAA5W,EAAA4W,GAGA,OAAAD,KAGAA,EAGA,QAAAE,GAAAC,GACA,MAAAA,GAAAta,OAAAsa,EAAA,mBAGA,QAAAC,GAAAC,EAAA1Z,GACA,MAAA,kBAAAA,GACA0Z,EAAA1Z,OAAAA,GAEA0Z,EApDA,GAAAC,IACA3Z,OAAA,SAAA8B,GAEA,OAAAA,EAAArB,cAAA,IAAAjB,QAAA,uBACAsC,EAAArB,cAAA,IAAAjB,QAAA,6BACAsC,EAAArB,cAAA,IAAAjB,QAAA,0BACAsC,EAAArB,cAAA,IAAAjB,QAAA,0BAEA8X,gBAGAsC,EAAA,WACA,IAEA,KAAA,IAAA3b,OACA,MAAAub,GACA,MAAAA,IAuCA,QAOAnM,IAAA,SAAAqH,GACA,GAAA8E,GAAAI,GACA,OAAAL,GAAAC,GAAAnc,KAAAwc,UAAAL,EAAA9E,GAAArX,KAAAyc,qBAAApF,IAUAqF,QAAA,SAAArF,GACAA,EAAAwE,EAAAS,EAAAjF,EACA,IAAA8E,GAAAI,IACA1a,EAAAqa,EAAAC,GAAA/a,EAAAK,MAAA0a,GAAAhF,EAAAC,UAAAC,EACA,OAAA+E,GAAAva,EAAAwV,EAAA1U,SAUA6Z,UAAA,SAAA9a,EAAA2V,GACAA,EAAAwE,EAAAS,EAAAjF,EACA,IAAAsF,GAAA,GAAA5E,GAAAV,EACA,OAAA,IAAAa,SAAA,SAAAC,GACA,GAAAkE,GAAAD,EAAAhb,EAAAK,MAAAC,GAAA2V,EAAA1U,OACAwV,GAAAD,QAAA0E,IAAAP,EAAAxZ,IAAA,SAAAga,GACA,MAAA,IAAA3E,SAAA,SAAAC,GACA,QAAA2E,KACA3E,EAAA0E,GAGAF,EAAAtB,SAAAwB,GAAA/B,KAAA3C,EAAA2E,GAAA,SAAAA,UAGA/B,KAAA/a,QASAyc,qBAAA,SAAApF,GACAA,EAAAwE,EAAAS,EAAAjF,EACA,IAAA0F,GAAA5F,EAAAC,UAAAC,EAIA,OAHA,kBAAAA,GAAA1U,SACAoa,EAAAA,EAAApa,OAAA0U,EAAA1U,SAEAuV,QAAAC,QAAA4E,IAYAC,WAAA,SAAAC,EAAAC,EAAAC,EAAAC,GACA,GAAA,kBAAAH,GACA,KAAA,IAAArc,OAAA,wCACA,IAAA,kBAAAqc,GAAAI,uBAEA,MAAAJ,EAGA,IAAAK,GAAA,WACA,IAEA,MADAtd,MAAAgQ,MAAA8K,KAAAoC,EAAAC,GAAA,SAAAA,GACAF,EAAAjG,MAAAoG,GAAApd,KAAA+U,WACA,MAAA5U,GAIA,KAHA+b,GAAA/b,IACAH,KAAAwc,UAAArc,GAAA2a,KAAAoC,EAAAC,GAAA,SAAAA,GAEAhd,IAEA4a,KAAA/a,KAGA,OAFAsd,GAAAD,uBAAAJ,EAEAK,GASAC,aAAA,SAAAN,GACA,GAAA,kBAAAA,GACA,KAAA,IAAArc,OAAA,2CACA,OAAA,kBAAAqc,GAAAI,uBACAJ,EAAAI,uBAGAJ,GAYAO,OAAA,SAAAnB,EAAAhN,EAAAoO,EAAAC,GACA,MAAA,IAAAxF,SAAA,SAAAC,EAAAC,GACA,GAAAC,GAAA,GAAAC,eAeA,IAdAD,EAAAG,QAAAJ,EACAC,EAAAI,mBAAA,WACA,IAAAJ,EAAAK,aACAL,EAAAM,QAAA,KAAAN,EAAAM,OAAA,IACAR,EAAAE,EAAAO,cAEAR,EAAA,GAAAxX,OAAA,WAAAyO,EAAA,wBAAAgJ,EAAAM,WAIAN,EAAAE,KAAA,OAAAlJ,GAGAgJ,EAAAsF,iBAAA,eAAA,oBACAD,GAAA,gBAAAA,GAAAE,QAAA,CACA,GAAAA,GAAAF,EAAAE,OACA,KAAA,GAAAC,KAAAD,GACA5X,OAAAJ,UAAAgC,eAAA7G,KAAA6c,EAAAC,IACAxF,EAAAsF,iBAAAE,EAAAD,EAAAC,IAKA,GAAAC,IAAAjc,MAAAwa,EACA7Z,UAAAib,GAAA,OAAAA,IACAK,EAAAla,QAAA6Z,GAGApF,EAAAQ,KAAArL,KAAAuQ,UAAAD,YA+uFGE,qBAAqB,EAAEC,kBAAkB,GAAGC,iBAAiB,UAAU,KAAK", "file": "stacktrace.min.js", "sourcesContent": ["(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c=\"function\"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error(\"Cannot find module '\"+i+\"'\");throw a.code=\"MODULE_NOT_FOUND\",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u=\"function\"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('error-stack-parser', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.ErrorStackParser = factory(root.StackFrame);\n    }\n}(this, function ErrorStackParser(StackFrame) {\n    'use strict';\n\n    var FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+:\\d+/;\n    var CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\n    var SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\n\n    return {\n        /**\n         * Given an Error object, extract the most information from it.\n         *\n         * @param {Error} error object\n         * @return {Array} of StackFrames\n         */\n        parse: function ErrorStackParser$$parse(error) {\n            if (typeof error.stacktrace !== 'undefined' || typeof error['opera#sourceloc'] !== 'undefined') {\n                return this.parseOpera(error);\n            } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n                return this.parseV8OrIE(error);\n            } else if (error.stack) {\n                return this.parseFFOrSafari(error);\n            } else {\n                throw new Error('Cannot parse given Error object');\n            }\n        },\n\n        // Separate line and column numbers from a string of the form: (URI:Line:Column)\n        extractLocation: function ErrorStackParser$$extractLocation(urlLike) {\n            // Fail-fast but return locations like \"(native)\"\n            if (urlLike.indexOf(':') === -1) {\n                return [urlLike];\n            }\n\n            var regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n            var parts = regExp.exec(urlLike.replace(/[()]/g, ''));\n            return [parts[1], parts[2] || undefined, parts[3] || undefined];\n        },\n\n        parseV8OrIE: function ErrorStackParser$$parseV8OrIE(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(CHROME_IE_STACK_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                if (line.indexOf('(eval ') > -1) {\n                    // Throw away eval information until we implement stacktrace.js/stackframe#8\n                    line = line.replace(/eval code/g, 'eval').replace(/(\\(eval at [^()]*)|(\\),.*$)/g, '');\n                }\n                var sanitizedLine = line.replace(/^\\s+/, '').replace(/\\(eval code/g, '(');\n\n                // capture and preseve the parenthesized location \"(/foo/my bar.js:12:87)\" in\n                // case it has spaces in it, as the string is split on \\s+ later on\n                var location = sanitizedLine.match(/ (\\((.+):(\\d+):(\\d+)\\)$)/);\n\n                // remove the parenthesized location from the line, if it was matched\n                sanitizedLine = location ? sanitizedLine.replace(location[0], '') : sanitizedLine;\n\n                var tokens = sanitizedLine.split(/\\s+/).slice(1);\n                // if a location was matched, pass it to extractLocation() otherwise pop the last token\n                var locationParts = this.extractLocation(location ? location[1] : tokens.pop());\n                var functionName = tokens.join(' ') || undefined;\n                var fileName = ['eval', '<anonymous>'].indexOf(locationParts[0]) > -1 ? undefined : locationParts[0];\n\n                return new StackFrame({\n                    functionName: functionName,\n                    fileName: fileName,\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        },\n\n        parseFFOrSafari: function ErrorStackParser$$parseFFOrSafari(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n            }, this);\n\n            return filtered.map(function(line) {\n                // Throw away eval information until we implement stacktrace.js/stackframe#8\n                if (line.indexOf(' > eval') > -1) {\n                    line = line.replace(/ line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g, ':$1');\n                }\n\n                if (line.indexOf('@') === -1 && line.indexOf(':') === -1) {\n                    // Safari eval frames only have function names and nothing else\n                    return new StackFrame({\n                        functionName: line\n                    });\n                } else {\n                    var functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n                    var matches = line.match(functionNameRegex);\n                    var functionName = matches && matches[1] ? matches[1] : undefined;\n                    var locationParts = this.extractLocation(line.replace(functionNameRegex, ''));\n\n                    return new StackFrame({\n                        functionName: functionName,\n                        fileName: locationParts[0],\n                        lineNumber: locationParts[1],\n                        columnNumber: locationParts[2],\n                        source: line\n                    });\n                }\n            }, this);\n        },\n\n        parseOpera: function ErrorStackParser$$parseOpera(e) {\n            if (!e.stacktrace || (e.message.indexOf('\\n') > -1 &&\n                e.message.split('\\n').length > e.stacktrace.split('\\n').length)) {\n                return this.parseOpera9(e);\n            } else if (!e.stack) {\n                return this.parseOpera10(e);\n            } else {\n                return this.parseOpera11(e);\n            }\n        },\n\n        parseOpera9: function ErrorStackParser$$parseOpera9(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n            var lines = e.message.split('\\n');\n            var result = [];\n\n            for (var i = 2, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(new StackFrame({\n                        fileName: match[2],\n                        lineNumber: match[1],\n                        source: lines[i]\n                    }));\n                }\n            }\n\n            return result;\n        },\n\n        parseOpera10: function ErrorStackParser$$parseOpera10(e) {\n            var lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n            var lines = e.stacktrace.split('\\n');\n            var result = [];\n\n            for (var i = 0, len = lines.length; i < len; i += 2) {\n                var match = lineRE.exec(lines[i]);\n                if (match) {\n                    result.push(\n                        new StackFrame({\n                            functionName: match[3] || undefined,\n                            fileName: match[2],\n                            lineNumber: match[1],\n                            source: lines[i]\n                        })\n                    );\n                }\n            }\n\n            return result;\n        },\n\n        // Opera 10.65+ Error.stack very similar to FF/Safari\n        parseOpera11: function ErrorStackParser$$parseOpera11(error) {\n            var filtered = error.stack.split('\\n').filter(function(line) {\n                return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n            }, this);\n\n            return filtered.map(function(line) {\n                var tokens = line.split('@');\n                var locationParts = this.extractLocation(tokens.pop());\n                var functionCall = (tokens.shift() || '');\n                var functionName = functionCall\n                    .replace(/<anonymous function(: (\\w+))?>/, '$2')\n                    .replace(/\\([^)]*\\)/g, '') || undefined;\n                var argsRaw;\n                if (functionCall.match(/\\(([^)]*)\\)/)) {\n                    argsRaw = functionCall.replace(/^[^(]+\\(([^)]*)\\)$/, '$1');\n                }\n                var args = (argsRaw === undefined || argsRaw === '[arguments not available]') ?\n                    undefined : argsRaw.split(',');\n\n                return new StackFrame({\n                    functionName: functionName,\n                    args: args,\n                    fileName: locationParts[0],\n                    lineNumber: locationParts[1],\n                    columnNumber: locationParts[2],\n                    source: line\n                });\n            }, this);\n        }\n    };\n}));\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stacktrace', ['error-stack-parser', 'stack-generator', 'stacktrace-gps'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('error-stack-parser'), require('stack-generator'), require('stacktrace-gps'));\n    } else {\n        root.StackTrace = factory(root.ErrorStackParser, root.StackGenerator, root.StackTraceGPS);\n    }\n}(this, function StackTrace(ErrorStackParser, StackGenerator, StackTraceGPS) {\n    var _options = {\n        filter: function(stackframe) {\n            // Filter out stackframes for this library by default\n            return (stackframe.functionName || '').indexOf('StackTrace$$') === -1 &&\n                (stackframe.functionName || '').indexOf('ErrorStackParser$$') === -1 &&\n                (stackframe.functionName || '').indexOf('StackTraceGPS$$') === -1 &&\n                (stackframe.functionName || '').indexOf('StackGenerator$$') === -1;\n        },\n        sourceCache: {}\n    };\n\n    var _generateError = function StackTrace$$GenerateError() {\n        try {\n            // Error must be thrown to get stack in IE\n            throw new Error();\n        } catch (err) {\n            return err;\n        }\n    };\n\n    /**\n     * Merge 2 given Objects. If a conflict occurs the second object wins.\n     * Does not do deep merges.\n     *\n     * @param {Object} first base object\n     * @param {Object} second overrides\n     * @returns {Object} merged first and second\n     * @private\n     */\n    function _merge(first, second) {\n        var target = {};\n\n        [first, second].forEach(function(obj) {\n            for (var prop in obj) {\n                if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n                    target[prop] = obj[prop];\n                }\n            }\n            return target;\n        });\n\n        return target;\n    }\n\n    function _isShapedLikeParsableError(err) {\n        return err.stack || err['opera#sourceloc'];\n    }\n\n    function _filtered(stackframes, filter) {\n        if (typeof filter === 'function') {\n            return stackframes.filter(filter);\n        }\n        return stackframes;\n    }\n\n    return {\n        /**\n         * Get a backtrace from invocation point.\n         *\n         * @param {Object} opts\n         * @returns {Array} of StackFrame\n         */\n        get: function StackTrace$$get(opts) {\n            var err = _generateError();\n            return _isShapedLikeParsableError(err) ? this.fromError(err, opts) : this.generateArtificially(opts);\n        },\n\n        /**\n         * Get a backtrace from invocation point.\n         * IMPORTANT: Does not handle source maps or guess function names!\n         *\n         * @param {Object} opts\n         * @returns {Array} of StackFrame\n         */\n        getSync: function StackTrace$$getSync(opts) {\n            opts = _merge(_options, opts);\n            var err = _generateError();\n            var stack = _isShapedLikeParsableError(err) ? ErrorStackParser.parse(err) : StackGenerator.backtrace(opts);\n            return _filtered(stack, opts.filter);\n        },\n\n        /**\n         * Given an error object, parse it.\n         *\n         * @param {Error} error object\n         * @param {Object} opts\n         * @returns {Promise} for Array[StackFrame}\n         */\n        fromError: function StackTrace$$fromError(error, opts) {\n            opts = _merge(_options, opts);\n            var gps = new StackTraceGPS(opts);\n            return new Promise(function(resolve) {\n                var stackframes = _filtered(ErrorStackParser.parse(error), opts.filter);\n                resolve(Promise.all(stackframes.map(function(sf) {\n                    return new Promise(function(resolve) {\n                        function resolveOriginal() {\n                            resolve(sf);\n                        }\n\n                        gps.pinpoint(sf).then(resolve, resolveOriginal)['catch'](resolveOriginal);\n                    });\n                })));\n            }.bind(this));\n        },\n\n        /**\n         * Use StackGenerator to generate a backtrace.\n         *\n         * @param {Object} opts\n         * @returns {Promise} of Array[StackFrame]\n         */\n        generateArtificially: function StackTrace$$generateArtificially(opts) {\n            opts = _merge(_options, opts);\n            var stackFrames = StackGenerator.backtrace(opts);\n            if (typeof opts.filter === 'function') {\n                stackFrames = stackFrames.filter(opts.filter);\n            }\n            return Promise.resolve(stackFrames);\n        },\n\n        /**\n         * Given a function, wrap it such that invocations trigger a callback that\n         * is called with a stack trace.\n         *\n         * @param {Function} fn to be instrumented\n         * @param {Function} callback function to call with a stack trace on invocation\n         * @param {Function} errback optional function to call with error if unable to get stack trace.\n         * @param {Object} thisArg optional context object (e.g. window)\n         */\n        instrument: function StackTrace$$instrument(fn, callback, errback, thisArg) {\n            if (typeof fn !== 'function') {\n                throw new Error('Cannot instrument non-function object');\n            } else if (typeof fn.__stacktraceOriginalFn === 'function') {\n                // Already instrumented, return given Function\n                return fn;\n            }\n\n            var instrumented = function StackTrace$$instrumented() {\n                try {\n                    this.get().then(callback, errback)['catch'](errback);\n                    return fn.apply(thisArg || this, arguments);\n                } catch (e) {\n                    if (_isShapedLikeParsableError(e)) {\n                        this.fromError(e).then(callback, errback)['catch'](errback);\n                    }\n                    throw e;\n                }\n            }.bind(this);\n            instrumented.__stacktraceOriginalFn = fn;\n\n            return instrumented;\n        },\n\n        /**\n         * Given a function that has been instrumented,\n         * revert the function to it's original (non-instrumented) state.\n         *\n         * @param {Function} fn to de-instrument\n         */\n        deinstrument: function StackTrace$$deinstrument(fn) {\n            if (typeof fn !== 'function') {\n                throw new Error('Cannot de-instrument non-function object');\n            } else if (typeof fn.__stacktraceOriginalFn === 'function') {\n                return fn.__stacktraceOriginalFn;\n            } else {\n                // Function not instrumented, return original\n                return fn;\n            }\n        },\n\n        /**\n         * Given an error message and Array of StackFrames, serialize and POST to given URL.\n         *\n         * @param {Array} stackframes\n         * @param {String} url\n         * @param {String} errorMsg\n         * @param {Object} requestOptions\n         */\n        report: function StackTrace$$report(stackframes, url, errorMsg, requestOptions) {\n            return new Promise(function(resolve, reject) {\n                var req = new XMLHttpRequest();\n                req.onerror = reject;\n                req.onreadystatechange = function onreadystatechange() {\n                    if (req.readyState === 4) {\n                        if (req.status >= 200 && req.status < 400) {\n                            resolve(req.responseText);\n                        } else {\n                            reject(new Error('POST to ' + url + ' failed with status: ' + req.status));\n                        }\n                    }\n                };\n                req.open('post', url);\n\n                // Set request headers\n                req.setRequestHeader('Content-Type', 'application/json');\n                if (requestOptions && typeof requestOptions.headers === 'object') {\n                    var headers = requestOptions.headers;\n                    for (var header in headers) {\n                        if (Object.prototype.hasOwnProperty.call(headers, header)) {\n                            req.setRequestHeader(header, headers[header]);\n                        }\n                    }\n                }\n\n                var reportPayload = {stack: stackframes};\n                if (errorMsg !== undefined && errorMsg !== null) {\n                    reportPayload.message = errorMsg;\n                }\n\n                req.send(JSON.stringify(reportPayload));\n            });\n        }\n    };\n}));\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stackframe', [], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory();\n    } else {\n        root.StackFrame = factory();\n    }\n}(this, function() {\n    'use strict';\n    function _isNumber(n) {\n        return !isNaN(parseFloat(n)) && isFinite(n);\n    }\n\n    function _capitalize(str) {\n        return str.charAt(0).toUpperCase() + str.substring(1);\n    }\n\n    function _getter(p) {\n        return function() {\n            return this[p];\n        };\n    }\n\n    var booleanProps = ['isConstructor', 'isEval', 'isNative', 'isToplevel'];\n    var numericProps = ['columnNumber', 'lineNumber'];\n    var stringProps = ['fileName', 'functionName', 'source'];\n    var arrayProps = ['args'];\n\n    var props = booleanProps.concat(numericProps, stringProps, arrayProps);\n\n    function StackFrame(obj) {\n        if (!obj) return;\n        for (var i = 0; i < props.length; i++) {\n            if (obj[props[i]] !== undefined) {\n                this['set' + _capitalize(props[i])](obj[props[i]]);\n            }\n        }\n    }\n\n    StackFrame.prototype = {\n        getArgs: function() {\n            return this.args;\n        },\n        setArgs: function(v) {\n            if (Object.prototype.toString.call(v) !== '[object Array]') {\n                throw new TypeError('Args must be an Array');\n            }\n            this.args = v;\n        },\n\n        getEvalOrigin: function() {\n            return this.evalOrigin;\n        },\n        setEvalOrigin: function(v) {\n            if (v instanceof StackFrame) {\n                this.evalOrigin = v;\n            } else if (v instanceof Object) {\n                this.evalOrigin = new StackFrame(v);\n            } else {\n                throw new TypeError('Eval Origin must be an Object or StackFrame');\n            }\n        },\n\n        toString: function() {\n            var fileName = this.getFileName() || '';\n            var lineNumber = this.getLineNumber() || '';\n            var columnNumber = this.getColumnNumber() || '';\n            var functionName = this.getFunctionName() || '';\n            if (this.getIsEval()) {\n                if (fileName) {\n                    return '[eval] (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n                }\n                return '[eval]:' + lineNumber + ':' + columnNumber;\n            }\n            if (functionName) {\n                return functionName + ' (' + fileName + ':' + lineNumber + ':' + columnNumber + ')';\n            }\n            return fileName + ':' + lineNumber + ':' + columnNumber;\n        }\n    };\n\n    StackFrame.fromString = function StackFrame$$fromString(str) {\n        var argsStartIndex = str.indexOf('(');\n        var argsEndIndex = str.lastIndexOf(')');\n\n        var functionName = str.substring(0, argsStartIndex);\n        var args = str.substring(argsStartIndex + 1, argsEndIndex).split(',');\n        var locationString = str.substring(argsEndIndex + 1);\n\n        if (locationString.indexOf('@') === 0) {\n            var parts = /@(.+?)(?::(\\d+))?(?::(\\d+))?$/.exec(locationString, '');\n            var fileName = parts[1];\n            var lineNumber = parts[2];\n            var columnNumber = parts[3];\n        }\n\n        return new StackFrame({\n            functionName: functionName,\n            args: args || undefined,\n            fileName: fileName,\n            lineNumber: lineNumber || undefined,\n            columnNumber: columnNumber || undefined\n        });\n    };\n\n    for (var i = 0; i < booleanProps.length; i++) {\n        StackFrame.prototype['get' + _capitalize(booleanProps[i])] = _getter(booleanProps[i]);\n        StackFrame.prototype['set' + _capitalize(booleanProps[i])] = (function(p) {\n            return function(v) {\n                this[p] = Boolean(v);\n            };\n        })(booleanProps[i]);\n    }\n\n    for (var j = 0; j < numericProps.length; j++) {\n        StackFrame.prototype['get' + _capitalize(numericProps[j])] = _getter(numericProps[j]);\n        StackFrame.prototype['set' + _capitalize(numericProps[j])] = (function(p) {\n            return function(v) {\n                if (!_isNumber(v)) {\n                    throw new TypeError(p + ' must be a Number');\n                }\n                this[p] = Number(v);\n            };\n        })(numericProps[j]);\n    }\n\n    for (var k = 0; k < stringProps.length; k++) {\n        StackFrame.prototype['get' + _capitalize(stringProps[k])] = _getter(stringProps[k]);\n        StackFrame.prototype['set' + _capitalize(stringProps[k])] = (function(p) {\n            return function(v) {\n                this[p] = String(v);\n            };\n        })(stringProps[k]);\n    }\n\n    return StackFrame;\n}));\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar has = Object.prototype.hasOwnProperty;\n\n/**\n * A data structure which is a combination of an array and a set. Adding a new\n * member is O(1), testing for membership is O(1), and finding the index of an\n * element is O(1). Removing elements from the set is not supported. Only\n * strings are supported for membership.\n */\nfunction ArraySet() {\n  this._array = [];\n  this._set = Object.create(null);\n}\n\n/**\n * Static method for creating ArraySet instances from an existing array.\n */\nArraySet.fromArray = function ArraySet_fromArray(aArray, aAllowDuplicates) {\n  var set = new ArraySet();\n  for (var i = 0, len = aArray.length; i < len; i++) {\n    set.add(aArray[i], aAllowDuplicates);\n  }\n  return set;\n};\n\n/**\n * Return how many unique items are in this ArraySet. If duplicates have been\n * added, than those do not count towards the size.\n *\n * @returns Number\n */\nArraySet.prototype.size = function ArraySet_size() {\n  return Object.getOwnPropertyNames(this._set).length;\n};\n\n/**\n * Add the given string to this set.\n *\n * @param String aStr\n */\nArraySet.prototype.add = function ArraySet_add(aStr, aAllowDuplicates) {\n  var sStr = util.toSetString(aStr);\n  var isDuplicate = has.call(this._set, sStr);\n  var idx = this._array.length;\n  if (!isDuplicate || aAllowDuplicates) {\n    this._array.push(aStr);\n  }\n  if (!isDuplicate) {\n    this._set[sStr] = idx;\n  }\n};\n\n/**\n * Is the given string a member of this set?\n *\n * @param String aStr\n */\nArraySet.prototype.has = function ArraySet_has(aStr) {\n  var sStr = util.toSetString(aStr);\n  return has.call(this._set, sStr);\n};\n\n/**\n * What is the index of the given string in the array?\n *\n * @param String aStr\n */\nArraySet.prototype.indexOf = function ArraySet_indexOf(aStr) {\n  var sStr = util.toSetString(aStr);\n  if (has.call(this._set, sStr)) {\n    return this._set[sStr];\n  }\n  throw new Error('\"' + aStr + '\" is not in the set.');\n};\n\n/**\n * What is the element at the given index?\n *\n * @param Number aIdx\n */\nArraySet.prototype.at = function ArraySet_at(aIdx) {\n  if (aIdx >= 0 && aIdx < this._array.length) {\n    return this._array[aIdx];\n  }\n  throw new Error('No element indexed by ' + aIdx);\n};\n\n/**\n * Returns the array representation of this set (which has the proper indices\n * indicated by indexOf). Note that this is a copy of the internal array used\n * for storing the members so that no one can mess with internal state.\n */\nArraySet.prototype.toArray = function ArraySet_toArray() {\n  return this._array.slice();\n};\n\nexports.ArraySet = ArraySet;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n *\n * Based on the Base 64 VLQ implementation in Closure Compiler:\n * https://code.google.com/p/closure-compiler/source/browse/trunk/src/com/google/debugging/sourcemap/Base64VLQ.java\n *\n * Copyright 2011 The Closure Compiler Authors. All rights reserved.\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are\n * met:\n *\n *  * Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n *  * Redistributions in binary form must reproduce the above\n *    copyright notice, this list of conditions and the following\n *    disclaimer in the documentation and/or other materials provided\n *    with the distribution.\n *  * Neither the name of Google Inc. nor the names of its\n *    contributors may be used to endorse or promote products derived\n *    from this software without specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n * \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar base64 = require('./base64');\n\n// A single base 64 digit can contain 6 bits of data. For the base 64 variable\n// length quantities we use in the source map spec, the first bit is the sign,\n// the next four bits are the actual value, and the 6th bit is the\n// continuation bit. The continuation bit tells us whether there are more\n// digits in this value following this digit.\n//\n//   Continuation\n//   |    Sign\n//   |    |\n//   V    V\n//   101011\n\nvar VLQ_BASE_SHIFT = 5;\n\n// binary: 100000\nvar VLQ_BASE = 1 << VLQ_BASE_SHIFT;\n\n// binary: 011111\nvar VLQ_BASE_MASK = VLQ_BASE - 1;\n\n// binary: 100000\nvar VLQ_CONTINUATION_BIT = VLQ_BASE;\n\n/**\n * Converts from a two-complement value to a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   1 becomes 2 (10 binary), -1 becomes 3 (11 binary)\n *   2 becomes 4 (100 binary), -2 becomes 5 (101 binary)\n */\nfunction toVLQSigned(aValue) {\n  return aValue < 0\n    ? ((-aValue) << 1) + 1\n    : (aValue << 1) + 0;\n}\n\n/**\n * Converts to a two-complement value from a value where the sign bit is\n * placed in the least significant bit.  For example, as decimals:\n *   2 (10 binary) becomes 1, 3 (11 binary) becomes -1\n *   4 (100 binary) becomes 2, 5 (101 binary) becomes -2\n */\nfunction fromVLQSigned(aValue) {\n  var isNegative = (aValue & 1) === 1;\n  var shifted = aValue >> 1;\n  return isNegative\n    ? -shifted\n    : shifted;\n}\n\n/**\n * Returns the base 64 VLQ encoded value.\n */\nexports.encode = function base64VLQ_encode(aValue) {\n  var encoded = \"\";\n  var digit;\n\n  var vlq = toVLQSigned(aValue);\n\n  do {\n    digit = vlq & VLQ_BASE_MASK;\n    vlq >>>= VLQ_BASE_SHIFT;\n    if (vlq > 0) {\n      // There are still more digits in this value, so we must make sure the\n      // continuation bit is marked.\n      digit |= VLQ_CONTINUATION_BIT;\n    }\n    encoded += base64.encode(digit);\n  } while (vlq > 0);\n\n  return encoded;\n};\n\n/**\n * Decodes the next base 64 VLQ value from the given string and returns the\n * value and the rest of the string via the out parameter.\n */\nexports.decode = function base64VLQ_decode(aStr, aIndex, aOutParam) {\n  var strLen = aStr.length;\n  var result = 0;\n  var shift = 0;\n  var continuation, digit;\n\n  do {\n    if (aIndex >= strLen) {\n      throw new Error(\"Expected more digits in base 64 VLQ value.\");\n    }\n\n    digit = base64.decode(aStr.charCodeAt(aIndex++));\n    if (digit === -1) {\n      throw new Error(\"Invalid base64 digit: \" + aStr.charAt(aIndex - 1));\n    }\n\n    continuation = !!(digit & VLQ_CONTINUATION_BIT);\n    digit &= VLQ_BASE_MASK;\n    result = result + (digit << shift);\n    shift += VLQ_BASE_SHIFT;\n  } while (continuation);\n\n  aOutParam.value = fromVLQSigned(result);\n  aOutParam.rest = aIndex;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar intToCharMap = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/'.split('');\n\n/**\n * Encode an integer in the range of 0 to 63 to a single base 64 digit.\n */\nexports.encode = function (number) {\n  if (0 <= number && number < intToCharMap.length) {\n    return intToCharMap[number];\n  }\n  throw new TypeError(\"Must be between 0 and 63: \" + number);\n};\n\n/**\n * Decode a single base 64 character code digit to an integer. Returns -1 on\n * failure.\n */\nexports.decode = function (charCode) {\n  var bigA = 65;     // 'A'\n  var bigZ = 90;     // 'Z'\n\n  var littleA = 97;  // 'a'\n  var littleZ = 122; // 'z'\n\n  var zero = 48;     // '0'\n  var nine = 57;     // '9'\n\n  var plus = 43;     // '+'\n  var slash = 47;    // '/'\n\n  var littleOffset = 26;\n  var numberOffset = 52;\n\n  // 0 - 25: ABCDEFGHIJKLMNOPQRSTUVWXYZ\n  if (bigA <= charCode && charCode <= bigZ) {\n    return (charCode - bigA);\n  }\n\n  // 26 - 51: abcdefghijklmnopqrstuvwxyz\n  if (littleA <= charCode && charCode <= littleZ) {\n    return (charCode - littleA + littleOffset);\n  }\n\n  // 52 - 61: **********\n  if (zero <= charCode && charCode <= nine) {\n    return (charCode - zero + numberOffset);\n  }\n\n  // 62: +\n  if (charCode == plus) {\n    return 62;\n  }\n\n  // 63: /\n  if (charCode == slash) {\n    return 63;\n  }\n\n  // Invalid base64 digit.\n  return -1;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nexports.GREATEST_LOWER_BOUND = 1;\nexports.LEAST_UPPER_BOUND = 2;\n\n/**\n * Recursive implementation of binary search.\n *\n * @param aLow Indices here and lower do not contain the needle.\n * @param aHigh Indices here and higher do not contain the needle.\n * @param aNeedle The element being searched for.\n * @param aHaystack The non-empty array being searched.\n * @param aCompare Function which takes two elements and returns -1, 0, or 1.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n */\nfunction recursiveSearch(aLow, aHigh, a<PERSON>eed<PERSON>, aHaystack, aCompare, a<PERSON><PERSON>) {\n  // This function terminates when one of the following is true:\n  //\n  //   1. We find the exact element we are looking for.\n  //\n  //   2. We did not find the exact element, but we can return the index of\n  //      the next-closest element.\n  //\n  //   3. We did not find the exact element, and there is no next-closest\n  //      element than the one we are searching for, so we return -1.\n  var mid = Math.floor((aHigh - aLow) / 2) + aLow;\n  var cmp = aCompare(aNeedle, aHaystack[mid], true);\n  if (cmp === 0) {\n    // Found the element we are looking for.\n    return mid;\n  }\n  else if (cmp > 0) {\n    // Our needle is greater than aHaystack[mid].\n    if (aHigh - mid > 1) {\n      // The element is in the upper half.\n      return recursiveSearch(mid, aHigh, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // The exact needle element was not found in this haystack. Determine if\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return aHigh < aHaystack.length ? aHigh : -1;\n    } else {\n      return mid;\n    }\n  }\n  else {\n    // Our needle is less than aHaystack[mid].\n    if (mid - aLow > 1) {\n      // The element is in the lower half.\n      return recursiveSearch(aLow, mid, aNeedle, aHaystack, aCompare, aBias);\n    }\n\n    // we are in termination case (3) or (2) and return the appropriate thing.\n    if (aBias == exports.LEAST_UPPER_BOUND) {\n      return mid;\n    } else {\n      return aLow < 0 ? -1 : aLow;\n    }\n  }\n}\n\n/**\n * This is an implementation of binary search which will always try and return\n * the index of the closest element if there is no exact hit. This is because\n * mappings between original and generated line/col pairs are single points,\n * and there is an implicit region between each of them, so a miss just means\n * that you aren't on the very start of a region.\n *\n * @param aNeedle The element you are looking for.\n * @param aHaystack The array that is being searched.\n * @param aCompare A function which takes the needle and an element in the\n *     array and returns -1, 0, or 1 depending on whether the needle is less\n *     than, equal to, or greater than the element, respectively.\n * @param aBias Either 'binarySearch.GREATEST_LOWER_BOUND' or\n *     'binarySearch.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'binarySearch.GREATEST_LOWER_BOUND'.\n */\nexports.search = function search(aNeedle, aHaystack, aCompare, aBias) {\n  if (aHaystack.length === 0) {\n    return -1;\n  }\n\n  var index = recursiveSearch(-1, aHaystack.length, aNeedle, aHaystack,\n                              aCompare, aBias || exports.GREATEST_LOWER_BOUND);\n  if (index < 0) {\n    return -1;\n  }\n\n  // We have found either the exact element, or the next-closest element than\n  // the one we are searching for. However, there may be more than one such\n  // element. Make sure we always return the smallest of these.\n  while (index - 1 >= 0) {\n    if (aCompare(aHaystack[index], aHaystack[index - 1], true) !== 0) {\n      break;\n    }\n    --index;\n  }\n\n  return index;\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n// It turns out that some (most?) JavaScript engines don't self-host\n// `Array.prototype.sort`. This makes sense because C++ will likely remain\n// faster than JS when doing raw CPU-intensive sorting. However, when using a\n// custom comparator function, calling back and forth between the VM's C++ and\n// JIT'd JS is rather slow *and* loses JIT type information, resulting in\n// worse generated code for the comparator function than would be optimal. In\n// fact, when sorting with a comparator, these costs outweigh the benefits of\n// sorting in C++. By using our own JS-implemented Quick Sort (below), we get\n// a ~3500ms mean speed-up in `bench/bench.html`.\n\n/**\n * Swap the elements indexed by `x` and `y` in the array `ary`.\n *\n * @param {Array} ary\n *        The array.\n * @param {Number} x\n *        The index of the first item.\n * @param {Number} y\n *        The index of the second item.\n */\nfunction swap(ary, x, y) {\n  var temp = ary[x];\n  ary[x] = ary[y];\n  ary[y] = temp;\n}\n\n/**\n * Returns a random integer within the range `low .. high` inclusive.\n *\n * @param {Number} low\n *        The lower bound on the range.\n * @param {Number} high\n *        The upper bound on the range.\n */\nfunction randomIntInRange(low, high) {\n  return Math.round(low + (Math.random() * (high - low)));\n}\n\n/**\n * The Quick Sort algorithm.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n * @param {Number} p\n *        Start index of the array\n * @param {Number} r\n *        End index of the array\n */\nfunction doQuickSort(ary, comparator, p, r) {\n  // If our lower bound is less than our upper bound, we (1) partition the\n  // array into two pieces and (2) recurse on each half. If it is not, this is\n  // the empty array and our base case.\n\n  if (p < r) {\n    // (1) Partitioning.\n    //\n    // The partitioning chooses a pivot between `p` and `r` and moves all\n    // elements that are less than or equal to the pivot to the before it, and\n    // all the elements that are greater than it after it. The effect is that\n    // once partition is done, the pivot is in the exact place it will be when\n    // the array is put in sorted order, and it will not need to be moved\n    // again. This runs in O(n) time.\n\n    // Always choose a random pivot so that an input array which is reverse\n    // sorted does not cause O(n^2) running time.\n    var pivotIndex = randomIntInRange(p, r);\n    var i = p - 1;\n\n    swap(ary, pivotIndex, r);\n    var pivot = ary[r];\n\n    // Immediately after `j` is incremented in this loop, the following hold\n    // true:\n    //\n    //   * Every element in `ary[p .. i]` is less than or equal to the pivot.\n    //\n    //   * Every element in `ary[i+1 .. j-1]` is greater than the pivot.\n    for (var j = p; j < r; j++) {\n      if (comparator(ary[j], pivot) <= 0) {\n        i += 1;\n        swap(ary, i, j);\n      }\n    }\n\n    swap(ary, i + 1, j);\n    var q = i + 1;\n\n    // (2) Recurse on each half.\n\n    doQuickSort(ary, comparator, p, q - 1);\n    doQuickSort(ary, comparator, q + 1, r);\n  }\n}\n\n/**\n * Sort the given array in-place with the given comparator function.\n *\n * @param {Array} ary\n *        An array to sort.\n * @param {function} comparator\n *        Function to use to compare two items.\n */\nexports.quickSort = function (ary, comparator) {\n  doQuickSort(ary, comparator, 0, ary.length - 1);\n};\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\nvar util = require('./util');\nvar binarySearch = require('./binary-search');\nvar ArraySet = require('./array-set').ArraySet;\nvar base64VLQ = require('./base64-vlq');\nvar quickSort = require('./quick-sort').quickSort;\n\nfunction SourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  return sourceMap.sections != null\n    ? new IndexedSourceMapConsumer(sourceMap)\n    : new BasicSourceMapConsumer(sourceMap);\n}\n\nSourceMapConsumer.fromSourceMap = function(aSourceMap) {\n  return BasicSourceMapConsumer.fromSourceMap(aSourceMap);\n}\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nSourceMapConsumer.prototype._version = 3;\n\n// `__generatedMappings` and `__originalMappings` are arrays that hold the\n// parsed mapping coordinates from the source map's \"mappings\" attribute. They\n// are lazily instantiated, accessed via the `_generatedMappings` and\n// `_originalMappings` getters respectively, and we only parse the mappings\n// and create these arrays once queried for a source location. We jump through\n// these hoops because there can be many thousands of mappings, and parsing\n// them is expensive, so we only want to do it if we must.\n//\n// Each object in the arrays is of the form:\n//\n//     {\n//       generatedLine: The line number in the generated code,\n//       generatedColumn: The column number in the generated code,\n//       source: The path to the original source file that generated this\n//               chunk of code,\n//       originalLine: The line number in the original source that\n//                     corresponds to this chunk of generated code,\n//       originalColumn: The column number in the original source that\n//                       corresponds to this chunk of generated code,\n//       name: The name of the original symbol which generated this chunk of\n//             code.\n//     }\n//\n// All properties except for `generatedLine` and `generatedColumn` can be\n// `null`.\n//\n// `_generatedMappings` is ordered by the generated positions.\n//\n// `_originalMappings` is ordered by the original positions.\n\nSourceMapConsumer.prototype.__generatedMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_generatedMappings', {\n  get: function () {\n    if (!this.__generatedMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__generatedMappings;\n  }\n});\n\nSourceMapConsumer.prototype.__originalMappings = null;\nObject.defineProperty(SourceMapConsumer.prototype, '_originalMappings', {\n  get: function () {\n    if (!this.__originalMappings) {\n      this._parseMappings(this._mappings, this.sourceRoot);\n    }\n\n    return this.__originalMappings;\n  }\n});\n\nSourceMapConsumer.prototype._charIsMappingSeparator =\n  function SourceMapConsumer_charIsMappingSeparator(aStr, index) {\n    var c = aStr.charAt(index);\n    return c === \";\" || c === \",\";\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    throw new Error(\"Subclasses must implement _parseMappings\");\n  };\n\nSourceMapConsumer.GENERATED_ORDER = 1;\nSourceMapConsumer.ORIGINAL_ORDER = 2;\n\nSourceMapConsumer.GREATEST_LOWER_BOUND = 1;\nSourceMapConsumer.LEAST_UPPER_BOUND = 2;\n\n/**\n * Iterate over each mapping between an original source/line/column and a\n * generated line/column in this source map.\n *\n * @param Function aCallback\n *        The function that is called with each mapping.\n * @param Object aContext\n *        Optional. If specified, this object will be the value of `this` every\n *        time that `aCallback` is called.\n * @param aOrder\n *        Either `SourceMapConsumer.GENERATED_ORDER` or\n *        `SourceMapConsumer.ORIGINAL_ORDER`. Specifies whether you want to\n *        iterate over the mappings sorted by the generated file's line/column\n *        order or the original's source/line/column order, respectively. Defaults to\n *        `SourceMapConsumer.GENERATED_ORDER`.\n */\nSourceMapConsumer.prototype.eachMapping =\n  function SourceMapConsumer_eachMapping(aCallback, aContext, aOrder) {\n    var context = aContext || null;\n    var order = aOrder || SourceMapConsumer.GENERATED_ORDER;\n\n    var mappings;\n    switch (order) {\n    case SourceMapConsumer.GENERATED_ORDER:\n      mappings = this._generatedMappings;\n      break;\n    case SourceMapConsumer.ORIGINAL_ORDER:\n      mappings = this._originalMappings;\n      break;\n    default:\n      throw new Error(\"Unknown order of iteration.\");\n    }\n\n    var sourceRoot = this.sourceRoot;\n    mappings.map(function (mapping) {\n      var source = mapping.source === null ? null : this._sources.at(mapping.source);\n      if (source != null && sourceRoot != null) {\n        source = util.join(sourceRoot, source);\n      }\n      return {\n        source: source,\n        generatedLine: mapping.generatedLine,\n        generatedColumn: mapping.generatedColumn,\n        originalLine: mapping.originalLine,\n        originalColumn: mapping.originalColumn,\n        name: mapping.name === null ? null : this._names.at(mapping.name)\n      };\n    }, this).forEach(aCallback, context);\n  };\n\n/**\n * Returns all generated line and column information for the original source,\n * line, and column provided. If no column is provided, returns all mappings\n * corresponding to a either the line we are searching for or the next\n * closest line that has any mappings. Otherwise, returns all mappings\n * corresponding to the given line and either the column we are searching for\n * or the next closest column that has any offsets.\n *\n * The only argument is an object with the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: Optional. the column number in the original source.\n *\n * and an array of objects is returned, each with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nSourceMapConsumer.prototype.allGeneratedPositionsFor =\n  function SourceMapConsumer_allGeneratedPositionsFor(aArgs) {\n    var line = util.getArg(aArgs, 'line');\n\n    // When there is no exact match, BasicSourceMapConsumer.prototype._findMapping\n    // returns the index of the closest mapping less than the needle. By\n    // setting needle.originalColumn to 0, we thus find the last mapping for\n    // the given line, provided such a mapping exists.\n    var needle = {\n      source: util.getArg(aArgs, 'source'),\n      originalLine: line,\n      originalColumn: util.getArg(aArgs, 'column', 0)\n    };\n\n    if (this.sourceRoot != null) {\n      needle.source = util.relative(this.sourceRoot, needle.source);\n    }\n    if (!this._sources.has(needle.source)) {\n      return [];\n    }\n    needle.source = this._sources.indexOf(needle.source);\n\n    var mappings = [];\n\n    var index = this._findMapping(needle,\n                                  this._originalMappings,\n                                  \"originalLine\",\n                                  \"originalColumn\",\n                                  util.compareByOriginalPositions,\n                                  binarySearch.LEAST_UPPER_BOUND);\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (aArgs.column === undefined) {\n        var originalLine = mapping.originalLine;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we found. Since\n        // mappings are sorted, this is guaranteed to find all mappings for\n        // the line we found.\n        while (mapping && mapping.originalLine === originalLine) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      } else {\n        var originalColumn = mapping.originalColumn;\n\n        // Iterate until either we run out of mappings, or we run into\n        // a mapping for a different line than the one we were searching for.\n        // Since mappings are sorted, this is guaranteed to find all mappings for\n        // the line we are searching for.\n        while (mapping &&\n               mapping.originalLine === line &&\n               mapping.originalColumn == originalColumn) {\n          mappings.push({\n            line: util.getArg(mapping, 'generatedLine', null),\n            column: util.getArg(mapping, 'generatedColumn', null),\n            lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n          });\n\n          mapping = this._originalMappings[++index];\n        }\n      }\n    }\n\n    return mappings;\n  };\n\nexports.SourceMapConsumer = SourceMapConsumer;\n\n/**\n * A BasicSourceMapConsumer instance represents a parsed source map which we can\n * query for information about the original file positions by giving it a file\n * position in the generated source.\n *\n * The only parameter is the raw source map (either as a JSON string, or\n * already parsed to an object). According to the spec, source maps have the\n * following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - sources: An array of URLs to the original source files.\n *   - names: An array of identifiers which can be referrenced by individual mappings.\n *   - sourceRoot: Optional. The URL root from which all sources are relative.\n *   - sourcesContent: Optional. An array of contents of the original source files.\n *   - mappings: A string of base64 VLQs which contain the actual mappings.\n *   - file: Optional. The generated file this source map is associated with.\n *\n * Here is an example source map, taken from the source map spec[0]:\n *\n *     {\n *       version : 3,\n *       file: \"out.js\",\n *       sourceRoot : \"\",\n *       sources: [\"foo.js\", \"bar.js\"],\n *       names: [\"src\", \"maps\", \"are\", \"fun\"],\n *       mappings: \"AA,AB;;ABCDE;\"\n *     }\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit?pli=1#\n */\nfunction BasicSourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sources = util.getArg(sourceMap, 'sources');\n  // Sass 3.3 leaves out the 'names' array, so we deviate from the spec (which\n  // requires the array) to play nice here.\n  var names = util.getArg(sourceMap, 'names', []);\n  var sourceRoot = util.getArg(sourceMap, 'sourceRoot', null);\n  var sourcesContent = util.getArg(sourceMap, 'sourcesContent', null);\n  var mappings = util.getArg(sourceMap, 'mappings');\n  var file = util.getArg(sourceMap, 'file', null);\n\n  // Once again, Sass deviates from the spec and supplies the version as a\n  // string rather than a number, so we use loose equality checking here.\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  sources = sources\n    .map(String)\n    // Some source maps produce relative source paths like \"./foo.js\" instead of\n    // \"foo.js\".  Normalize these first so that future comparisons will succeed.\n    // See bugzil.la/1090768.\n    .map(util.normalize)\n    // Always ensure that absolute sources are internally stored relative to\n    // the source root, if the source root is absolute. Not doing this would\n    // be particularly problematic when the source root is a prefix of the\n    // source (valid, but why??). See github issue #199 and bugzil.la/1188982.\n    .map(function (source) {\n      return sourceRoot && util.isAbsolute(sourceRoot) && util.isAbsolute(source)\n        ? util.relative(sourceRoot, source)\n        : source;\n    });\n\n  // Pass `true` below to allow duplicate names and sources. While source maps\n  // are intended to be compressed and deduplicated, the TypeScript compiler\n  // sometimes generates source maps with duplicates in them. See Github issue\n  // #72 and bugzil.la/889492.\n  this._names = ArraySet.fromArray(names.map(String), true);\n  this._sources = ArraySet.fromArray(sources, true);\n\n  this.sourceRoot = sourceRoot;\n  this.sourcesContent = sourcesContent;\n  this._mappings = mappings;\n  this.file = file;\n}\n\nBasicSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nBasicSourceMapConsumer.prototype.consumer = SourceMapConsumer;\n\n/**\n * Create a BasicSourceMapConsumer from a SourceMapGenerator.\n *\n * @param SourceMapGenerator aSourceMap\n *        The source map that will be consumed.\n * @returns BasicSourceMapConsumer\n */\nBasicSourceMapConsumer.fromSourceMap =\n  function SourceMapConsumer_fromSourceMap(aSourceMap) {\n    var smc = Object.create(BasicSourceMapConsumer.prototype);\n\n    var names = smc._names = ArraySet.fromArray(aSourceMap._names.toArray(), true);\n    var sources = smc._sources = ArraySet.fromArray(aSourceMap._sources.toArray(), true);\n    smc.sourceRoot = aSourceMap._sourceRoot;\n    smc.sourcesContent = aSourceMap._generateSourcesContent(smc._sources.toArray(),\n                                                            smc.sourceRoot);\n    smc.file = aSourceMap._file;\n\n    // Because we are modifying the entries (by converting string sources and\n    // names to indices into the sources and names ArraySets), we have to make\n    // a copy of the entry or else bad things happen. Shared mutable state\n    // strikes again! See github issue #191.\n\n    var generatedMappings = aSourceMap._mappings.toArray().slice();\n    var destGeneratedMappings = smc.__generatedMappings = [];\n    var destOriginalMappings = smc.__originalMappings = [];\n\n    for (var i = 0, length = generatedMappings.length; i < length; i++) {\n      var srcMapping = generatedMappings[i];\n      var destMapping = new Mapping;\n      destMapping.generatedLine = srcMapping.generatedLine;\n      destMapping.generatedColumn = srcMapping.generatedColumn;\n\n      if (srcMapping.source) {\n        destMapping.source = sources.indexOf(srcMapping.source);\n        destMapping.originalLine = srcMapping.originalLine;\n        destMapping.originalColumn = srcMapping.originalColumn;\n\n        if (srcMapping.name) {\n          destMapping.name = names.indexOf(srcMapping.name);\n        }\n\n        destOriginalMappings.push(destMapping);\n      }\n\n      destGeneratedMappings.push(destMapping);\n    }\n\n    quickSort(smc.__originalMappings, util.compareByOriginalPositions);\n\n    return smc;\n  };\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nBasicSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(BasicSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    return this._sources.toArray().map(function (s) {\n      return this.sourceRoot != null ? util.join(this.sourceRoot, s) : s;\n    }, this);\n  }\n});\n\n/**\n * Provide the JIT with a nice shape / hidden class.\n */\nfunction Mapping() {\n  this.generatedLine = 0;\n  this.generatedColumn = 0;\n  this.source = null;\n  this.originalLine = null;\n  this.originalColumn = null;\n  this.name = null;\n}\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nBasicSourceMapConsumer.prototype._parseMappings =\n  function SourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    var generatedLine = 1;\n    var previousGeneratedColumn = 0;\n    var previousOriginalLine = 0;\n    var previousOriginalColumn = 0;\n    var previousSource = 0;\n    var previousName = 0;\n    var length = aStr.length;\n    var index = 0;\n    var cachedSegments = {};\n    var temp = {};\n    var originalMappings = [];\n    var generatedMappings = [];\n    var mapping, str, segment, end, value;\n\n    while (index < length) {\n      if (aStr.charAt(index) === ';') {\n        generatedLine++;\n        index++;\n        previousGeneratedColumn = 0;\n      }\n      else if (aStr.charAt(index) === ',') {\n        index++;\n      }\n      else {\n        mapping = new Mapping();\n        mapping.generatedLine = generatedLine;\n\n        // Because each offset is encoded relative to the previous one,\n        // many segments often have the same encoding. We can exploit this\n        // fact by caching the parsed variable length fields of each segment,\n        // allowing us to avoid a second parse if we encounter the same\n        // segment again.\n        for (end = index; end < length; end++) {\n          if (this._charIsMappingSeparator(aStr, end)) {\n            break;\n          }\n        }\n        str = aStr.slice(index, end);\n\n        segment = cachedSegments[str];\n        if (segment) {\n          index += str.length;\n        } else {\n          segment = [];\n          while (index < end) {\n            base64VLQ.decode(aStr, index, temp);\n            value = temp.value;\n            index = temp.rest;\n            segment.push(value);\n          }\n\n          if (segment.length === 2) {\n            throw new Error('Found a source, but no line and column');\n          }\n\n          if (segment.length === 3) {\n            throw new Error('Found a source and line, but no column');\n          }\n\n          cachedSegments[str] = segment;\n        }\n\n        // Generated column.\n        mapping.generatedColumn = previousGeneratedColumn + segment[0];\n        previousGeneratedColumn = mapping.generatedColumn;\n\n        if (segment.length > 1) {\n          // Original source.\n          mapping.source = previousSource + segment[1];\n          previousSource += segment[1];\n\n          // Original line.\n          mapping.originalLine = previousOriginalLine + segment[2];\n          previousOriginalLine = mapping.originalLine;\n          // Lines are stored 0-based\n          mapping.originalLine += 1;\n\n          // Original column.\n          mapping.originalColumn = previousOriginalColumn + segment[3];\n          previousOriginalColumn = mapping.originalColumn;\n\n          if (segment.length > 4) {\n            // Original name.\n            mapping.name = previousName + segment[4];\n            previousName += segment[4];\n          }\n        }\n\n        generatedMappings.push(mapping);\n        if (typeof mapping.originalLine === 'number') {\n          originalMappings.push(mapping);\n        }\n      }\n    }\n\n    quickSort(generatedMappings, util.compareByGeneratedPositionsDeflated);\n    this.__generatedMappings = generatedMappings;\n\n    quickSort(originalMappings, util.compareByOriginalPositions);\n    this.__originalMappings = originalMappings;\n  };\n\n/**\n * Find the mapping that best matches the hypothetical \"needle\" mapping that\n * we are searching for in the given \"haystack\" of mappings.\n */\nBasicSourceMapConsumer.prototype._findMapping =\n  function SourceMapConsumer_findMapping(aNeedle, aMappings, aLineName,\n                                         aColumnName, aComparator, aBias) {\n    // To return the position we are searching for, we must first find the\n    // mapping for the given position and then return the opposite position it\n    // points to. Because the mappings are sorted, we can use binary search to\n    // find the best mapping.\n\n    if (aNeedle[aLineName] <= 0) {\n      throw new TypeError('Line must be greater than or equal to 1, got '\n                          + aNeedle[aLineName]);\n    }\n    if (aNeedle[aColumnName] < 0) {\n      throw new TypeError('Column must be greater than or equal to 0, got '\n                          + aNeedle[aColumnName]);\n    }\n\n    return binarySearch.search(aNeedle, aMappings, aComparator, aBias);\n  };\n\n/**\n * Compute the last column for each generated mapping. The last column is\n * inclusive.\n */\nBasicSourceMapConsumer.prototype.computeColumnSpans =\n  function SourceMapConsumer_computeColumnSpans() {\n    for (var index = 0; index < this._generatedMappings.length; ++index) {\n      var mapping = this._generatedMappings[index];\n\n      // Mappings do not contain a field for the last generated columnt. We\n      // can come up with an optimistic estimate, however, by assuming that\n      // mappings are contiguous (i.e. given two consecutive mappings, the\n      // first mapping ends where the second one starts).\n      if (index + 1 < this._generatedMappings.length) {\n        var nextMapping = this._generatedMappings[index + 1];\n\n        if (mapping.generatedLine === nextMapping.generatedLine) {\n          mapping.lastGeneratedColumn = nextMapping.generatedColumn - 1;\n          continue;\n        }\n      }\n\n      // The last mapping for each line spans the entire line.\n      mapping.lastGeneratedColumn = Infinity;\n    }\n  };\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.\n *   - column: The column number in the generated source.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.\n *   - column: The column number in the original source, or null.\n *   - name: The original identifier, or null.\n */\nBasicSourceMapConsumer.prototype.originalPositionFor =\n  function SourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._generatedMappings,\n      \"generatedLine\",\n      \"generatedColumn\",\n      util.compareByGeneratedPositionsDeflated,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._generatedMappings[index];\n\n      if (mapping.generatedLine === needle.generatedLine) {\n        var source = util.getArg(mapping, 'source', null);\n        if (source !== null) {\n          source = this._sources.at(source);\n          if (this.sourceRoot != null) {\n            source = util.join(this.sourceRoot, source);\n          }\n        }\n        var name = util.getArg(mapping, 'name', null);\n        if (name !== null) {\n          name = this._names.at(name);\n        }\n        return {\n          source: source,\n          line: util.getArg(mapping, 'originalLine', null),\n          column: util.getArg(mapping, 'originalColumn', null),\n          name: name\n        };\n      }\n    }\n\n    return {\n      source: null,\n      line: null,\n      column: null,\n      name: null\n    };\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nBasicSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function BasicSourceMapConsumer_hasContentsOfAllSources() {\n    if (!this.sourcesContent) {\n      return false;\n    }\n    return this.sourcesContent.length >= this._sources.size() &&\n      !this.sourcesContent.some(function (sc) { return sc == null; });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nBasicSourceMapConsumer.prototype.sourceContentFor =\n  function SourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    if (!this.sourcesContent) {\n      return null;\n    }\n\n    if (this.sourceRoot != null) {\n      aSource = util.relative(this.sourceRoot, aSource);\n    }\n\n    if (this._sources.has(aSource)) {\n      return this.sourcesContent[this._sources.indexOf(aSource)];\n    }\n\n    var url;\n    if (this.sourceRoot != null\n        && (url = util.urlParse(this.sourceRoot))) {\n      // XXX: file:// URIs and absolute paths lead to unexpected behavior for\n      // many users. We can help them out when they expect file:// URIs to\n      // behave like it would if they were running a local HTTP server. See\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=885597.\n      var fileUriAbsPath = aSource.replace(/^file:\\/\\//, \"\");\n      if (url.scheme == \"file\"\n          && this._sources.has(fileUriAbsPath)) {\n        return this.sourcesContent[this._sources.indexOf(fileUriAbsPath)]\n      }\n\n      if ((!url.path || url.path == \"/\")\n          && this._sources.has(\"/\" + aSource)) {\n        return this.sourcesContent[this._sources.indexOf(\"/\" + aSource)];\n      }\n    }\n\n    // This function is used recursively from\n    // IndexedSourceMapConsumer.prototype.sourceContentFor. In that case, we\n    // don't want to throw if we can't find the source - we just want to\n    // return null, so we provide a flag to exit gracefully.\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: The column number in the original source.\n *   - bias: Either 'SourceMapConsumer.GREATEST_LOWER_BOUND' or\n *     'SourceMapConsumer.LEAST_UPPER_BOUND'. Specifies whether to return the\n *     closest element that is smaller than or greater than the one we are\n *     searching for, respectively, if the exact element cannot be found.\n *     Defaults to 'SourceMapConsumer.GREATEST_LOWER_BOUND'.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nBasicSourceMapConsumer.prototype.generatedPositionFor =\n  function SourceMapConsumer_generatedPositionFor(aArgs) {\n    var source = util.getArg(aArgs, 'source');\n    if (this.sourceRoot != null) {\n      source = util.relative(this.sourceRoot, source);\n    }\n    if (!this._sources.has(source)) {\n      return {\n        line: null,\n        column: null,\n        lastColumn: null\n      };\n    }\n    source = this._sources.indexOf(source);\n\n    var needle = {\n      source: source,\n      originalLine: util.getArg(aArgs, 'line'),\n      originalColumn: util.getArg(aArgs, 'column')\n    };\n\n    var index = this._findMapping(\n      needle,\n      this._originalMappings,\n      \"originalLine\",\n      \"originalColumn\",\n      util.compareByOriginalPositions,\n      util.getArg(aArgs, 'bias', SourceMapConsumer.GREATEST_LOWER_BOUND)\n    );\n\n    if (index >= 0) {\n      var mapping = this._originalMappings[index];\n\n      if (mapping.source === needle.source) {\n        return {\n          line: util.getArg(mapping, 'generatedLine', null),\n          column: util.getArg(mapping, 'generatedColumn', null),\n          lastColumn: util.getArg(mapping, 'lastGeneratedColumn', null)\n        };\n      }\n    }\n\n    return {\n      line: null,\n      column: null,\n      lastColumn: null\n    };\n  };\n\nexports.BasicSourceMapConsumer = BasicSourceMapConsumer;\n\n/**\n * An IndexedSourceMapConsumer instance represents a parsed source map which\n * we can query for information. It differs from BasicSourceMapConsumer in\n * that it takes \"indexed\" source maps (i.e. ones with a \"sections\" field) as\n * input.\n *\n * The only parameter is a raw source map (either as a JSON string, or already\n * parsed to an object). According to the spec for indexed source maps, they\n * have the following attributes:\n *\n *   - version: Which version of the source map spec this map is following.\n *   - file: Optional. The generated file this source map is associated with.\n *   - sections: A list of section definitions.\n *\n * Each value under the \"sections\" field has two fields:\n *   - offset: The offset into the original specified at which this section\n *       begins to apply, defined as an object with a \"line\" and \"column\"\n *       field.\n *   - map: A source map definition. This source map could also be indexed,\n *       but doesn't have to be.\n *\n * Instead of the \"map\" field, it's also possible to have a \"url\" field\n * specifying a URL to retrieve a source map from, but that's currently\n * unsupported.\n *\n * Here's an example source map, taken from the source map spec[0], but\n * modified to omit a section which uses the \"url\" field.\n *\n *  {\n *    version : 3,\n *    file: \"app.js\",\n *    sections: [{\n *      offset: {line:100, column:10},\n *      map: {\n *        version : 3,\n *        file: \"section.js\",\n *        sources: [\"foo.js\", \"bar.js\"],\n *        names: [\"src\", \"maps\", \"are\", \"fun\"],\n *        mappings: \"AAAA,E;;ABCDE;\"\n *      }\n *    }],\n *  }\n *\n * [0]: https://docs.google.com/document/d/1U1RGAehQwRypUTovF1KRlpiOFze0b-_2gc6fAH0KY0k/edit#heading=h.535es3xeprgt\n */\nfunction IndexedSourceMapConsumer(aSourceMap) {\n  var sourceMap = aSourceMap;\n  if (typeof aSourceMap === 'string') {\n    sourceMap = JSON.parse(aSourceMap.replace(/^\\)\\]\\}'/, ''));\n  }\n\n  var version = util.getArg(sourceMap, 'version');\n  var sections = util.getArg(sourceMap, 'sections');\n\n  if (version != this._version) {\n    throw new Error('Unsupported version: ' + version);\n  }\n\n  this._sources = new ArraySet();\n  this._names = new ArraySet();\n\n  var lastOffset = {\n    line: -1,\n    column: 0\n  };\n  this._sections = sections.map(function (s) {\n    if (s.url) {\n      // The url field will require support for asynchronicity.\n      // See https://github.com/mozilla/source-map/issues/16\n      throw new Error('Support for url field in sections not implemented.');\n    }\n    var offset = util.getArg(s, 'offset');\n    var offsetLine = util.getArg(offset, 'line');\n    var offsetColumn = util.getArg(offset, 'column');\n\n    if (offsetLine < lastOffset.line ||\n        (offsetLine === lastOffset.line && offsetColumn < lastOffset.column)) {\n      throw new Error('Section offsets must be ordered and non-overlapping.');\n    }\n    lastOffset = offset;\n\n    return {\n      generatedOffset: {\n        // The offset fields are 0-based, but we use 1-based indices when\n        // encoding/decoding from VLQ.\n        generatedLine: offsetLine + 1,\n        generatedColumn: offsetColumn + 1\n      },\n      consumer: new SourceMapConsumer(util.getArg(s, 'map'))\n    }\n  });\n}\n\nIndexedSourceMapConsumer.prototype = Object.create(SourceMapConsumer.prototype);\nIndexedSourceMapConsumer.prototype.constructor = SourceMapConsumer;\n\n/**\n * The version of the source mapping spec that we are consuming.\n */\nIndexedSourceMapConsumer.prototype._version = 3;\n\n/**\n * The list of original sources.\n */\nObject.defineProperty(IndexedSourceMapConsumer.prototype, 'sources', {\n  get: function () {\n    var sources = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      for (var j = 0; j < this._sections[i].consumer.sources.length; j++) {\n        sources.push(this._sections[i].consumer.sources[j]);\n      }\n    }\n    return sources;\n  }\n});\n\n/**\n * Returns the original source, line, and column information for the generated\n * source's line and column positions provided. The only argument is an object\n * with the following properties:\n *\n *   - line: The line number in the generated source.\n *   - column: The column number in the generated source.\n *\n * and an object is returned with the following properties:\n *\n *   - source: The original source file, or null.\n *   - line: The line number in the original source, or null.\n *   - column: The column number in the original source, or null.\n *   - name: The original identifier, or null.\n */\nIndexedSourceMapConsumer.prototype.originalPositionFor =\n  function IndexedSourceMapConsumer_originalPositionFor(aArgs) {\n    var needle = {\n      generatedLine: util.getArg(aArgs, 'line'),\n      generatedColumn: util.getArg(aArgs, 'column')\n    };\n\n    // Find the section containing the generated position we're trying to map\n    // to an original position.\n    var sectionIndex = binarySearch.search(needle, this._sections,\n      function(needle, section) {\n        var cmp = needle.generatedLine - section.generatedOffset.generatedLine;\n        if (cmp) {\n          return cmp;\n        }\n\n        return (needle.generatedColumn -\n                section.generatedOffset.generatedColumn);\n      });\n    var section = this._sections[sectionIndex];\n\n    if (!section) {\n      return {\n        source: null,\n        line: null,\n        column: null,\n        name: null\n      };\n    }\n\n    return section.consumer.originalPositionFor({\n      line: needle.generatedLine -\n        (section.generatedOffset.generatedLine - 1),\n      column: needle.generatedColumn -\n        (section.generatedOffset.generatedLine === needle.generatedLine\n         ? section.generatedOffset.generatedColumn - 1\n         : 0),\n      bias: aArgs.bias\n    });\n  };\n\n/**\n * Return true if we have the source content for every source in the source\n * map, false otherwise.\n */\nIndexedSourceMapConsumer.prototype.hasContentsOfAllSources =\n  function IndexedSourceMapConsumer_hasContentsOfAllSources() {\n    return this._sections.every(function (s) {\n      return s.consumer.hasContentsOfAllSources();\n    });\n  };\n\n/**\n * Returns the original source content. The only argument is the url of the\n * original source file. Returns null if no original source content is\n * available.\n */\nIndexedSourceMapConsumer.prototype.sourceContentFor =\n  function IndexedSourceMapConsumer_sourceContentFor(aSource, nullOnMissing) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      var content = section.consumer.sourceContentFor(aSource, true);\n      if (content) {\n        return content;\n      }\n    }\n    if (nullOnMissing) {\n      return null;\n    }\n    else {\n      throw new Error('\"' + aSource + '\" is not in the SourceMap.');\n    }\n  };\n\n/**\n * Returns the generated line and column information for the original source,\n * line, and column positions provided. The only argument is an object with\n * the following properties:\n *\n *   - source: The filename of the original source.\n *   - line: The line number in the original source.\n *   - column: The column number in the original source.\n *\n * and an object is returned with the following properties:\n *\n *   - line: The line number in the generated source, or null.\n *   - column: The column number in the generated source, or null.\n */\nIndexedSourceMapConsumer.prototype.generatedPositionFor =\n  function IndexedSourceMapConsumer_generatedPositionFor(aArgs) {\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n\n      // Only consider this section if the requested source is in the list of\n      // sources of the consumer.\n      if (section.consumer.sources.indexOf(util.getArg(aArgs, 'source')) === -1) {\n        continue;\n      }\n      var generatedPosition = section.consumer.generatedPositionFor(aArgs);\n      if (generatedPosition) {\n        var ret = {\n          line: generatedPosition.line +\n            (section.generatedOffset.generatedLine - 1),\n          column: generatedPosition.column +\n            (section.generatedOffset.generatedLine === generatedPosition.line\n             ? section.generatedOffset.generatedColumn - 1\n             : 0)\n        };\n        return ret;\n      }\n    }\n\n    return {\n      line: null,\n      column: null\n    };\n  };\n\n/**\n * Parse the mappings in a string in to a data structure which we can easily\n * query (the ordered arrays in the `this.__generatedMappings` and\n * `this.__originalMappings` properties).\n */\nIndexedSourceMapConsumer.prototype._parseMappings =\n  function IndexedSourceMapConsumer_parseMappings(aStr, aSourceRoot) {\n    this.__generatedMappings = [];\n    this.__originalMappings = [];\n    for (var i = 0; i < this._sections.length; i++) {\n      var section = this._sections[i];\n      var sectionMappings = section.consumer._generatedMappings;\n      for (var j = 0; j < sectionMappings.length; j++) {\n        var mapping = sectionMappings[j];\n\n        var source = section.consumer._sources.at(mapping.source);\n        if (section.consumer.sourceRoot !== null) {\n          source = util.join(section.consumer.sourceRoot, source);\n        }\n        this._sources.add(source);\n        source = this._sources.indexOf(source);\n\n        var name = section.consumer._names.at(mapping.name);\n        this._names.add(name);\n        name = this._names.indexOf(name);\n\n        // The mappings coming from the consumer for the section have\n        // generated positions relative to the start of the section, so we\n        // need to offset them to be relative to the start of the concatenated\n        // generated file.\n        var adjustedMapping = {\n          source: source,\n          generatedLine: mapping.generatedLine +\n            (section.generatedOffset.generatedLine - 1),\n          generatedColumn: mapping.generatedColumn +\n            (section.generatedOffset.generatedLine === mapping.generatedLine\n            ? section.generatedOffset.generatedColumn - 1\n            : 0),\n          originalLine: mapping.originalLine,\n          originalColumn: mapping.originalColumn,\n          name: name\n        };\n\n        this.__generatedMappings.push(adjustedMapping);\n        if (typeof adjustedMapping.originalLine === 'number') {\n          this.__originalMappings.push(adjustedMapping);\n        }\n      }\n    }\n\n    quickSort(this.__generatedMappings, util.compareByGeneratedPositionsDeflated);\n    quickSort(this.__originalMappings, util.compareByOriginalPositions);\n  };\n\nexports.IndexedSourceMapConsumer = IndexedSourceMapConsumer;\n", "/* -*- Mode: js; js-indent-level: 2; -*- */\n/*\n * Copyright 2011 Mozilla Foundation and contributors\n * Licensed under the New BSD license. See LICENSE or:\n * http://opensource.org/licenses/BSD-3-Clause\n */\n\n/**\n * This is a helper function for getting values from parameter/options\n * objects.\n *\n * @param args The object we are extracting values from\n * @param name The name of the property we are getting.\n * @param defaultValue An optional value to return if the property is missing\n * from the object. If this is not specified and the property is missing, an\n * error will be thrown.\n */\nfunction getArg(aArgs, aName, aDefaultValue) {\n  if (aName in aArgs) {\n    return aArgs[aName];\n  } else if (arguments.length === 3) {\n    return aDefaultValue;\n  } else {\n    throw new Error('\"' + aName + '\" is a required argument.');\n  }\n}\nexports.getArg = getArg;\n\nvar urlRegexp = /^(?:([\\w+\\-.]+):)?\\/\\/(?:(\\w+:\\w+)@)?([\\w.]*)(?::(\\d+))?(\\S*)$/;\nvar dataUrlRegexp = /^data:.+\\,.+$/;\n\nfunction urlParse(aUrl) {\n  var match = aUrl.match(urlRegexp);\n  if (!match) {\n    return null;\n  }\n  return {\n    scheme: match[1],\n    auth: match[2],\n    host: match[3],\n    port: match[4],\n    path: match[5]\n  };\n}\nexports.urlParse = urlParse;\n\nfunction urlGenerate(aParsedUrl) {\n  var url = '';\n  if (aParsedUrl.scheme) {\n    url += aParsedUrl.scheme + ':';\n  }\n  url += '//';\n  if (aParsedUrl.auth) {\n    url += aParsedUrl.auth + '@';\n  }\n  if (aParsedUrl.host) {\n    url += aParsedUrl.host;\n  }\n  if (aParsedUrl.port) {\n    url += \":\" + aParsedUrl.port\n  }\n  if (aParsedUrl.path) {\n    url += aParsedUrl.path;\n  }\n  return url;\n}\nexports.urlGenerate = urlGenerate;\n\n/**\n * Normalizes a path, or the path portion of a URL:\n *\n * - Replaces consecutive slashes with one slash.\n * - Removes unnecessary '.' parts.\n * - Removes unnecessary '<dir>/..' parts.\n *\n * Based on code in the Node.js 'path' core module.\n *\n * @param aPath The path or url to normalize.\n */\nfunction normalize(aPath) {\n  var path = aPath;\n  var url = urlParse(aPath);\n  if (url) {\n    if (!url.path) {\n      return aPath;\n    }\n    path = url.path;\n  }\n  var isAbsolute = exports.isAbsolute(path);\n\n  var parts = path.split(/\\/+/);\n  for (var part, up = 0, i = parts.length - 1; i >= 0; i--) {\n    part = parts[i];\n    if (part === '.') {\n      parts.splice(i, 1);\n    } else if (part === '..') {\n      up++;\n    } else if (up > 0) {\n      if (part === '') {\n        // The first part is blank if the path is absolute. Trying to go\n        // above the root is a no-op. Therefore we can remove all '..' parts\n        // directly after the root.\n        parts.splice(i + 1, up);\n        up = 0;\n      } else {\n        parts.splice(i, 2);\n        up--;\n      }\n    }\n  }\n  path = parts.join('/');\n\n  if (path === '') {\n    path = isAbsolute ? '/' : '.';\n  }\n\n  if (url) {\n    url.path = path;\n    return urlGenerate(url);\n  }\n  return path;\n}\nexports.normalize = normalize;\n\n/**\n * Joins two paths/URLs.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be joined with the root.\n *\n * - If aPath is a URL or a data URI, aPath is returned, unless aPath is a\n *   scheme-relative URL: Then the scheme of aRoot, if any, is prepended\n *   first.\n * - Otherwise aPath is a path. If aRoot is a URL, then its path portion\n *   is updated with the result and aRoot is returned. Otherwise the result\n *   is returned.\n *   - If aPath is absolute, the result is aPath.\n *   - Otherwise the two paths are joined with a slash.\n * - Joining for example 'http://' and 'www.example.com' is also supported.\n */\nfunction join(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n  if (aPath === \"\") {\n    aPath = \".\";\n  }\n  var aPathUrl = urlParse(aPath);\n  var aRootUrl = urlParse(aRoot);\n  if (aRootUrl) {\n    aRoot = aRootUrl.path || '/';\n  }\n\n  // `join(foo, '//www.example.org')`\n  if (aPathUrl && !aPathUrl.scheme) {\n    if (aRootUrl) {\n      aPathUrl.scheme = aRootUrl.scheme;\n    }\n    return urlGenerate(aPathUrl);\n  }\n\n  if (aPathUrl || aPath.match(dataUrlRegexp)) {\n    return aPath;\n  }\n\n  // `join('http://', 'www.example.com')`\n  if (aRootUrl && !aRootUrl.host && !aRootUrl.path) {\n    aRootUrl.host = aPath;\n    return urlGenerate(aRootUrl);\n  }\n\n  var joined = aPath.charAt(0) === '/'\n    ? aPath\n    : normalize(aRoot.replace(/\\/+$/, '') + '/' + aPath);\n\n  if (aRootUrl) {\n    aRootUrl.path = joined;\n    return urlGenerate(aRootUrl);\n  }\n  return joined;\n}\nexports.join = join;\n\nexports.isAbsolute = function (aPath) {\n  return aPath.charAt(0) === '/' || !!aPath.match(urlRegexp);\n};\n\n/**\n * Make a path relative to a URL or another path.\n *\n * @param aRoot The root path or URL.\n * @param aPath The path or URL to be made relative to aRoot.\n */\nfunction relative(aRoot, aPath) {\n  if (aRoot === \"\") {\n    aRoot = \".\";\n  }\n\n  aRoot = aRoot.replace(/\\/$/, '');\n\n  // It is possible for the path to be above the root. In this case, simply\n  // checking whether the root is a prefix of the path won't work. Instead, we\n  // need to remove components from the root one by one, until either we find\n  // a prefix that fits, or we run out of components to remove.\n  var level = 0;\n  while (aPath.indexOf(aRoot + '/') !== 0) {\n    var index = aRoot.lastIndexOf(\"/\");\n    if (index < 0) {\n      return aPath;\n    }\n\n    // If the only part of the root that is left is the scheme (i.e. http://,\n    // file:///, etc.), one or more slashes (/), or simply nothing at all, we\n    // have exhausted all components, so the path is not relative to the root.\n    aRoot = aRoot.slice(0, index);\n    if (aRoot.match(/^([^\\/]+:\\/)?\\/*$/)) {\n      return aPath;\n    }\n\n    ++level;\n  }\n\n  // Make sure we add a \"../\" for each component we removed from the root.\n  return Array(level + 1).join(\"../\") + aPath.substr(aRoot.length + 1);\n}\nexports.relative = relative;\n\nvar supportsNullProto = (function () {\n  var obj = Object.create(null);\n  return !('__proto__' in obj);\n}());\n\nfunction identity (s) {\n  return s;\n}\n\n/**\n * Because behavior goes wacky when you set `__proto__` on objects, we\n * have to prefix all the strings in our set with an arbitrary character.\n *\n * See https://github.com/mozilla/source-map/pull/31 and\n * https://github.com/mozilla/source-map/issues/30\n *\n * @param String aStr\n */\nfunction toSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return '$' + aStr;\n  }\n\n  return aStr;\n}\nexports.toSetString = supportsNullProto ? identity : toSetString;\n\nfunction fromSetString(aStr) {\n  if (isProtoString(aStr)) {\n    return aStr.slice(1);\n  }\n\n  return aStr;\n}\nexports.fromSetString = supportsNullProto ? identity : fromSetString;\n\nfunction isProtoString(s) {\n  if (!s) {\n    return false;\n  }\n\n  var length = s.length;\n\n  if (length < 9 /* \"__proto__\".length */) {\n    return false;\n  }\n\n  if (s.charCodeAt(length - 1) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 2) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 3) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 4) !== 116 /* 't' */ ||\n      s.charCodeAt(length - 5) !== 111 /* 'o' */ ||\n      s.charCodeAt(length - 6) !== 114 /* 'r' */ ||\n      s.charCodeAt(length - 7) !== 112 /* 'p' */ ||\n      s.charCodeAt(length - 8) !== 95  /* '_' */ ||\n      s.charCodeAt(length - 9) !== 95  /* '_' */) {\n    return false;\n  }\n\n  for (var i = length - 10; i >= 0; i--) {\n    if (s.charCodeAt(i) !== 36 /* '$' */) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Comparator between two mappings where the original positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same original source/line/column, but different generated\n * line and column the same. Useful when searching for a mapping with a\n * stubbed out mapping.\n */\nfunction compareByOriginalPositions(mappingA, mappingB, onlyCompareOriginal) {\n  var cmp = mappingA.source - mappingB.source;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0 || onlyCompareOriginal) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return mappingA.name - mappingB.name;\n}\nexports.compareByOriginalPositions = compareByOriginalPositions;\n\n/**\n * Comparator between two mappings with deflated source and name indices where\n * the generated positions are compared.\n *\n * Optionally pass in `true` as `onlyCompareGenerated` to consider two\n * mappings with the same generated line and column, but different\n * source/name/original line and column the same. Useful when searching for a\n * mapping with a stubbed out mapping.\n */\nfunction compareByGeneratedPositionsDeflated(mappingA, mappingB, onlyCompareGenerated) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0 || onlyCompareGenerated) {\n    return cmp;\n  }\n\n  cmp = mappingA.source - mappingB.source;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return mappingA.name - mappingB.name;\n}\nexports.compareByGeneratedPositionsDeflated = compareByGeneratedPositionsDeflated;\n\nfunction strcmp(aStr1, aStr2) {\n  if (aStr1 === aStr2) {\n    return 0;\n  }\n\n  if (aStr1 > aStr2) {\n    return 1;\n  }\n\n  return -1;\n}\n\n/**\n * Comparator between two mappings with inflated source and name strings where\n * the generated positions are compared.\n */\nfunction compareByGeneratedPositionsInflated(mappingA, mappingB) {\n  var cmp = mappingA.generatedLine - mappingB.generatedLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.generatedColumn - mappingB.generatedColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = strcmp(mappingA.source, mappingB.source);\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalLine - mappingB.originalLine;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  cmp = mappingA.originalColumn - mappingB.originalColumn;\n  if (cmp !== 0) {\n    return cmp;\n  }\n\n  return strcmp(mappingA.name, mappingB.name);\n}\nexports.compareByGeneratedPositionsInflated = compareByGeneratedPositionsInflated;\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stack-generator', ['stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('stackframe'));\n    } else {\n        root.StackGenerator = factory(root.StackFrame);\n    }\n}(this, function(StackFrame) {\n    return {\n        backtrace: function StackGenerator$$backtrace(opts) {\n            var stack = [];\n            var maxStackSize = 10;\n\n            if (typeof opts === 'object' && typeof opts.maxStackSize === 'number') {\n                maxStackSize = opts.maxStackSize;\n            }\n\n            var curr = arguments.callee;\n            while (curr && stack.length < maxStackSize && curr['arguments']) {\n                // Allow V8 optimizations\n                var args = new Array(curr['arguments'].length);\n                for (var i = 0; i < args.length; ++i) {\n                    args[i] = curr['arguments'][i];\n                }\n                if (/function(?:\\s+([\\w$]+))+\\s*\\(/.test(curr.toString())) {\n                    stack.push(new StackFrame({functionName: RegExp.$1 || undefined, args: args}));\n                } else {\n                    stack.push(new StackFrame({args: args}));\n                }\n\n                try {\n                    curr = curr.caller;\n                } catch (e) {\n                    break;\n                }\n            }\n            return stack;\n        }\n    };\n}));\n", "(function(root, factory) {\n    'use strict';\n    // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js, Rhino, and browsers.\n\n    /* istanbul ignore next */\n    if (typeof define === 'function' && define.amd) {\n        define('stacktrace-gps', ['source-map', 'stackframe'], factory);\n    } else if (typeof exports === 'object') {\n        module.exports = factory(require('source-map/lib/source-map-consumer'), require('stackframe'));\n    } else {\n        root.StackTraceGPS = factory(root.SourceMap || root.sourceMap, root.StackFrame);\n    }\n}(this, function(SourceMap, StackFrame) {\n    'use strict';\n\n    /**\n     * Make a X-Domain request to url and callback.\n     *\n     * @param {String} url\n     * @returns {Promise} with response text if fulfilled\n     */\n    function _xdr(url) {\n        return new Promise(function(resolve, reject) {\n            var req = new XMLHttpRequest();\n            req.open('get', url);\n            req.onerror = reject;\n            req.onreadystatechange = function onreadystatechange() {\n                if (req.readyState === 4) {\n                    if ((req.status >= 200 && req.status < 300) ||\n                        (url.substr(0, 7) === 'file://' && req.responseText)) {\n                        resolve(req.responseText);\n                    } else {\n                        reject(new Error('HTTP status: ' + req.status + ' retrieving ' + url));\n                    }\n                }\n            };\n            req.send();\n        });\n\n    }\n\n    /**\n     * Convert a Base64-encoded string into its original representation.\n     * Used for inline sourcemaps.\n     *\n     * @param {String} b64str Base-64 encoded string\n     * @returns {String} original representation of the base64-encoded string.\n     */\n    function _atob(b64str) {\n        if (typeof window !== 'undefined' && window.atob) {\n            return window.atob(b64str);\n        } else {\n            throw new Error('You must supply a polyfill for window.atob in this environment');\n        }\n    }\n\n    function _parseJson(string) {\n        if (typeof JSON !== 'undefined' && JSON.parse) {\n            return JSON.parse(string);\n        } else {\n            throw new Error('You must supply a polyfill for JSON.parse in this environment');\n        }\n    }\n\n    function _findFunctionName(source, lineNumber/*, columnNumber*/) {\n        var syntaxes = [\n            // {name} = function ({args}) TODO args capture\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*function\\b/,\n            // function {name}({args}) m[1]=name m[2]=args\n            /function\\s+([^('\"`]*?)\\s*\\(([^)]*)\\)/,\n            // {name} = eval()\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*(?:eval|new Function)\\b/,\n            // fn_name() {\n            /\\b(?!(?:if|for|switch|while|with|catch)\\b)(?:(?:static)\\s+)?(\\S+)\\s*\\(.*?\\)\\s*\\{/,\n            // {name} = () => {\n            /['\"]?([$_A-Za-z][$_A-Za-z0-9]*)['\"]?\\s*[:=]\\s*\\(.*?\\)\\s*=>/\n        ];\n        var lines = source.split('\\n');\n\n        // Walk backwards in the source lines until we find the line which matches one of the patterns above\n        var code = '';\n        var maxLines = Math.min(lineNumber, 20);\n        for (var i = 0; i < maxLines; ++i) {\n            // lineNo is 1-based, source[] is 0-based\n            var line = lines[lineNumber - i - 1];\n            var commentPos = line.indexOf('//');\n            if (commentPos >= 0) {\n                line = line.substr(0, commentPos);\n            }\n\n            if (line) {\n                code = line + code;\n                var len = syntaxes.length;\n                for (var index = 0; index < len; index++) {\n                    var m = syntaxes[index].exec(code);\n                    if (m && m[1]) {\n                        return m[1];\n                    }\n                }\n            }\n        }\n        return undefined;\n    }\n\n    function _ensureSupportedEnvironment() {\n        if (typeof Object.defineProperty !== 'function' || typeof Object.create !== 'function') {\n            throw new Error('Unable to consume source maps in older browsers');\n        }\n    }\n\n    function _ensureStackFrameIsLegit(stackframe) {\n        if (typeof stackframe !== 'object') {\n            throw new TypeError('Given StackFrame is not an object');\n        } else if (typeof stackframe.fileName !== 'string') {\n            throw new TypeError('Given file name is not a String');\n        } else if (typeof stackframe.lineNumber !== 'number' ||\n            stackframe.lineNumber % 1 !== 0 ||\n            stackframe.lineNumber < 1) {\n            throw new TypeError('Given line number must be a positive integer');\n        } else if (typeof stackframe.columnNumber !== 'number' ||\n            stackframe.columnNumber % 1 !== 0 ||\n            stackframe.columnNumber < 0) {\n            throw new TypeError('Given column number must be a non-negative integer');\n        }\n        return true;\n    }\n\n    function _findSourceMappingURL(source) {\n        var sourceMappingUrlRegExp = /\\/\\/[#@] ?sourceMappingURL=([^\\s'\"]+)\\s*$/mg;\n        var lastSourceMappingUrl;\n        var matchSourceMappingUrl;\n        // eslint-disable-next-line no-cond-assign\n        while (matchSourceMappingUrl = sourceMappingUrlRegExp.exec(source)) {\n            lastSourceMappingUrl = matchSourceMappingUrl[1];\n        }\n        if (lastSourceMappingUrl) {\n            return lastSourceMappingUrl;\n        } else {\n            throw new Error('sourceMappingURL not found');\n        }\n    }\n\n    function _extractLocationInfoFromSourceMapSource(stackframe, sourceMapConsumer, sourceCache) {\n        return new Promise(function(resolve, reject) {\n            var loc = sourceMapConsumer.originalPositionFor({\n                line: stackframe.lineNumber,\n                column: stackframe.columnNumber\n            });\n\n            if (loc.source) {\n                // cache mapped sources\n                var mappedSource = sourceMapConsumer.sourceContentFor(loc.source);\n                if (mappedSource) {\n                    sourceCache[loc.source] = mappedSource;\n                }\n\n                resolve(\n                    // given stackframe and source location, update stackframe\n                    new StackFrame({\n                        functionName: loc.name || stackframe.functionName,\n                        args: stackframe.args,\n                        fileName: loc.source,\n                        lineNumber: loc.line,\n                        columnNumber: loc.column\n                    }));\n            } else {\n                reject(new Error('Could not get original source for given stackframe and source map'));\n            }\n        });\n    }\n\n    /**\n     * @constructor\n     * @param {Object} opts\n     *      opts.sourceCache = {url: \"Source String\"} => preload source cache\n     *      opts.sourceMapConsumerCache = {/path/file.js.map: SourceMapConsumer}\n     *      opts.offline = True to prevent network requests.\n     *              Best effort without sources or source maps.\n     *      opts.ajax = Promise returning function to make X-Domain requests\n     */\n    return function StackTraceGPS(opts) {\n        if (!(this instanceof StackTraceGPS)) {\n            return new StackTraceGPS(opts);\n        }\n        opts = opts || {};\n\n        this.sourceCache = opts.sourceCache || {};\n        this.sourceMapConsumerCache = opts.sourceMapConsumerCache || {};\n\n        this.ajax = opts.ajax || _xdr;\n\n        this._atob = opts.atob || _atob;\n\n        this._get = function _get(location) {\n            return new Promise(function(resolve, reject) {\n                var isDataUrl = location.substr(0, 5) === 'data:';\n                if (this.sourceCache[location]) {\n                    resolve(this.sourceCache[location]);\n                } else if (opts.offline && !isDataUrl) {\n                    reject(new Error('Cannot make network requests in offline mode'));\n                } else {\n                    if (isDataUrl) {\n                        // data URLs can have parameters.\n                        // see http://tools.ietf.org/html/rfc2397\n                        var supportedEncodingRegexp =\n                            /^data:application\\/json;([\\w=:\"-]+;)*base64,/;\n                        var match = location.match(supportedEncodingRegexp);\n                        if (match) {\n                            var sourceMapStart = match[0].length;\n                            var encodedSource = location.substr(sourceMapStart);\n                            var source = this._atob(encodedSource);\n                            this.sourceCache[location] = source;\n                            resolve(source);\n                        } else {\n                            reject(new Error('The encoding of the inline sourcemap is not supported'));\n                        }\n                    } else {\n                        var xhrPromise = this.ajax(location, {method: 'get'});\n                        // Cache the Promise to prevent duplicate in-flight requests\n                        this.sourceCache[location] = xhrPromise;\n                        xhrPromise.then(resolve, reject);\n                    }\n                }\n            }.bind(this));\n        };\n\n        /**\n         * Creating SourceMapConsumers is expensive, so this wraps the creation of a\n         * SourceMapConsumer in a per-instance cache.\n         *\n         * @param {String} sourceMappingURL = URL to fetch source map from\n         * @param {String} defaultSourceRoot = Default source root for source map if undefined\n         * @returns {Promise} that resolves a SourceMapConsumer\n         */\n        this._getSourceMapConsumer = function _getSourceMapConsumer(sourceMappingURL, defaultSourceRoot) {\n            return new Promise(function(resolve) {\n                if (this.sourceMapConsumerCache[sourceMappingURL]) {\n                    resolve(this.sourceMapConsumerCache[sourceMappingURL]);\n                } else {\n                    var sourceMapConsumerPromise = new Promise(function(resolve, reject) {\n                        return this._get(sourceMappingURL).then(function(sourceMapSource) {\n                            if (typeof sourceMapSource === 'string') {\n                                sourceMapSource = _parseJson(sourceMapSource.replace(/^\\)\\]\\}'/, ''));\n                            }\n                            if (typeof sourceMapSource.sourceRoot === 'undefined') {\n                                sourceMapSource.sourceRoot = defaultSourceRoot;\n                            }\n\n                            resolve(new SourceMap.SourceMapConsumer(sourceMapSource));\n                        }, reject);\n                    }.bind(this));\n                    this.sourceMapConsumerCache[sourceMappingURL] = sourceMapConsumerPromise;\n                    resolve(sourceMapConsumerPromise);\n                }\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, enhance function name and use source maps for a\n         * better StackFrame.\n         *\n         * @param {StackFrame} stackframe object\n         * @returns {Promise} that resolves with with source-mapped StackFrame\n         */\n        this.pinpoint = function StackTraceGPS$$pinpoint(stackframe) {\n            return new Promise(function(resolve, reject) {\n                this.getMappedLocation(stackframe).then(function(mappedStackFrame) {\n                    function resolveMappedStackFrame() {\n                        resolve(mappedStackFrame);\n                    }\n\n                    this.findFunctionName(mappedStackFrame)\n                        .then(resolve, resolveMappedStackFrame)\n                        // eslint-disable-next-line no-unexpected-multiline\n                        ['catch'](resolveMappedStackFrame);\n                }.bind(this), reject);\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, guess function name from location information.\n         *\n         * @param {StackFrame} stackframe\n         * @returns {Promise} that resolves with enhanced StackFrame.\n         */\n        this.findFunctionName = function StackTraceGPS$$findFunctionName(stackframe) {\n            return new Promise(function(resolve, reject) {\n                _ensureStackFrameIsLegit(stackframe);\n                this._get(stackframe.fileName).then(function getSourceCallback(source) {\n                    var lineNumber = stackframe.lineNumber;\n                    var columnNumber = stackframe.columnNumber;\n                    var guessedFunctionName = _findFunctionName(source, lineNumber, columnNumber);\n                    // Only replace functionName if we found something\n                    if (guessedFunctionName) {\n                        resolve(new StackFrame({\n                            functionName: guessedFunctionName,\n                            args: stackframe.args,\n                            fileName: stackframe.fileName,\n                            lineNumber: lineNumber,\n                            columnNumber: columnNumber\n                        }));\n                    } else {\n                        resolve(stackframe);\n                    }\n                }, reject)['catch'](reject);\n            }.bind(this));\n        };\n\n        /**\n         * Given a StackFrame, seek source-mapped location and return new enhanced StackFrame.\n         *\n         * @param {StackFrame} stackframe\n         * @returns {Promise} that resolves with enhanced StackFrame.\n         */\n        this.getMappedLocation = function StackTraceGPS$$getMappedLocation(stackframe) {\n            return new Promise(function(resolve, reject) {\n                _ensureSupportedEnvironment();\n                _ensureStackFrameIsLegit(stackframe);\n\n                var sourceCache = this.sourceCache;\n                var fileName = stackframe.fileName;\n                this._get(fileName).then(function(source) {\n                    var sourceMappingURL = _findSourceMappingURL(source);\n                    var isDataUrl = sourceMappingURL.substr(0, 5) === 'data:';\n                    var defaultSourceRoot = fileName.substring(0, fileName.lastIndexOf('/') + 1);\n\n                    if (sourceMappingURL[0] !== '/' && !isDataUrl && !(/^https?:\\/\\/|^\\/\\//i).test(sourceMappingURL)) {\n                        sourceMappingURL = defaultSourceRoot + sourceMappingURL;\n                    }\n\n                    return this._getSourceMapConsumer(sourceMappingURL, defaultSourceRoot)\n                        .then(function(sourceMapConsumer) {\n                            return _extractLocationInfoFromSourceMapSource(stackframe, sourceMapConsumer, sourceCache)\n                                .then(resolve)['catch'](function() {\n                                    resolve(stackframe);\n                                });\n                        });\n                }.bind(this), reject)['catch'](reject);\n            }.bind(this));\n        };\n    };\n}));\n"]}